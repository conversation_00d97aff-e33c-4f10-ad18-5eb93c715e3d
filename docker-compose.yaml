version: '3.8'

services:
  app:
    image: wxsatellite/php:8.2-fpm
    container_name: running-man
    working_dir: /var/www
    volumes:
      - ./:/var/www/running-man
    networks:
      - app-network

  nginx:
    image: nginx:latest
    container_name: nginx
    volumes:
      - ./:/var/www/running-man
      - ./nginx.conf:/etc/nginx/conf.d/running-man.conf
    ports:
      - "80:80"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
