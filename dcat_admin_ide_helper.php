<?php

/**
 * A helper file for Dcat Admin, to provide autocomplete information to your IDE
 *
 * This file should not be included in your code, only analyzed by your IDE!
 *
 * <AUTHOR> <<EMAIL>>
 */
namespace Dcat\Admin {
    use Illuminate\Support\Collection;

    /**
     * @property Grid\Column|Collection id
     * @property Grid\Column|Collection order
     * @property Grid\Column|Collection created_at
     * @property Grid\Column|Collection day
     * @property Grid\Column|Collection end_time
     * @property Grid\Column|Collection image
     * @property Grid\Column|Collection name
     * @property Grid\Column|Collection start_time
     * @property Grid\Column|Collection status
     * @property Grid\Column|Collection time_type
     * @property Grid\Column|Collection type
     * @property Grid\Column|Collection updated_at
     * @property Grid\Column|Collection activity_id
     * @property Grid\Column|Collection child_id
     * @property Grid\Column|Collection order_limit
     * @property Grid\Column|Collection pre_reward_amount
     * @property Grid\Column|Collection reward_amount
     * @property Grid\Column|Collection settle_status
     * @property Grid\Column|Collection settle_time
     * @property Grid\Column|Collection user_id
     * @property Grid\Column|Collection end
     * @property Grid\Column|Collection order_count
     * @property Grid\Column|Collection reward
     * @property Grid\Column|Collection start
     * @property Grid\Column|Collection detail
     * @property Grid\Column|Collection version
     * @property Grid\Column|Collection is_enabled
     * @property Grid\Column|Collection extension
     * @property Grid\Column|Collection icon
     * @property Grid\Column|Collection parent_id
     * @property Grid\Column|Collection uri
     * @property Grid\Column|Collection menu_id
     * @property Grid\Column|Collection permission_id
     * @property Grid\Column|Collection http_method
     * @property Grid\Column|Collection http_path
     * @property Grid\Column|Collection slug
     * @property Grid\Column|Collection role_id
     * @property Grid\Column|Collection value
     * @property Grid\Column|Collection site_id
     * @property Grid\Column|Collection avatar
     * @property Grid\Column|Collection password
     * @property Grid\Column|Collection remember_token
     * @property Grid\Column|Collection username
     * @property Grid\Column|Collection cover
     * @property Grid\Column|Collection sort
     * @property Grid\Column|Collection target
     * @property Grid\Column|Collection target_type
     * @property Grid\Column|Collection content
     * @property Grid\Column|Collection expired_at
     * @property Grid\Column|Collection ip
     * @property Grid\Column|Collection reason
     * @property Grid\Column|Collection quantity
     * @property Grid\Column|Collection shop_id
     * @property Grid\Column|Collection spu_id
     * @property Grid\Column|Collection pic
     * @property Grid\Column|Collection pid
     * @property Grid\Column|Collection sub_title
     * @property Grid\Column|Collection view_order
     * @property Grid\Column|Collection is_open
     * @property Grid\Column|Collection latitude
     * @property Grid\Column|Collection longitude
     * @property Grid\Column|Collection region_id
     * @property Grid\Column|Collection address
     * @property Grid\Column|Collection city
     * @property Grid\Column|Collection company
     * @property Grid\Column|Collection district
     * @property Grid\Column|Collection phone
     * @property Grid\Column|Collection province
     * @property Grid\Column|Collection discount_price
     * @property Grid\Column|Collection rules
     * @property Grid\Column|Collection sales
     * @property Grid\Column|Collection start_price
     * @property Grid\Column|Collection stock
     * @property Grid\Column|Collection validity
     * @property Grid\Column|Collection closed
     * @property Grid\Column|Collection operator_id
     * @property Grid\Column|Collection operator_name
     * @property Grid\Column|Collection order_id
     * @property Grid\Column|Collection remark
     * @property Grid\Column|Collection rider_id
     * @property Grid\Column|Collection card_no
     * @property Grid\Column|Collection create_time
     * @property Grid\Column|Collection exp_time_type
     * @property Grid\Column|Collection obverse
     * @property Grid\Column|Collection vehicle_type
     * @property Grid\Column|Collection car_no
     * @property Grid\Column|Collection actual_amount
     * @property Grid\Column|Collection deleted_at
     * @property Grid\Column|Collection order_amount
     * @property Grid\Column|Collection order_no
     * @property Grid\Column|Collection paid_at
     * @property Grid\Column|Collection pay_method
     * @property Grid\Column|Collection payment_no
     * @property Grid\Column|Collection refund_at
     * @property Grid\Column|Collection refund_no
     * @property Grid\Column|Collection refund_status
     * @property Grid\Column|Collection connection
     * @property Grid\Column|Collection exception
     * @property Grid\Column|Collection failed_at
     * @property Grid\Column|Collection payload
     * @property Grid\Column|Collection queue
     * @property Grid\Column|Collection uuid
     * @property Grid\Column|Collection check_time
     * @property Grid\Column|Collection images
     * @property Grid\Column|Collection temperature
     * @property Grid\Column|Collection ym_count
     * @property Grid\Column|Collection check_img
     * @property Grid\Column|Collection path
     * @property Grid\Column|Collection belong_id
     * @property Grid\Column|Collection belong_type
     * @property Grid\Column|Collection lat
     * @property Grid\Column|Collection lng
     * @property Grid\Column|Collection time
     * @property Grid\Column|Collection platform
     * @property Grid\Column|Collection is_important
     * @property Grid\Column|Collection is_settled
     * @property Grid\Column|Collection notify_id
     * @property Grid\Column|Collection app_key
     * @property Grid\Column|Collection appointment_end_time
     * @property Grid\Column|Collection appointment_start_time
     * @property Grid\Column|Collection arrive_at
     * @property Grid\Column|Collection buy_imgs
     * @property Grid\Column|Collection category_id
     * @property Grid\Column|Collection close_reason
     * @property Grid\Column|Collection coupon_amount
     * @property Grid\Column|Collection coupon_id
     * @property Grid\Column|Collection deliver_address
     * @property Grid\Column|Collection deliver_address_id
     * @property Grid\Column|Collection deliver_lat
     * @property Grid\Column|Collection deliver_lng
     * @property Grid\Column|Collection deliver_name
     * @property Grid\Column|Collection deliver_phone
     * @property Grid\Column|Collection deliver_region_id
     * @property Grid\Column|Collection dispatch_status
     * @property Grid\Column|Collection distance
     * @property Grid\Column|Collection distance_price
     * @property Grid\Column|Collection estimated_delivery_time
     * @property Grid\Column|Collection finish_time
     * @property Grid\Column|Collection freight
     * @property Grid\Column|Collection goods_category_id
     * @property Grid\Column|Collection goods_desc
     * @property Grid\Column|Collection goods_imgs
     * @property Grid\Column|Collection goods_price
     * @property Grid\Column|Collection goods_protected_price
     * @property Grid\Column|Collection gratuity
     * @property Grid\Column|Collection hide_address
     * @property Grid\Column|Collection is_special
     * @property Grid\Column|Collection is_trans
     * @property Grid\Column|Collection need_incubator
     * @property Grid\Column|Collection order_status
     * @property Grid\Column|Collection ori_rider_id
     * @property Grid\Column|Collection out_order_no
     * @property Grid\Column|Collection pickup_address
     * @property Grid\Column|Collection pickup_address_id
     * @property Grid\Column|Collection pickup_at
     * @property Grid\Column|Collection pickup_code
     * @property Grid\Column|Collection pickup_code_mode
     * @property Grid\Column|Collection pickup_lat
     * @property Grid\Column|Collection pickup_lng
     * @property Grid\Column|Collection pickup_name
     * @property Grid\Column|Collection pickup_phone
     * @property Grid\Column|Collection pickup_region_id
     * @property Grid\Column|Collection receipt_time
     * @property Grid\Column|Collection receive_code
     * @property Grid\Column|Collection receive_code_mode
     * @property Grid\Column|Collection refund_amount
     * @property Grid\Column|Collection reward_amount_full
     * @property Grid\Column|Collection reward_amount_part
     * @property Grid\Column|Collection time_price
     * @property Grid\Column|Collection volume
     * @property Grid\Column|Collection weather_price
     * @property Grid\Column|Collection weight
     * @property Grid\Column|Collection weight_price
     * @property Grid\Column|Collection address_id
     * @property Grid\Column|Collection app_secret
     * @property Grid\Column|Collection callback_url
     * @property Grid\Column|Collection price
     * @property Grid\Column|Collection spu_cover
     * @property Grid\Column|Collection spu_name
     * @property Grid\Column|Collection cdgf_star
     * @property Grid\Column|Collection hpwh_star
     * @property Grid\Column|Collection imgs
     * @property Grid\Column|Collection is_anonymous
     * @property Grid\Column|Collection is_satisfied
     * @property Grid\Column|Collection lmrq_star
     * @property Grid\Column|Collection pszs_star
     * @property Grid\Column|Collection ybzj_star
     * @property Grid\Column|Collection amount
     * @property Grid\Column|Collection no
     * @property Grid\Column|Collection refund_pics
     * @property Grid\Column|Collection refund_reason
     * @property Grid\Column|Collection refund_remark
     * @property Grid\Column|Collection reject_reason
     * @property Grid\Column|Collection spu_info
     * @property Grid\Column|Collection verify_at
     * @property Grid\Column|Collection is_tel_protect
     * @property Grid\Column|Collection pay_amount
     * @property Grid\Column|Collection receiver_address
     * @property Grid\Column|Collection receiver_area
     * @property Grid\Column|Collection receiver_name
     * @property Grid\Column|Collection receiver_tel
     * @property Grid\Column|Collection reduce_amount
     * @property Grid\Column|Collection refund_type
     * @property Grid\Column|Collection sending_end_time
     * @property Grid\Column|Collection sending_start_time
     * @property Grid\Column|Collection email
     * @property Grid\Column|Collection token
     * @property Grid\Column|Collection abilities
     * @property Grid\Column|Collection expires_at
     * @property Grid\Column|Collection last_used_at
     * @property Grid\Column|Collection tokenable_id
     * @property Grid\Column|Collection tokenable_type
     * @property Grid\Column|Collection expire_time
     * @property Grid\Column|Collection phone_a
     * @property Grid\Column|Collection phone_x
     * @property Grid\Column|Collection subsid
     * @property Grid\Column|Collection purchase_time
     * @property Grid\Column|Collection region
     * @property Grid\Column|Collection vendor
     * @property Grid\Column|Collection base_price
     * @property Grid\Column|Collection period_price
     * @property Grid\Column|Collection code
     * @property Grid\Column|Collection is_hot
     * @property Grid\Column|Collection letter
     * @property Grid\Column|Collection level
     * @property Grid\Column|Collection pinyin
     * @property Grid\Column|Collection citycode
     * @property Grid\Column|Collection dp_id
     * @property Grid\Column|Collection is_show
     * @property Grid\Column|Collection mername
     * @property Grid\Column|Collection mt_id
     * @property Grid\Column|Collection qyh_id
     * @property Grid\Column|Collection sname
     * @property Grid\Column|Collection st_id
     * @property Grid\Column|Collection ww_id
     * @property Grid\Column|Collection yzcode
     * @property Grid\Column|Collection after_amount
     * @property Grid\Column|Collection before_amount
     * @property Grid\Column|Collection business_type
     * @property Grid\Column|Collection chg_amount
     * @property Grid\Column|Collection user_account_id
     * @property Grid\Column|Collection account_type
     * @property Grid\Column|Collection card_background
     * @property Grid\Column|Collection max_score
     * @property Grid\Column|Collection trans_limit
     * @property Grid\Column|Collection children_count
     * @property Grid\Column|Collection edu_background
     * @property Grid\Column|Collection emergency_contact
     * @property Grid\Column|Collection emergency_mobile
     * @property Grid\Column|Collection health_cert_cover
     * @property Grid\Column|Collection health_cert_expire_date
     * @property Grid\Column|Collection health_cert_no
     * @property Grid\Column|Collection health_cert_obverse
     * @property Grid\Column|Collection health_cert_publisher
     * @property Grid\Column|Collection job
     * @property Grid\Column|Collection marriage
     * @property Grid\Column|Collection transport_type
     * @property Grid\Column|Collection point_key
     * @property Grid\Column|Collection point_type
     * @property Grid\Column|Collection score
     * @property Grid\Column|Collection around_push
     * @property Grid\Column|Collection cids
     * @property Grid\Column|Collection bank_account
     * @property Grid\Column|Collection bank_name
     * @property Grid\Column|Collection real_name
     * @property Grid\Column|Collection trans_message
     * @property Grid\Column|Collection trans_status
     * @property Grid\Column|Collection transfer_time
     * @property Grid\Column|Collection black
     * @property Grid\Column|Collection face_certified
     * @property Grid\Column|Collection health_status
     * @property Grid\Column|Collection id_card_certified
     * @property Grid\Column|Collection last_check_time
     * @property Grid\Column|Collection role
     * @property Grid\Column|Collection transport_status
     * @property Grid\Column|Collection verified
     * @property Grid\Column|Collection cat_id
     * @property Grid\Column|Collection is_signboard
     * @property Grid\Column|Collection sell_tags
     * @property Grid\Column|Collection address_detail
     * @property Grid\Column|Collection community_id
     * @property Grid\Column|Collection logo
     * @property Grid\Column|Collection promotion_info
     * @property Grid\Column|Collection tel
     * @property Grid\Column|Collection points
     * @property Grid\Column|Collection region_name
     * @property Grid\Column|Collection contact_name
     * @property Grid\Column|Collection farthest
     * @property Grid\Column|Collection contact
     * @property Grid\Column|Collection suggestion_cid
     * @property Grid\Column|Collection is_json
     * @property Grid\Column|Collection last_update_user
     * @property Grid\Column|Collection receive_time
     * @property Grid\Column|Collection target_rider_id
     * @property Grid\Column|Collection trans_rider_id
     * @property Grid\Column|Collection card_type
     * @property Grid\Column|Collection image2
     * @property Grid\Column|Collection address_remark
     * @property Grid\Column|Collection area_code
     * @property Grid\Column|Collection county
     * @property Grid\Column|Collection is_default
     * @property Grid\Column|Collection is_white_list
     * @property Grid\Column|Collection auth_id
     * @property Grid\Column|Collection access_token
     * @property Grid\Column|Collection login_type
     * @property Grid\Column|Collection nickname
     * @property Grid\Column|Collection openid
     * @property Grid\Column|Collection union_id
     * @property Grid\Column|Collection birth
     * @property Grid\Column|Collection expiry_date
     * @property Grid\Column|Collection id_card
     * @property Grid\Column|Collection id_card_image
     * @property Grid\Column|Collection id_card_image_over
     * @property Grid\Column|Collection issuing_authority
     * @property Grid\Column|Collection issuing_date
     * @property Grid\Column|Collection nation
     * @property Grid\Column|Collection sex
     * @property Grid\Column|Collection constellation
     * @property Grid\Column|Collection shu_xiang
     * @property Grid\Column|Collection tag
     * @property Grid\Column|Collection rid
     * @property Grid\Column|Collection active
     * @property Grid\Column|Collection sign_date
     * @property Grid\Column|Collection active_paotui
     * @property Grid\Column|Collection active_shequ
     * @property Grid\Column|Collection alipay_account
     * @property Grid\Column|Collection birthday
     * @property Grid\Column|Collection certified
     * @property Grid\Column|Collection channel
     * @property Grid\Column|Collection device_id
     * @property Grid\Column|Collection device_type
     * @property Grid\Column|Collection easemob_activated
     * @property Grid\Column|Collection easemob_uuid
     * @property Grid\Column|Collection last_active_time
     * @property Grid\Column|Collection log_out_pt
     * @property Grid\Column|Collection log_out_sq
     * @property Grid\Column|Collection mp_openid
     * @property Grid\Column|Collection recommend_code
     * @property Grid\Column|Collection scene
     * @property Grid\Column|Collection check_imgs
     * @property Grid\Column|Collection last_vaccine_time
     * @property Grid\Column|Collection vaccine_times
     * @property Grid\Column|Collection app_type
     * @property Grid\Column|Collection current
     * @property Grid\Column|Collection download_path
     * @property Grid\Column|Collection system
     *
     * @method Grid\Column|Collection id(string $label = null)
     * @method Grid\Column|Collection order(string $label = null)
     * @method Grid\Column|Collection created_at(string $label = null)
     * @method Grid\Column|Collection day(string $label = null)
     * @method Grid\Column|Collection end_time(string $label = null)
     * @method Grid\Column|Collection image(string $label = null)
     * @method Grid\Column|Collection name(string $label = null)
     * @method Grid\Column|Collection start_time(string $label = null)
     * @method Grid\Column|Collection status(string $label = null)
     * @method Grid\Column|Collection time_type(string $label = null)
     * @method Grid\Column|Collection type(string $label = null)
     * @method Grid\Column|Collection updated_at(string $label = null)
     * @method Grid\Column|Collection activity_id(string $label = null)
     * @method Grid\Column|Collection child_id(string $label = null)
     * @method Grid\Column|Collection order_limit(string $label = null)
     * @method Grid\Column|Collection pre_reward_amount(string $label = null)
     * @method Grid\Column|Collection reward_amount(string $label = null)
     * @method Grid\Column|Collection settle_status(string $label = null)
     * @method Grid\Column|Collection settle_time(string $label = null)
     * @method Grid\Column|Collection user_id(string $label = null)
     * @method Grid\Column|Collection end(string $label = null)
     * @method Grid\Column|Collection order_count(string $label = null)
     * @method Grid\Column|Collection reward(string $label = null)
     * @method Grid\Column|Collection start(string $label = null)
     * @method Grid\Column|Collection detail(string $label = null)
     * @method Grid\Column|Collection version(string $label = null)
     * @method Grid\Column|Collection is_enabled(string $label = null)
     * @method Grid\Column|Collection extension(string $label = null)
     * @method Grid\Column|Collection icon(string $label = null)
     * @method Grid\Column|Collection parent_id(string $label = null)
     * @method Grid\Column|Collection uri(string $label = null)
     * @method Grid\Column|Collection menu_id(string $label = null)
     * @method Grid\Column|Collection permission_id(string $label = null)
     * @method Grid\Column|Collection http_method(string $label = null)
     * @method Grid\Column|Collection http_path(string $label = null)
     * @method Grid\Column|Collection slug(string $label = null)
     * @method Grid\Column|Collection role_id(string $label = null)
     * @method Grid\Column|Collection value(string $label = null)
     * @method Grid\Column|Collection site_id(string $label = null)
     * @method Grid\Column|Collection avatar(string $label = null)
     * @method Grid\Column|Collection password(string $label = null)
     * @method Grid\Column|Collection remember_token(string $label = null)
     * @method Grid\Column|Collection username(string $label = null)
     * @method Grid\Column|Collection cover(string $label = null)
     * @method Grid\Column|Collection sort(string $label = null)
     * @method Grid\Column|Collection target(string $label = null)
     * @method Grid\Column|Collection target_type(string $label = null)
     * @method Grid\Column|Collection content(string $label = null)
     * @method Grid\Column|Collection expired_at(string $label = null)
     * @method Grid\Column|Collection ip(string $label = null)
     * @method Grid\Column|Collection reason(string $label = null)
     * @method Grid\Column|Collection quantity(string $label = null)
     * @method Grid\Column|Collection shop_id(string $label = null)
     * @method Grid\Column|Collection spu_id(string $label = null)
     * @method Grid\Column|Collection pic(string $label = null)
     * @method Grid\Column|Collection pid(string $label = null)
     * @method Grid\Column|Collection sub_title(string $label = null)
     * @method Grid\Column|Collection view_order(string $label = null)
     * @method Grid\Column|Collection is_open(string $label = null)
     * @method Grid\Column|Collection latitude(string $label = null)
     * @method Grid\Column|Collection longitude(string $label = null)
     * @method Grid\Column|Collection region_id(string $label = null)
     * @method Grid\Column|Collection address(string $label = null)
     * @method Grid\Column|Collection city(string $label = null)
     * @method Grid\Column|Collection company(string $label = null)
     * @method Grid\Column|Collection district(string $label = null)
     * @method Grid\Column|Collection phone(string $label = null)
     * @method Grid\Column|Collection province(string $label = null)
     * @method Grid\Column|Collection discount_price(string $label = null)
     * @method Grid\Column|Collection rules(string $label = null)
     * @method Grid\Column|Collection sales(string $label = null)
     * @method Grid\Column|Collection start_price(string $label = null)
     * @method Grid\Column|Collection stock(string $label = null)
     * @method Grid\Column|Collection validity(string $label = null)
     * @method Grid\Column|Collection closed(string $label = null)
     * @method Grid\Column|Collection operator_id(string $label = null)
     * @method Grid\Column|Collection operator_name(string $label = null)
     * @method Grid\Column|Collection order_id(string $label = null)
     * @method Grid\Column|Collection remark(string $label = null)
     * @method Grid\Column|Collection rider_id(string $label = null)
     * @method Grid\Column|Collection card_no(string $label = null)
     * @method Grid\Column|Collection create_time(string $label = null)
     * @method Grid\Column|Collection exp_time_type(string $label = null)
     * @method Grid\Column|Collection obverse(string $label = null)
     * @method Grid\Column|Collection vehicle_type(string $label = null)
     * @method Grid\Column|Collection car_no(string $label = null)
     * @method Grid\Column|Collection actual_amount(string $label = null)
     * @method Grid\Column|Collection deleted_at(string $label = null)
     * @method Grid\Column|Collection order_amount(string $label = null)
     * @method Grid\Column|Collection order_no(string $label = null)
     * @method Grid\Column|Collection paid_at(string $label = null)
     * @method Grid\Column|Collection pay_method(string $label = null)
     * @method Grid\Column|Collection payment_no(string $label = null)
     * @method Grid\Column|Collection refund_at(string $label = null)
     * @method Grid\Column|Collection refund_no(string $label = null)
     * @method Grid\Column|Collection refund_status(string $label = null)
     * @method Grid\Column|Collection connection(string $label = null)
     * @method Grid\Column|Collection exception(string $label = null)
     * @method Grid\Column|Collection failed_at(string $label = null)
     * @method Grid\Column|Collection payload(string $label = null)
     * @method Grid\Column|Collection queue(string $label = null)
     * @method Grid\Column|Collection uuid(string $label = null)
     * @method Grid\Column|Collection check_time(string $label = null)
     * @method Grid\Column|Collection images(string $label = null)
     * @method Grid\Column|Collection temperature(string $label = null)
     * @method Grid\Column|Collection ym_count(string $label = null)
     * @method Grid\Column|Collection check_img(string $label = null)
     * @method Grid\Column|Collection path(string $label = null)
     * @method Grid\Column|Collection belong_id(string $label = null)
     * @method Grid\Column|Collection belong_type(string $label = null)
     * @method Grid\Column|Collection lat(string $label = null)
     * @method Grid\Column|Collection lng(string $label = null)
     * @method Grid\Column|Collection time(string $label = null)
     * @method Grid\Column|Collection platform(string $label = null)
     * @method Grid\Column|Collection is_important(string $label = null)
     * @method Grid\Column|Collection is_settled(string $label = null)
     * @method Grid\Column|Collection notify_id(string $label = null)
     * @method Grid\Column|Collection app_key(string $label = null)
     * @method Grid\Column|Collection appointment_end_time(string $label = null)
     * @method Grid\Column|Collection appointment_start_time(string $label = null)
     * @method Grid\Column|Collection arrive_at(string $label = null)
     * @method Grid\Column|Collection buy_imgs(string $label = null)
     * @method Grid\Column|Collection category_id(string $label = null)
     * @method Grid\Column|Collection close_reason(string $label = null)
     * @method Grid\Column|Collection coupon_amount(string $label = null)
     * @method Grid\Column|Collection coupon_id(string $label = null)
     * @method Grid\Column|Collection deliver_address(string $label = null)
     * @method Grid\Column|Collection deliver_address_id(string $label = null)
     * @method Grid\Column|Collection deliver_lat(string $label = null)
     * @method Grid\Column|Collection deliver_lng(string $label = null)
     * @method Grid\Column|Collection deliver_name(string $label = null)
     * @method Grid\Column|Collection deliver_phone(string $label = null)
     * @method Grid\Column|Collection deliver_region_id(string $label = null)
     * @method Grid\Column|Collection dispatch_status(string $label = null)
     * @method Grid\Column|Collection distance(string $label = null)
     * @method Grid\Column|Collection distance_price(string $label = null)
     * @method Grid\Column|Collection estimated_delivery_time(string $label = null)
     * @method Grid\Column|Collection finish_time(string $label = null)
     * @method Grid\Column|Collection freight(string $label = null)
     * @method Grid\Column|Collection goods_category_id(string $label = null)
     * @method Grid\Column|Collection goods_desc(string $label = null)
     * @method Grid\Column|Collection goods_imgs(string $label = null)
     * @method Grid\Column|Collection goods_price(string $label = null)
     * @method Grid\Column|Collection goods_protected_price(string $label = null)
     * @method Grid\Column|Collection gratuity(string $label = null)
     * @method Grid\Column|Collection hide_address(string $label = null)
     * @method Grid\Column|Collection is_special(string $label = null)
     * @method Grid\Column|Collection is_trans(string $label = null)
     * @method Grid\Column|Collection need_incubator(string $label = null)
     * @method Grid\Column|Collection order_status(string $label = null)
     * @method Grid\Column|Collection ori_rider_id(string $label = null)
     * @method Grid\Column|Collection out_order_no(string $label = null)
     * @method Grid\Column|Collection pickup_address(string $label = null)
     * @method Grid\Column|Collection pickup_address_id(string $label = null)
     * @method Grid\Column|Collection pickup_at(string $label = null)
     * @method Grid\Column|Collection pickup_code(string $label = null)
     * @method Grid\Column|Collection pickup_code_mode(string $label = null)
     * @method Grid\Column|Collection pickup_lat(string $label = null)
     * @method Grid\Column|Collection pickup_lng(string $label = null)
     * @method Grid\Column|Collection pickup_name(string $label = null)
     * @method Grid\Column|Collection pickup_phone(string $label = null)
     * @method Grid\Column|Collection pickup_region_id(string $label = null)
     * @method Grid\Column|Collection receipt_time(string $label = null)
     * @method Grid\Column|Collection receive_code(string $label = null)
     * @method Grid\Column|Collection receive_code_mode(string $label = null)
     * @method Grid\Column|Collection refund_amount(string $label = null)
     * @method Grid\Column|Collection reward_amount_full(string $label = null)
     * @method Grid\Column|Collection reward_amount_part(string $label = null)
     * @method Grid\Column|Collection time_price(string $label = null)
     * @method Grid\Column|Collection volume(string $label = null)
     * @method Grid\Column|Collection weather_price(string $label = null)
     * @method Grid\Column|Collection weight(string $label = null)
     * @method Grid\Column|Collection weight_price(string $label = null)
     * @method Grid\Column|Collection address_id(string $label = null)
     * @method Grid\Column|Collection app_secret(string $label = null)
     * @method Grid\Column|Collection callback_url(string $label = null)
     * @method Grid\Column|Collection price(string $label = null)
     * @method Grid\Column|Collection spu_cover(string $label = null)
     * @method Grid\Column|Collection spu_name(string $label = null)
     * @method Grid\Column|Collection cdgf_star(string $label = null)
     * @method Grid\Column|Collection hpwh_star(string $label = null)
     * @method Grid\Column|Collection imgs(string $label = null)
     * @method Grid\Column|Collection is_anonymous(string $label = null)
     * @method Grid\Column|Collection is_satisfied(string $label = null)
     * @method Grid\Column|Collection lmrq_star(string $label = null)
     * @method Grid\Column|Collection pszs_star(string $label = null)
     * @method Grid\Column|Collection ybzj_star(string $label = null)
     * @method Grid\Column|Collection amount(string $label = null)
     * @method Grid\Column|Collection no(string $label = null)
     * @method Grid\Column|Collection refund_pics(string $label = null)
     * @method Grid\Column|Collection refund_reason(string $label = null)
     * @method Grid\Column|Collection refund_remark(string $label = null)
     * @method Grid\Column|Collection reject_reason(string $label = null)
     * @method Grid\Column|Collection spu_info(string $label = null)
     * @method Grid\Column|Collection verify_at(string $label = null)
     * @method Grid\Column|Collection is_tel_protect(string $label = null)
     * @method Grid\Column|Collection pay_amount(string $label = null)
     * @method Grid\Column|Collection receiver_address(string $label = null)
     * @method Grid\Column|Collection receiver_area(string $label = null)
     * @method Grid\Column|Collection receiver_name(string $label = null)
     * @method Grid\Column|Collection receiver_tel(string $label = null)
     * @method Grid\Column|Collection reduce_amount(string $label = null)
     * @method Grid\Column|Collection refund_type(string $label = null)
     * @method Grid\Column|Collection sending_end_time(string $label = null)
     * @method Grid\Column|Collection sending_start_time(string $label = null)
     * @method Grid\Column|Collection email(string $label = null)
     * @method Grid\Column|Collection token(string $label = null)
     * @method Grid\Column|Collection abilities(string $label = null)
     * @method Grid\Column|Collection expires_at(string $label = null)
     * @method Grid\Column|Collection last_used_at(string $label = null)
     * @method Grid\Column|Collection tokenable_id(string $label = null)
     * @method Grid\Column|Collection tokenable_type(string $label = null)
     * @method Grid\Column|Collection expire_time(string $label = null)
     * @method Grid\Column|Collection phone_a(string $label = null)
     * @method Grid\Column|Collection phone_x(string $label = null)
     * @method Grid\Column|Collection subsid(string $label = null)
     * @method Grid\Column|Collection purchase_time(string $label = null)
     * @method Grid\Column|Collection region(string $label = null)
     * @method Grid\Column|Collection vendor(string $label = null)
     * @method Grid\Column|Collection base_price(string $label = null)
     * @method Grid\Column|Collection period_price(string $label = null)
     * @method Grid\Column|Collection code(string $label = null)
     * @method Grid\Column|Collection is_hot(string $label = null)
     * @method Grid\Column|Collection letter(string $label = null)
     * @method Grid\Column|Collection level(string $label = null)
     * @method Grid\Column|Collection pinyin(string $label = null)
     * @method Grid\Column|Collection citycode(string $label = null)
     * @method Grid\Column|Collection dp_id(string $label = null)
     * @method Grid\Column|Collection is_show(string $label = null)
     * @method Grid\Column|Collection mername(string $label = null)
     * @method Grid\Column|Collection mt_id(string $label = null)
     * @method Grid\Column|Collection qyh_id(string $label = null)
     * @method Grid\Column|Collection sname(string $label = null)
     * @method Grid\Column|Collection st_id(string $label = null)
     * @method Grid\Column|Collection ww_id(string $label = null)
     * @method Grid\Column|Collection yzcode(string $label = null)
     * @method Grid\Column|Collection after_amount(string $label = null)
     * @method Grid\Column|Collection before_amount(string $label = null)
     * @method Grid\Column|Collection business_type(string $label = null)
     * @method Grid\Column|Collection chg_amount(string $label = null)
     * @method Grid\Column|Collection user_account_id(string $label = null)
     * @method Grid\Column|Collection account_type(string $label = null)
     * @method Grid\Column|Collection card_background(string $label = null)
     * @method Grid\Column|Collection max_score(string $label = null)
     * @method Grid\Column|Collection trans_limit(string $label = null)
     * @method Grid\Column|Collection children_count(string $label = null)
     * @method Grid\Column|Collection edu_background(string $label = null)
     * @method Grid\Column|Collection emergency_contact(string $label = null)
     * @method Grid\Column|Collection emergency_mobile(string $label = null)
     * @method Grid\Column|Collection health_cert_cover(string $label = null)
     * @method Grid\Column|Collection health_cert_expire_date(string $label = null)
     * @method Grid\Column|Collection health_cert_no(string $label = null)
     * @method Grid\Column|Collection health_cert_obverse(string $label = null)
     * @method Grid\Column|Collection health_cert_publisher(string $label = null)
     * @method Grid\Column|Collection job(string $label = null)
     * @method Grid\Column|Collection marriage(string $label = null)
     * @method Grid\Column|Collection transport_type(string $label = null)
     * @method Grid\Column|Collection point_key(string $label = null)
     * @method Grid\Column|Collection point_type(string $label = null)
     * @method Grid\Column|Collection score(string $label = null)
     * @method Grid\Column|Collection around_push(string $label = null)
     * @method Grid\Column|Collection cids(string $label = null)
     * @method Grid\Column|Collection bank_account(string $label = null)
     * @method Grid\Column|Collection bank_name(string $label = null)
     * @method Grid\Column|Collection real_name(string $label = null)
     * @method Grid\Column|Collection trans_message(string $label = null)
     * @method Grid\Column|Collection trans_status(string $label = null)
     * @method Grid\Column|Collection transfer_time(string $label = null)
     * @method Grid\Column|Collection black(string $label = null)
     * @method Grid\Column|Collection face_certified(string $label = null)
     * @method Grid\Column|Collection health_status(string $label = null)
     * @method Grid\Column|Collection id_card_certified(string $label = null)
     * @method Grid\Column|Collection last_check_time(string $label = null)
     * @method Grid\Column|Collection role(string $label = null)
     * @method Grid\Column|Collection transport_status(string $label = null)
     * @method Grid\Column|Collection verified(string $label = null)
     * @method Grid\Column|Collection cat_id(string $label = null)
     * @method Grid\Column|Collection is_signboard(string $label = null)
     * @method Grid\Column|Collection sell_tags(string $label = null)
     * @method Grid\Column|Collection address_detail(string $label = null)
     * @method Grid\Column|Collection community_id(string $label = null)
     * @method Grid\Column|Collection logo(string $label = null)
     * @method Grid\Column|Collection promotion_info(string $label = null)
     * @method Grid\Column|Collection tel(string $label = null)
     * @method Grid\Column|Collection points(string $label = null)
     * @method Grid\Column|Collection region_name(string $label = null)
     * @method Grid\Column|Collection contact_name(string $label = null)
     * @method Grid\Column|Collection farthest(string $label = null)
     * @method Grid\Column|Collection contact(string $label = null)
     * @method Grid\Column|Collection suggestion_cid(string $label = null)
     * @method Grid\Column|Collection is_json(string $label = null)
     * @method Grid\Column|Collection last_update_user(string $label = null)
     * @method Grid\Column|Collection receive_time(string $label = null)
     * @method Grid\Column|Collection target_rider_id(string $label = null)
     * @method Grid\Column|Collection trans_rider_id(string $label = null)
     * @method Grid\Column|Collection card_type(string $label = null)
     * @method Grid\Column|Collection image2(string $label = null)
     * @method Grid\Column|Collection address_remark(string $label = null)
     * @method Grid\Column|Collection area_code(string $label = null)
     * @method Grid\Column|Collection county(string $label = null)
     * @method Grid\Column|Collection is_default(string $label = null)
     * @method Grid\Column|Collection is_white_list(string $label = null)
     * @method Grid\Column|Collection auth_id(string $label = null)
     * @method Grid\Column|Collection access_token(string $label = null)
     * @method Grid\Column|Collection login_type(string $label = null)
     * @method Grid\Column|Collection nickname(string $label = null)
     * @method Grid\Column|Collection openid(string $label = null)
     * @method Grid\Column|Collection union_id(string $label = null)
     * @method Grid\Column|Collection birth(string $label = null)
     * @method Grid\Column|Collection expiry_date(string $label = null)
     * @method Grid\Column|Collection id_card(string $label = null)
     * @method Grid\Column|Collection id_card_image(string $label = null)
     * @method Grid\Column|Collection id_card_image_over(string $label = null)
     * @method Grid\Column|Collection issuing_authority(string $label = null)
     * @method Grid\Column|Collection issuing_date(string $label = null)
     * @method Grid\Column|Collection nation(string $label = null)
     * @method Grid\Column|Collection sex(string $label = null)
     * @method Grid\Column|Collection constellation(string $label = null)
     * @method Grid\Column|Collection shu_xiang(string $label = null)
     * @method Grid\Column|Collection tag(string $label = null)
     * @method Grid\Column|Collection rid(string $label = null)
     * @method Grid\Column|Collection active(string $label = null)
     * @method Grid\Column|Collection sign_date(string $label = null)
     * @method Grid\Column|Collection active_paotui(string $label = null)
     * @method Grid\Column|Collection active_shequ(string $label = null)
     * @method Grid\Column|Collection alipay_account(string $label = null)
     * @method Grid\Column|Collection birthday(string $label = null)
     * @method Grid\Column|Collection certified(string $label = null)
     * @method Grid\Column|Collection channel(string $label = null)
     * @method Grid\Column|Collection device_id(string $label = null)
     * @method Grid\Column|Collection device_type(string $label = null)
     * @method Grid\Column|Collection easemob_activated(string $label = null)
     * @method Grid\Column|Collection easemob_uuid(string $label = null)
     * @method Grid\Column|Collection last_active_time(string $label = null)
     * @method Grid\Column|Collection log_out_pt(string $label = null)
     * @method Grid\Column|Collection log_out_sq(string $label = null)
     * @method Grid\Column|Collection mp_openid(string $label = null)
     * @method Grid\Column|Collection recommend_code(string $label = null)
     * @method Grid\Column|Collection scene(string $label = null)
     * @method Grid\Column|Collection check_imgs(string $label = null)
     * @method Grid\Column|Collection last_vaccine_time(string $label = null)
     * @method Grid\Column|Collection vaccine_times(string $label = null)
     * @method Grid\Column|Collection app_type(string $label = null)
     * @method Grid\Column|Collection current(string $label = null)
     * @method Grid\Column|Collection download_path(string $label = null)
     * @method Grid\Column|Collection system(string $label = null)
     */
    class Grid {}

    class MiniGrid extends Grid {}

    /**
     * @property Show\Field|Collection id
     * @property Show\Field|Collection order
     * @property Show\Field|Collection created_at
     * @property Show\Field|Collection day
     * @property Show\Field|Collection end_time
     * @property Show\Field|Collection image
     * @property Show\Field|Collection name
     * @property Show\Field|Collection start_time
     * @property Show\Field|Collection status
     * @property Show\Field|Collection time_type
     * @property Show\Field|Collection type
     * @property Show\Field|Collection updated_at
     * @property Show\Field|Collection activity_id
     * @property Show\Field|Collection child_id
     * @property Show\Field|Collection order_limit
     * @property Show\Field|Collection pre_reward_amount
     * @property Show\Field|Collection reward_amount
     * @property Show\Field|Collection settle_status
     * @property Show\Field|Collection settle_time
     * @property Show\Field|Collection user_id
     * @property Show\Field|Collection end
     * @property Show\Field|Collection order_count
     * @property Show\Field|Collection reward
     * @property Show\Field|Collection start
     * @property Show\Field|Collection detail
     * @property Show\Field|Collection version
     * @property Show\Field|Collection is_enabled
     * @property Show\Field|Collection extension
     * @property Show\Field|Collection icon
     * @property Show\Field|Collection parent_id
     * @property Show\Field|Collection uri
     * @property Show\Field|Collection menu_id
     * @property Show\Field|Collection permission_id
     * @property Show\Field|Collection http_method
     * @property Show\Field|Collection http_path
     * @property Show\Field|Collection slug
     * @property Show\Field|Collection role_id
     * @property Show\Field|Collection value
     * @property Show\Field|Collection site_id
     * @property Show\Field|Collection avatar
     * @property Show\Field|Collection password
     * @property Show\Field|Collection remember_token
     * @property Show\Field|Collection username
     * @property Show\Field|Collection cover
     * @property Show\Field|Collection sort
     * @property Show\Field|Collection target
     * @property Show\Field|Collection target_type
     * @property Show\Field|Collection content
     * @property Show\Field|Collection expired_at
     * @property Show\Field|Collection ip
     * @property Show\Field|Collection reason
     * @property Show\Field|Collection quantity
     * @property Show\Field|Collection shop_id
     * @property Show\Field|Collection spu_id
     * @property Show\Field|Collection pic
     * @property Show\Field|Collection pid
     * @property Show\Field|Collection sub_title
     * @property Show\Field|Collection view_order
     * @property Show\Field|Collection is_open
     * @property Show\Field|Collection latitude
     * @property Show\Field|Collection longitude
     * @property Show\Field|Collection region_id
     * @property Show\Field|Collection address
     * @property Show\Field|Collection city
     * @property Show\Field|Collection company
     * @property Show\Field|Collection district
     * @property Show\Field|Collection phone
     * @property Show\Field|Collection province
     * @property Show\Field|Collection discount_price
     * @property Show\Field|Collection rules
     * @property Show\Field|Collection sales
     * @property Show\Field|Collection start_price
     * @property Show\Field|Collection stock
     * @property Show\Field|Collection validity
     * @property Show\Field|Collection closed
     * @property Show\Field|Collection operator_id
     * @property Show\Field|Collection operator_name
     * @property Show\Field|Collection order_id
     * @property Show\Field|Collection remark
     * @property Show\Field|Collection rider_id
     * @property Show\Field|Collection card_no
     * @property Show\Field|Collection create_time
     * @property Show\Field|Collection exp_time_type
     * @property Show\Field|Collection obverse
     * @property Show\Field|Collection vehicle_type
     * @property Show\Field|Collection car_no
     * @property Show\Field|Collection actual_amount
     * @property Show\Field|Collection deleted_at
     * @property Show\Field|Collection order_amount
     * @property Show\Field|Collection order_no
     * @property Show\Field|Collection paid_at
     * @property Show\Field|Collection pay_method
     * @property Show\Field|Collection payment_no
     * @property Show\Field|Collection refund_at
     * @property Show\Field|Collection refund_no
     * @property Show\Field|Collection refund_status
     * @property Show\Field|Collection connection
     * @property Show\Field|Collection exception
     * @property Show\Field|Collection failed_at
     * @property Show\Field|Collection payload
     * @property Show\Field|Collection queue
     * @property Show\Field|Collection uuid
     * @property Show\Field|Collection check_time
     * @property Show\Field|Collection images
     * @property Show\Field|Collection temperature
     * @property Show\Field|Collection ym_count
     * @property Show\Field|Collection check_img
     * @property Show\Field|Collection path
     * @property Show\Field|Collection belong_id
     * @property Show\Field|Collection belong_type
     * @property Show\Field|Collection lat
     * @property Show\Field|Collection lng
     * @property Show\Field|Collection time
     * @property Show\Field|Collection platform
     * @property Show\Field|Collection is_important
     * @property Show\Field|Collection is_settled
     * @property Show\Field|Collection notify_id
     * @property Show\Field|Collection app_key
     * @property Show\Field|Collection appointment_end_time
     * @property Show\Field|Collection appointment_start_time
     * @property Show\Field|Collection arrive_at
     * @property Show\Field|Collection buy_imgs
     * @property Show\Field|Collection category_id
     * @property Show\Field|Collection close_reason
     * @property Show\Field|Collection coupon_amount
     * @property Show\Field|Collection coupon_id
     * @property Show\Field|Collection deliver_address
     * @property Show\Field|Collection deliver_address_id
     * @property Show\Field|Collection deliver_lat
     * @property Show\Field|Collection deliver_lng
     * @property Show\Field|Collection deliver_name
     * @property Show\Field|Collection deliver_phone
     * @property Show\Field|Collection deliver_region_id
     * @property Show\Field|Collection dispatch_status
     * @property Show\Field|Collection distance
     * @property Show\Field|Collection distance_price
     * @property Show\Field|Collection estimated_delivery_time
     * @property Show\Field|Collection finish_time
     * @property Show\Field|Collection freight
     * @property Show\Field|Collection goods_category_id
     * @property Show\Field|Collection goods_desc
     * @property Show\Field|Collection goods_imgs
     * @property Show\Field|Collection goods_price
     * @property Show\Field|Collection goods_protected_price
     * @property Show\Field|Collection gratuity
     * @property Show\Field|Collection hide_address
     * @property Show\Field|Collection is_special
     * @property Show\Field|Collection is_trans
     * @property Show\Field|Collection need_incubator
     * @property Show\Field|Collection order_status
     * @property Show\Field|Collection ori_rider_id
     * @property Show\Field|Collection out_order_no
     * @property Show\Field|Collection pickup_address
     * @property Show\Field|Collection pickup_address_id
     * @property Show\Field|Collection pickup_at
     * @property Show\Field|Collection pickup_code
     * @property Show\Field|Collection pickup_code_mode
     * @property Show\Field|Collection pickup_lat
     * @property Show\Field|Collection pickup_lng
     * @property Show\Field|Collection pickup_name
     * @property Show\Field|Collection pickup_phone
     * @property Show\Field|Collection pickup_region_id
     * @property Show\Field|Collection receipt_time
     * @property Show\Field|Collection receive_code
     * @property Show\Field|Collection receive_code_mode
     * @property Show\Field|Collection refund_amount
     * @property Show\Field|Collection reward_amount_full
     * @property Show\Field|Collection reward_amount_part
     * @property Show\Field|Collection time_price
     * @property Show\Field|Collection volume
     * @property Show\Field|Collection weather_price
     * @property Show\Field|Collection weight
     * @property Show\Field|Collection weight_price
     * @property Show\Field|Collection address_id
     * @property Show\Field|Collection app_secret
     * @property Show\Field|Collection callback_url
     * @property Show\Field|Collection price
     * @property Show\Field|Collection spu_cover
     * @property Show\Field|Collection spu_name
     * @property Show\Field|Collection cdgf_star
     * @property Show\Field|Collection hpwh_star
     * @property Show\Field|Collection imgs
     * @property Show\Field|Collection is_anonymous
     * @property Show\Field|Collection is_satisfied
     * @property Show\Field|Collection lmrq_star
     * @property Show\Field|Collection pszs_star
     * @property Show\Field|Collection ybzj_star
     * @property Show\Field|Collection amount
     * @property Show\Field|Collection no
     * @property Show\Field|Collection refund_pics
     * @property Show\Field|Collection refund_reason
     * @property Show\Field|Collection refund_remark
     * @property Show\Field|Collection reject_reason
     * @property Show\Field|Collection spu_info
     * @property Show\Field|Collection verify_at
     * @property Show\Field|Collection is_tel_protect
     * @property Show\Field|Collection pay_amount
     * @property Show\Field|Collection receiver_address
     * @property Show\Field|Collection receiver_area
     * @property Show\Field|Collection receiver_name
     * @property Show\Field|Collection receiver_tel
     * @property Show\Field|Collection reduce_amount
     * @property Show\Field|Collection refund_type
     * @property Show\Field|Collection sending_end_time
     * @property Show\Field|Collection sending_start_time
     * @property Show\Field|Collection email
     * @property Show\Field|Collection token
     * @property Show\Field|Collection abilities
     * @property Show\Field|Collection expires_at
     * @property Show\Field|Collection last_used_at
     * @property Show\Field|Collection tokenable_id
     * @property Show\Field|Collection tokenable_type
     * @property Show\Field|Collection expire_time
     * @property Show\Field|Collection phone_a
     * @property Show\Field|Collection phone_x
     * @property Show\Field|Collection subsid
     * @property Show\Field|Collection purchase_time
     * @property Show\Field|Collection region
     * @property Show\Field|Collection vendor
     * @property Show\Field|Collection base_price
     * @property Show\Field|Collection period_price
     * @property Show\Field|Collection code
     * @property Show\Field|Collection is_hot
     * @property Show\Field|Collection letter
     * @property Show\Field|Collection level
     * @property Show\Field|Collection pinyin
     * @property Show\Field|Collection citycode
     * @property Show\Field|Collection dp_id
     * @property Show\Field|Collection is_show
     * @property Show\Field|Collection mername
     * @property Show\Field|Collection mt_id
     * @property Show\Field|Collection qyh_id
     * @property Show\Field|Collection sname
     * @property Show\Field|Collection st_id
     * @property Show\Field|Collection ww_id
     * @property Show\Field|Collection yzcode
     * @property Show\Field|Collection after_amount
     * @property Show\Field|Collection before_amount
     * @property Show\Field|Collection business_type
     * @property Show\Field|Collection chg_amount
     * @property Show\Field|Collection user_account_id
     * @property Show\Field|Collection account_type
     * @property Show\Field|Collection card_background
     * @property Show\Field|Collection max_score
     * @property Show\Field|Collection trans_limit
     * @property Show\Field|Collection children_count
     * @property Show\Field|Collection edu_background
     * @property Show\Field|Collection emergency_contact
     * @property Show\Field|Collection emergency_mobile
     * @property Show\Field|Collection health_cert_cover
     * @property Show\Field|Collection health_cert_expire_date
     * @property Show\Field|Collection health_cert_no
     * @property Show\Field|Collection health_cert_obverse
     * @property Show\Field|Collection health_cert_publisher
     * @property Show\Field|Collection job
     * @property Show\Field|Collection marriage
     * @property Show\Field|Collection transport_type
     * @property Show\Field|Collection point_key
     * @property Show\Field|Collection point_type
     * @property Show\Field|Collection score
     * @property Show\Field|Collection around_push
     * @property Show\Field|Collection cids
     * @property Show\Field|Collection bank_account
     * @property Show\Field|Collection bank_name
     * @property Show\Field|Collection real_name
     * @property Show\Field|Collection trans_message
     * @property Show\Field|Collection trans_status
     * @property Show\Field|Collection transfer_time
     * @property Show\Field|Collection black
     * @property Show\Field|Collection face_certified
     * @property Show\Field|Collection health_status
     * @property Show\Field|Collection id_card_certified
     * @property Show\Field|Collection last_check_time
     * @property Show\Field|Collection role
     * @property Show\Field|Collection transport_status
     * @property Show\Field|Collection verified
     * @property Show\Field|Collection cat_id
     * @property Show\Field|Collection is_signboard
     * @property Show\Field|Collection sell_tags
     * @property Show\Field|Collection address_detail
     * @property Show\Field|Collection community_id
     * @property Show\Field|Collection logo
     * @property Show\Field|Collection promotion_info
     * @property Show\Field|Collection tel
     * @property Show\Field|Collection points
     * @property Show\Field|Collection region_name
     * @property Show\Field|Collection contact_name
     * @property Show\Field|Collection farthest
     * @property Show\Field|Collection contact
     * @property Show\Field|Collection suggestion_cid
     * @property Show\Field|Collection is_json
     * @property Show\Field|Collection last_update_user
     * @property Show\Field|Collection receive_time
     * @property Show\Field|Collection target_rider_id
     * @property Show\Field|Collection trans_rider_id
     * @property Show\Field|Collection card_type
     * @property Show\Field|Collection image2
     * @property Show\Field|Collection address_remark
     * @property Show\Field|Collection area_code
     * @property Show\Field|Collection county
     * @property Show\Field|Collection is_default
     * @property Show\Field|Collection is_white_list
     * @property Show\Field|Collection auth_id
     * @property Show\Field|Collection access_token
     * @property Show\Field|Collection login_type
     * @property Show\Field|Collection nickname
     * @property Show\Field|Collection openid
     * @property Show\Field|Collection union_id
     * @property Show\Field|Collection birth
     * @property Show\Field|Collection expiry_date
     * @property Show\Field|Collection id_card
     * @property Show\Field|Collection id_card_image
     * @property Show\Field|Collection id_card_image_over
     * @property Show\Field|Collection issuing_authority
     * @property Show\Field|Collection issuing_date
     * @property Show\Field|Collection nation
     * @property Show\Field|Collection sex
     * @property Show\Field|Collection constellation
     * @property Show\Field|Collection shu_xiang
     * @property Show\Field|Collection tag
     * @property Show\Field|Collection rid
     * @property Show\Field|Collection active
     * @property Show\Field|Collection sign_date
     * @property Show\Field|Collection active_paotui
     * @property Show\Field|Collection active_shequ
     * @property Show\Field|Collection alipay_account
     * @property Show\Field|Collection birthday
     * @property Show\Field|Collection certified
     * @property Show\Field|Collection channel
     * @property Show\Field|Collection device_id
     * @property Show\Field|Collection device_type
     * @property Show\Field|Collection easemob_activated
     * @property Show\Field|Collection easemob_uuid
     * @property Show\Field|Collection last_active_time
     * @property Show\Field|Collection log_out_pt
     * @property Show\Field|Collection log_out_sq
     * @property Show\Field|Collection mp_openid
     * @property Show\Field|Collection recommend_code
     * @property Show\Field|Collection scene
     * @property Show\Field|Collection check_imgs
     * @property Show\Field|Collection last_vaccine_time
     * @property Show\Field|Collection vaccine_times
     * @property Show\Field|Collection app_type
     * @property Show\Field|Collection current
     * @property Show\Field|Collection download_path
     * @property Show\Field|Collection system
     *
     * @method Show\Field|Collection id(string $label = null)
     * @method Show\Field|Collection order(string $label = null)
     * @method Show\Field|Collection created_at(string $label = null)
     * @method Show\Field|Collection day(string $label = null)
     * @method Show\Field|Collection end_time(string $label = null)
     * @method Show\Field|Collection image(string $label = null)
     * @method Show\Field|Collection name(string $label = null)
     * @method Show\Field|Collection start_time(string $label = null)
     * @method Show\Field|Collection status(string $label = null)
     * @method Show\Field|Collection time_type(string $label = null)
     * @method Show\Field|Collection type(string $label = null)
     * @method Show\Field|Collection updated_at(string $label = null)
     * @method Show\Field|Collection activity_id(string $label = null)
     * @method Show\Field|Collection child_id(string $label = null)
     * @method Show\Field|Collection order_limit(string $label = null)
     * @method Show\Field|Collection pre_reward_amount(string $label = null)
     * @method Show\Field|Collection reward_amount(string $label = null)
     * @method Show\Field|Collection settle_status(string $label = null)
     * @method Show\Field|Collection settle_time(string $label = null)
     * @method Show\Field|Collection user_id(string $label = null)
     * @method Show\Field|Collection end(string $label = null)
     * @method Show\Field|Collection order_count(string $label = null)
     * @method Show\Field|Collection reward(string $label = null)
     * @method Show\Field|Collection start(string $label = null)
     * @method Show\Field|Collection detail(string $label = null)
     * @method Show\Field|Collection version(string $label = null)
     * @method Show\Field|Collection is_enabled(string $label = null)
     * @method Show\Field|Collection extension(string $label = null)
     * @method Show\Field|Collection icon(string $label = null)
     * @method Show\Field|Collection parent_id(string $label = null)
     * @method Show\Field|Collection uri(string $label = null)
     * @method Show\Field|Collection menu_id(string $label = null)
     * @method Show\Field|Collection permission_id(string $label = null)
     * @method Show\Field|Collection http_method(string $label = null)
     * @method Show\Field|Collection http_path(string $label = null)
     * @method Show\Field|Collection slug(string $label = null)
     * @method Show\Field|Collection role_id(string $label = null)
     * @method Show\Field|Collection value(string $label = null)
     * @method Show\Field|Collection site_id(string $label = null)
     * @method Show\Field|Collection avatar(string $label = null)
     * @method Show\Field|Collection password(string $label = null)
     * @method Show\Field|Collection remember_token(string $label = null)
     * @method Show\Field|Collection username(string $label = null)
     * @method Show\Field|Collection cover(string $label = null)
     * @method Show\Field|Collection sort(string $label = null)
     * @method Show\Field|Collection target(string $label = null)
     * @method Show\Field|Collection target_type(string $label = null)
     * @method Show\Field|Collection content(string $label = null)
     * @method Show\Field|Collection expired_at(string $label = null)
     * @method Show\Field|Collection ip(string $label = null)
     * @method Show\Field|Collection reason(string $label = null)
     * @method Show\Field|Collection quantity(string $label = null)
     * @method Show\Field|Collection shop_id(string $label = null)
     * @method Show\Field|Collection spu_id(string $label = null)
     * @method Show\Field|Collection pic(string $label = null)
     * @method Show\Field|Collection pid(string $label = null)
     * @method Show\Field|Collection sub_title(string $label = null)
     * @method Show\Field|Collection view_order(string $label = null)
     * @method Show\Field|Collection is_open(string $label = null)
     * @method Show\Field|Collection latitude(string $label = null)
     * @method Show\Field|Collection longitude(string $label = null)
     * @method Show\Field|Collection region_id(string $label = null)
     * @method Show\Field|Collection address(string $label = null)
     * @method Show\Field|Collection city(string $label = null)
     * @method Show\Field|Collection company(string $label = null)
     * @method Show\Field|Collection district(string $label = null)
     * @method Show\Field|Collection phone(string $label = null)
     * @method Show\Field|Collection province(string $label = null)
     * @method Show\Field|Collection discount_price(string $label = null)
     * @method Show\Field|Collection rules(string $label = null)
     * @method Show\Field|Collection sales(string $label = null)
     * @method Show\Field|Collection start_price(string $label = null)
     * @method Show\Field|Collection stock(string $label = null)
     * @method Show\Field|Collection validity(string $label = null)
     * @method Show\Field|Collection closed(string $label = null)
     * @method Show\Field|Collection operator_id(string $label = null)
     * @method Show\Field|Collection operator_name(string $label = null)
     * @method Show\Field|Collection order_id(string $label = null)
     * @method Show\Field|Collection remark(string $label = null)
     * @method Show\Field|Collection rider_id(string $label = null)
     * @method Show\Field|Collection card_no(string $label = null)
     * @method Show\Field|Collection create_time(string $label = null)
     * @method Show\Field|Collection exp_time_type(string $label = null)
     * @method Show\Field|Collection obverse(string $label = null)
     * @method Show\Field|Collection vehicle_type(string $label = null)
     * @method Show\Field|Collection car_no(string $label = null)
     * @method Show\Field|Collection actual_amount(string $label = null)
     * @method Show\Field|Collection deleted_at(string $label = null)
     * @method Show\Field|Collection order_amount(string $label = null)
     * @method Show\Field|Collection order_no(string $label = null)
     * @method Show\Field|Collection paid_at(string $label = null)
     * @method Show\Field|Collection pay_method(string $label = null)
     * @method Show\Field|Collection payment_no(string $label = null)
     * @method Show\Field|Collection refund_at(string $label = null)
     * @method Show\Field|Collection refund_no(string $label = null)
     * @method Show\Field|Collection refund_status(string $label = null)
     * @method Show\Field|Collection connection(string $label = null)
     * @method Show\Field|Collection exception(string $label = null)
     * @method Show\Field|Collection failed_at(string $label = null)
     * @method Show\Field|Collection payload(string $label = null)
     * @method Show\Field|Collection queue(string $label = null)
     * @method Show\Field|Collection uuid(string $label = null)
     * @method Show\Field|Collection check_time(string $label = null)
     * @method Show\Field|Collection images(string $label = null)
     * @method Show\Field|Collection temperature(string $label = null)
     * @method Show\Field|Collection ym_count(string $label = null)
     * @method Show\Field|Collection check_img(string $label = null)
     * @method Show\Field|Collection path(string $label = null)
     * @method Show\Field|Collection belong_id(string $label = null)
     * @method Show\Field|Collection belong_type(string $label = null)
     * @method Show\Field|Collection lat(string $label = null)
     * @method Show\Field|Collection lng(string $label = null)
     * @method Show\Field|Collection time(string $label = null)
     * @method Show\Field|Collection platform(string $label = null)
     * @method Show\Field|Collection is_important(string $label = null)
     * @method Show\Field|Collection is_settled(string $label = null)
     * @method Show\Field|Collection notify_id(string $label = null)
     * @method Show\Field|Collection app_key(string $label = null)
     * @method Show\Field|Collection appointment_end_time(string $label = null)
     * @method Show\Field|Collection appointment_start_time(string $label = null)
     * @method Show\Field|Collection arrive_at(string $label = null)
     * @method Show\Field|Collection buy_imgs(string $label = null)
     * @method Show\Field|Collection category_id(string $label = null)
     * @method Show\Field|Collection close_reason(string $label = null)
     * @method Show\Field|Collection coupon_amount(string $label = null)
     * @method Show\Field|Collection coupon_id(string $label = null)
     * @method Show\Field|Collection deliver_address(string $label = null)
     * @method Show\Field|Collection deliver_address_id(string $label = null)
     * @method Show\Field|Collection deliver_lat(string $label = null)
     * @method Show\Field|Collection deliver_lng(string $label = null)
     * @method Show\Field|Collection deliver_name(string $label = null)
     * @method Show\Field|Collection deliver_phone(string $label = null)
     * @method Show\Field|Collection deliver_region_id(string $label = null)
     * @method Show\Field|Collection dispatch_status(string $label = null)
     * @method Show\Field|Collection distance(string $label = null)
     * @method Show\Field|Collection distance_price(string $label = null)
     * @method Show\Field|Collection estimated_delivery_time(string $label = null)
     * @method Show\Field|Collection finish_time(string $label = null)
     * @method Show\Field|Collection freight(string $label = null)
     * @method Show\Field|Collection goods_category_id(string $label = null)
     * @method Show\Field|Collection goods_desc(string $label = null)
     * @method Show\Field|Collection goods_imgs(string $label = null)
     * @method Show\Field|Collection goods_price(string $label = null)
     * @method Show\Field|Collection goods_protected_price(string $label = null)
     * @method Show\Field|Collection gratuity(string $label = null)
     * @method Show\Field|Collection hide_address(string $label = null)
     * @method Show\Field|Collection is_special(string $label = null)
     * @method Show\Field|Collection is_trans(string $label = null)
     * @method Show\Field|Collection need_incubator(string $label = null)
     * @method Show\Field|Collection order_status(string $label = null)
     * @method Show\Field|Collection ori_rider_id(string $label = null)
     * @method Show\Field|Collection out_order_no(string $label = null)
     * @method Show\Field|Collection pickup_address(string $label = null)
     * @method Show\Field|Collection pickup_address_id(string $label = null)
     * @method Show\Field|Collection pickup_at(string $label = null)
     * @method Show\Field|Collection pickup_code(string $label = null)
     * @method Show\Field|Collection pickup_code_mode(string $label = null)
     * @method Show\Field|Collection pickup_lat(string $label = null)
     * @method Show\Field|Collection pickup_lng(string $label = null)
     * @method Show\Field|Collection pickup_name(string $label = null)
     * @method Show\Field|Collection pickup_phone(string $label = null)
     * @method Show\Field|Collection pickup_region_id(string $label = null)
     * @method Show\Field|Collection receipt_time(string $label = null)
     * @method Show\Field|Collection receive_code(string $label = null)
     * @method Show\Field|Collection receive_code_mode(string $label = null)
     * @method Show\Field|Collection refund_amount(string $label = null)
     * @method Show\Field|Collection reward_amount_full(string $label = null)
     * @method Show\Field|Collection reward_amount_part(string $label = null)
     * @method Show\Field|Collection time_price(string $label = null)
     * @method Show\Field|Collection volume(string $label = null)
     * @method Show\Field|Collection weather_price(string $label = null)
     * @method Show\Field|Collection weight(string $label = null)
     * @method Show\Field|Collection weight_price(string $label = null)
     * @method Show\Field|Collection address_id(string $label = null)
     * @method Show\Field|Collection app_secret(string $label = null)
     * @method Show\Field|Collection callback_url(string $label = null)
     * @method Show\Field|Collection price(string $label = null)
     * @method Show\Field|Collection spu_cover(string $label = null)
     * @method Show\Field|Collection spu_name(string $label = null)
     * @method Show\Field|Collection cdgf_star(string $label = null)
     * @method Show\Field|Collection hpwh_star(string $label = null)
     * @method Show\Field|Collection imgs(string $label = null)
     * @method Show\Field|Collection is_anonymous(string $label = null)
     * @method Show\Field|Collection is_satisfied(string $label = null)
     * @method Show\Field|Collection lmrq_star(string $label = null)
     * @method Show\Field|Collection pszs_star(string $label = null)
     * @method Show\Field|Collection ybzj_star(string $label = null)
     * @method Show\Field|Collection amount(string $label = null)
     * @method Show\Field|Collection no(string $label = null)
     * @method Show\Field|Collection refund_pics(string $label = null)
     * @method Show\Field|Collection refund_reason(string $label = null)
     * @method Show\Field|Collection refund_remark(string $label = null)
     * @method Show\Field|Collection reject_reason(string $label = null)
     * @method Show\Field|Collection spu_info(string $label = null)
     * @method Show\Field|Collection verify_at(string $label = null)
     * @method Show\Field|Collection is_tel_protect(string $label = null)
     * @method Show\Field|Collection pay_amount(string $label = null)
     * @method Show\Field|Collection receiver_address(string $label = null)
     * @method Show\Field|Collection receiver_area(string $label = null)
     * @method Show\Field|Collection receiver_name(string $label = null)
     * @method Show\Field|Collection receiver_tel(string $label = null)
     * @method Show\Field|Collection reduce_amount(string $label = null)
     * @method Show\Field|Collection refund_type(string $label = null)
     * @method Show\Field|Collection sending_end_time(string $label = null)
     * @method Show\Field|Collection sending_start_time(string $label = null)
     * @method Show\Field|Collection email(string $label = null)
     * @method Show\Field|Collection token(string $label = null)
     * @method Show\Field|Collection abilities(string $label = null)
     * @method Show\Field|Collection expires_at(string $label = null)
     * @method Show\Field|Collection last_used_at(string $label = null)
     * @method Show\Field|Collection tokenable_id(string $label = null)
     * @method Show\Field|Collection tokenable_type(string $label = null)
     * @method Show\Field|Collection expire_time(string $label = null)
     * @method Show\Field|Collection phone_a(string $label = null)
     * @method Show\Field|Collection phone_x(string $label = null)
     * @method Show\Field|Collection subsid(string $label = null)
     * @method Show\Field|Collection purchase_time(string $label = null)
     * @method Show\Field|Collection region(string $label = null)
     * @method Show\Field|Collection vendor(string $label = null)
     * @method Show\Field|Collection base_price(string $label = null)
     * @method Show\Field|Collection period_price(string $label = null)
     * @method Show\Field|Collection code(string $label = null)
     * @method Show\Field|Collection is_hot(string $label = null)
     * @method Show\Field|Collection letter(string $label = null)
     * @method Show\Field|Collection level(string $label = null)
     * @method Show\Field|Collection pinyin(string $label = null)
     * @method Show\Field|Collection citycode(string $label = null)
     * @method Show\Field|Collection dp_id(string $label = null)
     * @method Show\Field|Collection is_show(string $label = null)
     * @method Show\Field|Collection mername(string $label = null)
     * @method Show\Field|Collection mt_id(string $label = null)
     * @method Show\Field|Collection qyh_id(string $label = null)
     * @method Show\Field|Collection sname(string $label = null)
     * @method Show\Field|Collection st_id(string $label = null)
     * @method Show\Field|Collection ww_id(string $label = null)
     * @method Show\Field|Collection yzcode(string $label = null)
     * @method Show\Field|Collection after_amount(string $label = null)
     * @method Show\Field|Collection before_amount(string $label = null)
     * @method Show\Field|Collection business_type(string $label = null)
     * @method Show\Field|Collection chg_amount(string $label = null)
     * @method Show\Field|Collection user_account_id(string $label = null)
     * @method Show\Field|Collection account_type(string $label = null)
     * @method Show\Field|Collection card_background(string $label = null)
     * @method Show\Field|Collection max_score(string $label = null)
     * @method Show\Field|Collection trans_limit(string $label = null)
     * @method Show\Field|Collection children_count(string $label = null)
     * @method Show\Field|Collection edu_background(string $label = null)
     * @method Show\Field|Collection emergency_contact(string $label = null)
     * @method Show\Field|Collection emergency_mobile(string $label = null)
     * @method Show\Field|Collection health_cert_cover(string $label = null)
     * @method Show\Field|Collection health_cert_expire_date(string $label = null)
     * @method Show\Field|Collection health_cert_no(string $label = null)
     * @method Show\Field|Collection health_cert_obverse(string $label = null)
     * @method Show\Field|Collection health_cert_publisher(string $label = null)
     * @method Show\Field|Collection job(string $label = null)
     * @method Show\Field|Collection marriage(string $label = null)
     * @method Show\Field|Collection transport_type(string $label = null)
     * @method Show\Field|Collection point_key(string $label = null)
     * @method Show\Field|Collection point_type(string $label = null)
     * @method Show\Field|Collection score(string $label = null)
     * @method Show\Field|Collection around_push(string $label = null)
     * @method Show\Field|Collection cids(string $label = null)
     * @method Show\Field|Collection bank_account(string $label = null)
     * @method Show\Field|Collection bank_name(string $label = null)
     * @method Show\Field|Collection real_name(string $label = null)
     * @method Show\Field|Collection trans_message(string $label = null)
     * @method Show\Field|Collection trans_status(string $label = null)
     * @method Show\Field|Collection transfer_time(string $label = null)
     * @method Show\Field|Collection black(string $label = null)
     * @method Show\Field|Collection face_certified(string $label = null)
     * @method Show\Field|Collection health_status(string $label = null)
     * @method Show\Field|Collection id_card_certified(string $label = null)
     * @method Show\Field|Collection last_check_time(string $label = null)
     * @method Show\Field|Collection role(string $label = null)
     * @method Show\Field|Collection transport_status(string $label = null)
     * @method Show\Field|Collection verified(string $label = null)
     * @method Show\Field|Collection cat_id(string $label = null)
     * @method Show\Field|Collection is_signboard(string $label = null)
     * @method Show\Field|Collection sell_tags(string $label = null)
     * @method Show\Field|Collection address_detail(string $label = null)
     * @method Show\Field|Collection community_id(string $label = null)
     * @method Show\Field|Collection logo(string $label = null)
     * @method Show\Field|Collection promotion_info(string $label = null)
     * @method Show\Field|Collection tel(string $label = null)
     * @method Show\Field|Collection points(string $label = null)
     * @method Show\Field|Collection region_name(string $label = null)
     * @method Show\Field|Collection contact_name(string $label = null)
     * @method Show\Field|Collection farthest(string $label = null)
     * @method Show\Field|Collection contact(string $label = null)
     * @method Show\Field|Collection suggestion_cid(string $label = null)
     * @method Show\Field|Collection is_json(string $label = null)
     * @method Show\Field|Collection last_update_user(string $label = null)
     * @method Show\Field|Collection receive_time(string $label = null)
     * @method Show\Field|Collection target_rider_id(string $label = null)
     * @method Show\Field|Collection trans_rider_id(string $label = null)
     * @method Show\Field|Collection card_type(string $label = null)
     * @method Show\Field|Collection image2(string $label = null)
     * @method Show\Field|Collection address_remark(string $label = null)
     * @method Show\Field|Collection area_code(string $label = null)
     * @method Show\Field|Collection county(string $label = null)
     * @method Show\Field|Collection is_default(string $label = null)
     * @method Show\Field|Collection is_white_list(string $label = null)
     * @method Show\Field|Collection auth_id(string $label = null)
     * @method Show\Field|Collection access_token(string $label = null)
     * @method Show\Field|Collection login_type(string $label = null)
     * @method Show\Field|Collection nickname(string $label = null)
     * @method Show\Field|Collection openid(string $label = null)
     * @method Show\Field|Collection union_id(string $label = null)
     * @method Show\Field|Collection birth(string $label = null)
     * @method Show\Field|Collection expiry_date(string $label = null)
     * @method Show\Field|Collection id_card(string $label = null)
     * @method Show\Field|Collection id_card_image(string $label = null)
     * @method Show\Field|Collection id_card_image_over(string $label = null)
     * @method Show\Field|Collection issuing_authority(string $label = null)
     * @method Show\Field|Collection issuing_date(string $label = null)
     * @method Show\Field|Collection nation(string $label = null)
     * @method Show\Field|Collection sex(string $label = null)
     * @method Show\Field|Collection constellation(string $label = null)
     * @method Show\Field|Collection shu_xiang(string $label = null)
     * @method Show\Field|Collection tag(string $label = null)
     * @method Show\Field|Collection rid(string $label = null)
     * @method Show\Field|Collection active(string $label = null)
     * @method Show\Field|Collection sign_date(string $label = null)
     * @method Show\Field|Collection active_paotui(string $label = null)
     * @method Show\Field|Collection active_shequ(string $label = null)
     * @method Show\Field|Collection alipay_account(string $label = null)
     * @method Show\Field|Collection birthday(string $label = null)
     * @method Show\Field|Collection certified(string $label = null)
     * @method Show\Field|Collection channel(string $label = null)
     * @method Show\Field|Collection device_id(string $label = null)
     * @method Show\Field|Collection device_type(string $label = null)
     * @method Show\Field|Collection easemob_activated(string $label = null)
     * @method Show\Field|Collection easemob_uuid(string $label = null)
     * @method Show\Field|Collection last_active_time(string $label = null)
     * @method Show\Field|Collection log_out_pt(string $label = null)
     * @method Show\Field|Collection log_out_sq(string $label = null)
     * @method Show\Field|Collection mp_openid(string $label = null)
     * @method Show\Field|Collection recommend_code(string $label = null)
     * @method Show\Field|Collection scene(string $label = null)
     * @method Show\Field|Collection check_imgs(string $label = null)
     * @method Show\Field|Collection last_vaccine_time(string $label = null)
     * @method Show\Field|Collection vaccine_times(string $label = null)
     * @method Show\Field|Collection app_type(string $label = null)
     * @method Show\Field|Collection current(string $label = null)
     * @method Show\Field|Collection download_path(string $label = null)
     * @method Show\Field|Collection system(string $label = null)
     */
    class Show {}

    /**
     
     */
    class Form {}

}

namespace Dcat\Admin\Grid {
    /**
     
     */
    class Column {}

    /**
     
     */
    class Filter {}
}

namespace Dcat\Admin\Show {
    /**
     
     */
    class Field {}
}
