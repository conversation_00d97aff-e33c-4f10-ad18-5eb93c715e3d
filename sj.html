<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骑手APP - 多路径规划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-8">骑手APP多路径规划功能预览</h1>
        
        <!-- 页面1: 路径概览页 -->
        <div class="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white inline-block w-[360px] h-[640px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-800 text-white p-2 flex justify-between items-center text-xs">
                <span>10:30</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <span class="ml-4 font-medium text-gray-800">多任务路径规划</span>
                </div>
                <i class="fas fa-ellipsis-v text-gray-600"></i>
            </div>
            
            <!-- 简易地图预览 -->
            <div class="relative h-64 bg-blue-50">
                <img src="https://source.unsplash.com/random/360x256/?map" alt="路径地图" class="w-full h-full object-cover">
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="bg-white/80 backdrop-blur-sm rounded-lg p-3 text-center shadow-lg">
                        <p class="text-gray-800">地图加载中...</p>
                        <p class="text-xs text-gray-500 mt-1">共 5 个配送点</p>
                    </div>
                </div>
                <div class="absolute bottom-4 right-4">
                    <button class="bg-white rounded-full p-2 shadow-lg text-blue-600">
                        <i class="fas fa-location-crosshairs"></i>
                    </button>
                </div>
            </div>
            
            <!-- 路径信息统计 -->
            <div class="p-4 bg-white border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="font-semibold text-gray-800">最优路径</h2>
                        <p class="text-sm text-gray-500">总距离: 8.5 公里</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-blue-600">5 个任务</p>
                        <p class="text-sm text-gray-500">预计: 45 分钟</p>
                    </div>
                </div>
            </div>
            
            <!-- 任务列表 -->
            <div class="bg-white overflow-y-auto" style="height: 320px;">
                <!-- 任务1 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">1</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                        <p class="text-xs text-gray-500">取件 · 1.2km</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务2 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">2</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">滨湖天地购物中心</p>
                        <p class="text-xs text-gray-500">送件 · 1.5km</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务3 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">3</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">余杭区创景路城北商场</p>
                        <p class="text-xs text-gray-500">取件 · 3.8km</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务4 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">4</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">西湖区文三路科技大厦</p>
                        <p class="text-xs text-gray-500">送件 · 1.8km</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务5 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">5</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">西湖区黄龙商圈</p>
                        <p class="text-xs text-gray-500">送件 · 0.6km</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div class="absolute bottom-0 left-0 right-0 flex justify-between border-t border-gray-200 bg-white p-3">
                <button class="flex-1 py-2 px-4 bg-gray-100 text-gray-700 rounded-l-lg font-medium flex items-center justify-center">
                    <i class="fas fa-edit mr-2"></i>调整顺序
                </button>
                <button class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-r-lg font-medium flex items-center justify-center">
                    <i class="fas fa-play mr-2"></i>开始导航
                </button>
            </div>
        </div>
        
        <!-- 页面2: 路径详情页 -->
        <div class="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white inline-block w-[360px] h-[640px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-800 text-white p-2 flex justify-between items-center text-xs">
                <span>10:31</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <span class="ml-4 font-medium text-gray-800">路径详情</span>
                </div>
                <i class="fas fa-share-alt text-gray-600"></i>
            </div>
            
            <!-- 地图导航预览 -->
            <div class="relative h-56 bg-blue-50">
                <img src="https://source.unsplash.com/random/360x224/?navigation" alt="导航地图" class="w-full h-full object-cover">
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                    <p class="text-white font-medium">当前路段: 1.2 公里</p>
                    <p class="text-white/80 text-sm">总路程: 8.5 公里 · 预计剩余: 42 分钟</p>
                </div>
                <div class="absolute top-4 right-4 flex flex-col gap-2">
                    <button class="bg-white rounded-full p-2 shadow-lg text-blue-600">
                        <i class="fas fa-location-crosshairs"></i>
                    </button>
                    <button class="bg-white rounded-full p-2 shadow-lg text-gray-600">
                        <i class="fas fa-layer-group"></i>
                    </button>
                </div>
            </div>
            
            <!-- 路段导航提示 -->
            <div class="p-4 bg-white border-b border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center">
                        <i class="fas fa-route text-2xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="font-semibold text-gray-800">前往第一个任务点</h2>
                        <p class="text-sm text-gray-500">沿农林路向东行驶 450 米</p>
                    </div>
                </div>
            </div>
            
            <!-- 路段详情列表 -->
            <div class="bg-white overflow-y-auto" style="height: 280px;">
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-green-600 text-white flex items-center justify-center">
                            <i class="fas fa-walking text-xs"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">当前位置</p>
                                <span class="text-xs text-gray-500">0.0km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">杭州市临安区临天路328号</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-300 my-2">
                        <p class="text-xs text-gray-500 py-1">直行 450 米，右转</p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center text-xs font-bold">
                            1
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                                <span class="text-xs text-gray-500">1.2km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">取件 · 任务#2839</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-300 my-2">
                        <p class="text-xs text-gray-500 py-1">左转，沿湖滨路行驶 1.5 公里</p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center text-xs font-bold">
                            2
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">滨湖天地购物中心</p>
                                <span class="text-xs text-gray-500">1.5km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">送件 · 任务#2840</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-300 my-2">
                        <p class="text-xs text-gray-500 py-1">右转，进入城北大道 3.8 公里</p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center text-xs font-bold">
                            3
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">余杭区创景路城北商场</p>
                                <span class="text-xs text-gray-500">3.8km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">取件 · 任务#2842</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-300 my-2">
                        <p class="text-xs text-gray-500 py-1">继续直行 1.8 公里</p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center text-xs font-bold">
                            4
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">西湖区文三路科技大厦</p>
                                <span class="text-xs text-gray-500">1.8km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">送件 · 任务#2845</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-300 my-2">
                        <p class="text-xs text-gray-500 py-1">右转，沿黄龙路行驶 0.6 公里</p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center text-xs font-bold">
                            5
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">西湖区黄龙商圈</p>
                                <span class="text-xs text-gray-500">0.6km</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">送件 · 任务#2846</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div class="absolute bottom-0 left-0 right-0 flex border-t border-gray-200 bg-white p-3">
                <button class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg font-medium flex items-center justify-center">
                    <i class="fas fa-navigation mr-2"></i>开始导航
                </button>
            </div>
        </div>
        
        <!-- 页面3: 路径调整页 -->
        <div class="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white inline-block w-[360px] h-[640px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-800 text-white p-2 flex justify-between items-center text-xs">
                <span>10:32</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <span class="ml-4 font-medium text-gray-800">调整任务顺序</span>
                </div>
                <div class="flex items-center">
                    <button class="text-blue-600 text-sm font-medium">重置</button>
                </div>
            </div>
            
            <!-- 小地图预览 -->
            <div class="relative h-40 bg-blue-50">
                <img src="https://source.unsplash.com/random/360x160/?map" alt="路径地图" class="w-full h-full object-cover">
                <div class="absolute top-2 right-2">
                    <button class="bg-white rounded-full p-2 shadow-lg text-blue-600">
                        <i class="fas fa-expand-alt text-sm"></i>
                    </button>
                </div>
            </div>
            
            <!-- 调整结果信息 -->
            <div class="p-4 bg-white border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="font-semibold text-gray-800">已调整路径</h2>
                        <p class="text-sm text-gray-500">总距离: 9.2 公里</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-blue-600">5 个任务</p>
                        <p class="text-sm text-gray-500">预计: 50 分钟</p>
                    </div>
                </div>
                <div class="mt-2 p-2 bg-yellow-50 rounded-md border border-yellow-200">
                    <p class="text-xs text-yellow-700 flex items-center">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        与最优路径相比多行驶 0.7 公里，耗时增加 5 分钟
                    </p>
                </div>
            </div>
            
            <!-- 可拖拽任务列表 -->
            <div class="bg-white h-[300px] overflow-y-auto">
                <!-- 任务1 (当前拖动中) -->
                <div class="p-3 border-b border-gray-100 flex items-center bg-blue-50 shadow-md">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">3</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">余杭区创景路城北商场</p>
                        <p class="text-xs text-gray-500">取件 · 任务#2842</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-grip-lines text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务2 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">1</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                        <p class="text-xs text-gray-500">取件 · 任务#2839</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-grip-lines text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务3 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">2</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">滨湖天地购物中心</p>
                        <p class="text-xs text-gray-500">送件 · 任务#2840</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-grip-lines text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 提示放置位置 -->
                <div class="border-2 border-dashed border-blue-300 rounded-md mx-3 my-2 p-2 bg-blue-50/50 flex items-center justify-center">
                    <p class="text-blue-500 text-sm">放置任务到此处</p>
                </div>
                
                <!-- 任务4 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">4</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">西湖区文三路科技大厦</p>
                        <p class="text-xs text-gray-500">送件 · 任务#2845</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-grip-lines text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 任务5 -->
                <div class="p-3 border-b border-gray-100 flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">5</div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-800">西湖区黄龙商圈</p>
                        <p class="text-xs text-gray-500">送件 · 任务#2846</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-grip-lines text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div class="absolute bottom-0 left-0 right-0 flex border-t border-gray-200 bg-white p-3">
                <button class="flex-1 mr-2 py-2 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium">
                    取消
                </button>
                <button class="flex-1 ml-2 py-2 px-4 bg-blue-600 text-white rounded-lg font-medium">
                    确认调整
                </button>
            </div>
        </div>
        
        <!-- 页面4: 导航页面 -->
        <div class="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white inline-block w-[360px] h-[640px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-800 text-white p-2 flex justify-between items-center text-xs">
                <span>10:35</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 全屏导航地图 -->
            <div class="relative h-full w-full bg-blue-50">
                <img src="https://source.unsplash.com/random/360x640/?navigation,map" alt="实时导航" class="w-full h-full object-cover">
                
                <!-- 顶部信息栏 -->
                <div class="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/60 to-transparent p-4">
                    <div class="flex justify-between items-center">
                        <button class="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                            <i class="fas fa-chevron-left text-gray-800"></i>
                        </button>
                        <div class="bg-white/90 backdrop-blur-sm rounded-full py-1 px-3 shadow-lg">
                            <p class="text-xs font-medium text-gray-800">任务 1/5</p>
                        </div>
                        <button class="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                            <i class="fas fa-list text-gray-800"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 导航指示控件 -->
                <div class="absolute top-20 left-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-location-arrow text-blue-600 text-lg"></i>
                        <p class="text-xs font-semibold text-gray-800 mt-1">北</p>
                    </div>
                </div>
                
                <!-- 右侧控制栏 -->
                <div class="absolute top-1/4 right-4 flex flex-col gap-3">
                    <button class="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                        <i class="fas fa-plus text-gray-800"></i>
                    </button>
                    <button class="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                        <i class="fas fa-minus text-gray-800"></i>
                    </button>
                    <button class="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                        <i class="fas fa-location-crosshairs text-blue-600"></i>
                    </button>
                </div>
                
                <!-- 底部导航提示条 -->
                <div class="absolute bottom-24 left-0 right-0 px-4">
                    <div class="bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg">
                        <div class="flex items-center mb-3">
                            <div class="bg-blue-600 rounded-full p-2 mr-3">
                                <i class="fas fa-directions text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800">右转进入湖滨路</h3>
                                <p class="text-sm text-gray-600">前方 300 米</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-gray-800">距离目的地</p>
                                <p class="text-xs text-gray-600">1.2 公里 · 预计 5 分钟</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                                <p class="text-xs text-gray-600">取件 · 任务#2839</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部控制栏 -->
                <div class="absolute bottom-0 left-0 right-0 p-4 bg-white/90 backdrop-blur-sm">
                    <div class="flex justify-between items-center">
                        <button class="bg-white rounded-full p-3 shadow border border-gray-200">
                            <i class="fas fa-volume-up text-gray-800"></i>
                        </button>
                        <button class="flex-1 mx-3 py-3 bg-blue-600 text-white font-medium rounded-lg">
                            任务详情
                        </button>
                        <button class="bg-white rounded-full p-3 shadow border border-gray-200">
                            <i class="fas fa-phone text-gray-800"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面5: 设置页面 -->
        <div class="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white inline-block w-[360px] h-[640px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-800 text-white p-2 flex justify-between items-center text-xs">
                <span>10:36</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <span class="ml-4 font-medium text-gray-800">路径规划设置</span>
                </div>
                <i class="fas fa-question-circle text-gray-600"></i>
            </div>
            
            <!-- 设置内容 -->
            <div class="bg-gray-50 h-[576px] overflow-y-auto">
                <!-- 路径优先级设置 -->
                <div class="p-4 bg-white mb-3">
                    <h3 class="text-gray-800 font-medium mb-4">路径优先级</h3>
                    
                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">时间优先 (推荐)</span>
                        </label>
                        <p class="mt-1 text-xs text-gray-500 ml-6">优先考虑配送时间，可能会增加行驶距离</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" class="h-4 w-4 text-blue-600" checked>
                            <span class="ml-2 text-sm text-gray-700">距离优先</span>
                        </label>
                        <p class="mt-1 text-xs text-gray-500 ml-6">优先考虑最短行驶距离，可能会增加总配送时间</p>
                    </div>
                    
                    <div>
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">平衡模式</span>
                        </label>
                        <p class="mt-1 text-xs text-gray-500 ml-6">平衡考虑时间和距离因素</p>
                    </div>
                </div>
                
                <!-- 任务优先级设置 -->
                <div class="p-4 bg-white mb-3">
                    <h3 class="text-gray-800 font-medium mb-4">任务优先级</h3>
                    
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-sm text-gray-700">优先配送特殊商品</p>
                            <p class="text-xs text-gray-500">如易碎、冷藏等需优先配送的商品</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-sm text-gray-700">优先取货后再送货</p>
                            <p class="text-xs text-gray-500">先完成所有取件任务再进行送件任务</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-700">优先即将超时订单</p>
                            <p class="text-xs text-gray-500">优先处理接近配送时限的订单</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
                
                <!-- 导航设置 -->
                <div class="p-4 bg-white mb-3">
                    <h3 class="text-gray-800 font-medium mb-4">导航设置</h3>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-700 mb-2">首选导航模式</p>
                        <div class="flex gap-2">
                            <button class="py-2 px-3 bg-blue-600 text-white text-sm rounded-md">
                                骑行
                            </button>
                            <button class="py-2 px-3 bg-gray-100 text-gray-700 text-sm rounded-md">
                                驾车
                            </button>
                            <button class="py-2 px-3 bg-gray-100 text-gray-700 text-sm rounded-md">
                                步行
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-700">避开高速公路</p>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" value="" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-700">避开拥堵道路</p>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" value="" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 其他设置 -->
                <div class="p-4 bg-white mb-3">
                    <h3 class="text-gray-800 font-medium mb-4">其他设置</h3>
                    
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-sm text-gray-700">自动更新路径</p>
                            <p class="text-xs text-gray-500">根据实时路况自动更新最优路径</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-700">语音提示</p>
                            <p class="text-xs text-gray-500">导航过程中语音播报提示信息</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
                
                <!-- 确认按钮 -->
                <div class="p-4">
                    <button class="w-full py-3 bg-blue-600 text-white font-medium rounded-lg">
                        保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 