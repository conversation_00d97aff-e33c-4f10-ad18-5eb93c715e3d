{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "ext-bcmath": "*", "alibabacloud/dyplsapi-20170525": "1.0.5", "dcat/easy-excel": "^1.1", "dcat/laravel-admin": "2.*", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "ixudra/curl": "^6.22", "laravel/framework": "^9.19", "laravel/horizon": "^5.10", "laravel/octane": "^1.3", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "link1st/laravel-easemob": "^0.1.1", "maatwebsite/excel": "^3.1", "maniac/easemob-php": "^1.0", "mews/captcha": "^3.3", "mitoop/laravel-jpush": "^9.0", "overtrue/easy-sms": "^2.2", "overtrue/laravel-lang": "^6.0", "overtrue/laravel-socialite": "^4.0", "overtrue/laravel-wechat": "^7.2", "overtrue/pinyin": "^5.0", "overtrue/socialite": "^4.8", "php-open-source-saver/jwt-auth": "^2.0", "phpoffice/phpspreadsheet": "^1.27", "predis/predis": "^2.0", "propaganistas/laravel-phone": "^4.3", "psr/simple-cache": "^2.0", "simplesoftwareio/simple-qrcode": "^4.2", "wangju/ding-notice": "^1.0", "yansongda/laravel-pay": "~3.2.0", "yansongda/pay": "~3.2.0", "zgldh/qiniu-laravel-storage": "^9.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}