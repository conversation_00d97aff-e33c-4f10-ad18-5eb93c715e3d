<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>跑腿APP - 多单规划交互原型</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            max-width: 414px;
            margin: 0 auto;
            position: relative;
        }
        
        .page-title {
            text-align: center;
            font-size: 16px;
            padding: 15px 0;
            background-color: #fff;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        section {
            margin-bottom: 20px;
            border: 1px dashed #ccc;
            padding: 10px;
            position: relative;
        }
        
        .state-label {
            position: absolute;
            top: -10px;
            left: 10px;
            background-color: #ff6b01;
            color: white;
            padding: 2px 8px;
            font-size: 12px;
            border-radius: 4px;
        }
        
        h2 {
            margin: 10px 0;
            font-size: 16px;
            color: #666;
        }
        
        /* 选项卡导航 */
        .tab-nav {
            display: flex;
            background-color: #fff;
            padding: 10px 15px;
            margin-bottom: 10px;
            position: relative;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 5px 0;
            color: #999;
            position: relative;
        }
        
        .tab-item.active {
            color: #ff6b01;
            font-weight: bold;
        }
        
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #ff6b01;
        }
        
        /* 智能规划按钮样式 */
        .smart-plan-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 4px 10px;
            font-size: 12px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 5px rgba(24, 144, 255, 0.3);
        }
        
        .smart-plan-btn .icon {
            margin-right: 3px;
            font-size: 12px;
        }
        
        .notification-dot {
            position: absolute;
            top: -3px;
            right: -3px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #f5222d;
        }
        
        /* 智能规划面板 */
        .smart-plan-panel {
            background-color: #fff;
            border-radius: 8px;
            margin: 0 10px 15px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
        }
        
        .smart-plan-panel.active {
            display: block;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .plan-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .plan-title {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        
        .plan-savings {
            color: #ff6b01;
            font-size: 13px;
        }
        
        .plan-body {
            padding: 15px;
        }
        
        /* 多单总览视图样式 */
        .order-summary {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .stats-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
        }
        
        .stat-label {
            font-size: 12px;
            color: #999;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #ff6b01;
        }
        
        .view-switch {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }
        
        .view-switch button {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 0;
            font-size: 14px;
            color: #666;
        }
        
        .view-switch button.active {
            color: #ff6b01;
            border-bottom: 2px solid #ff6b01;
        }
        
        .map-view, .list-view, .timeline-view {
            height: 180px;
            background-color: #eee;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .view-content {
            display: none;
        }
        
        .view-content.active {
            display: flex;
        }
        
        /* 订单卡片样式 */
        .order-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .order-type {
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .order-time {
            color: #ff6b01;
            font-size: 12px;
        }
        
        .order-address {
            display: flex;
            margin-bottom: 8px;
        }
        
        .address-icon {
            width: 20px;
            height: 20px;
            background-color: #e6f7ff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .pickup-icon {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .delivery-icon {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .address-content {
            flex: 1;
        }
        
        .address-name {
            font-weight: bold;
        }
        
        .address-detail {
            font-size: 12px;
            color: #999;
        }
        
        /* 拖拽调整样式 */
        .drag-handle {
            width: 30px;
            height: 30px;
            background-color: #f5f5f5;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            cursor: move;
        }
        
        .optimize-button {
            display: block;
            width: 100%;
            background-color: #ff6b01;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 0;
            margin-top: 15px;
            font-size: 16px;
            cursor: pointer;
        }
        
        /* 导航视图样式 */
        .navigation-view {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            position: relative;
        }
        
        .current-destination {
            margin-bottom: 15px;
        }
        
        .destination-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .distance-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .distance-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b01;
            margin-right: 10px;
        }
        
        .time-value {
            font-size: 16px;
            color: #666;
        }
        
        .navigation-map {
            height: 200px;
            background-color: #eee;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .next-order {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
        }
        
        .next-order-title {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }
        
        /* 状态更新操作样式 */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .action-button {
            flex: 1;
            padding: 12px 0;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .pickup-button {
            background-color: #1890ff;
            color: white;
        }
        
        .delivery-button {
            background-color: #52c41a;
            color: white;
        }
        
        .report-button {
            background-color: #f5f5f5;
            color: #666;
        }
        
        /* 导航栏样式 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            max-width: 414px;
            background-color: white;
            display: flex;
            border-top: 1px solid #eee;
            z-index: 100;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            color: #999;
            font-size: 12px;
            cursor: pointer;
        }
        
        .nav-item.active {
            color: #ff6b01;
        }
        
        .nav-icon {
            display: block;
            font-size: 20px;
            margin-bottom: 2px;
        }
        
        .app-content {
            padding-bottom: 60px;
        }
        
        /* 状态切换 */
        .app-page {
            display: none;
        }
        
        .app-page.active {
            display: block;
        }
        
        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        /* 加载状态 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #eee;
            border-top: 3px solid #ff6b01;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 消息提示 */
        .toast-message {
            position: fixed;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
            display: none;
        }
        
        /* 弹出的订单详情 */
        .order-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: flex-end;
            z-index: 1001;
            display: none;
        }
        
        .order-detail-content {
            width: 100%;
            max-width: 414px;
            background-color: white;
            border-radius: 12px 12px 0 0;
            padding: 20px;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 20px;
            color: #999;
        }
        
        .detail-header {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .detail-status {
            flex: 1;
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .detail-info {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .detail-label {
            width: 80px;
            color: #999;
        }
        
        .detail-value {
            flex: 1;
        }
        
        .detail-qrcode {
            text-align: center;
            margin: 15px 0;
        }
        
        .qrcode-img {
            width: 120px;
            height: 120px;
            background-color: #f5f5f5;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 新增智能规划菜单 */
        .smart-plan-menu {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .plan-option {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 13px;
            color: #666;
            position: relative;
        }
        
        .plan-option.active {
            color: #1890ff;
            font-weight: bold;
        }
        
        .plan-option.active:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 20%;
            width: 60%;
            height: 2px;
            background-color: #1890ff;
        }
        
        .apply-plan-btn {
            display: block;
            width: 100%;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 0;
            margin-top: 15px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
    
    <div class="toast-message"></div>
    
    <div class="order-detail-modal">
        <div class="order-detail-content">
            <div class="modal-close">×</div>
            <div class="detail-header">
                <div class="detail-status">
                    <div class="detail-title">鸡排小店 (取餐点)</div>
                    <div>订单编号：12345678</div>
                </div>
                <div class="order-type">取餐</div>
            </div>
            
            <div class="detail-info">
                <div class="detail-row">
                    <div class="detail-label">联系人：</div>
                    <div class="detail-value">张师傅 13800138000</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">地址：</div>
                    <div class="detail-value">科技路15号时代广场1楼108铺</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">取餐时间：</div>
                    <div class="detail-value">今日 12:30前</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">备注：</div>
                    <div class="detail-value">请报取餐码：A138</div>
                </div>
            </div>
            
            <div class="detail-qrcode">
                <div>取餐码</div>
                <div class="qrcode-img">取餐码二维码</div>
            </div>
            
            <button class="action-button pickup-button">确认已取餐</button>
        </div>
    </div>
    
    <h1 class="page-title">跑腿APP多单规划交互原型</h1>
    
    <!-- 添加选项卡导航 -->
    <div class="tab-nav">
        <div class="tab-item">新任务</div>
        <div class="tab-item active">进行中</div>
        
        <!-- 智能规划按钮 -->
        <button class="smart-plan-btn" id="smartPlanBtn">
            <span class="icon">📊</span>
            <span>智能规划</span>
            <span class="notification-dot"></span>
        </button>
    </div>
    
    <!-- 智能规划面板 -->
    <div class="smart-plan-panel" id="smartPlanPanel">
        <div class="plan-header">
            <div class="plan-title">智能配送规划</div>
            <div class="plan-savings">预计节省8分钟</div>
        </div>
        
        <div class="plan-body">
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-label">总订单</div>
                    <div class="stat-value">2</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">总距离</div>
                    <div class="stat-value">3.4km</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">预计时间</div>
                    <div class="stat-value">28分钟</div>
                </div>
            </div>
            
            <div class="smart-plan-menu">
                <div class="plan-option active">智能推荐</div>
                <div class="plan-option">距离优先</div>
                <div class="plan-option">时间优先</div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-type">取货</div>
                    <div class="order-time">已超时</div>
                </div>
                <div class="order-address">
                    <div class="address-icon pickup-icon">①</div>
                    <div class="address-content">
                        <div class="address-name">浙江省杭州市临安区滨湖天地购物中心</div>
                        <div class="address-detail">801</div>
                    </div>
                </div>
            </div>
            
            <div class="order-card">
                <div class="order-header">
                    <div class="order-type">送货</div>
                    <div class="order-time">已超时</div>
                </div>
                <div class="order-address">
                    <div class="address-icon delivery-icon">②</div>
                    <div class="address-content">
                        <div class="address-name">浙江省杭州市余杭区洋东新城昆明路与天台路交汇处西北角</div>
                    </div>
                </div>
            </div>
            
            <button class="apply-plan-btn">应用规划路线</button>
        </div>
    </div>
    
    <div class="app-content">
        <!-- 多单总览视图 -->
        <div class="app-page active" id="page-overview">
            <section>
                <span class="state-label">状态1</span>
                <h2>多单总览</h2>
                
                <div class="order-summary">
                    <div class="stats-bar">
                        <div class="stat-item">
                            <div class="stat-label">总订单</div>
                            <div class="stat-value">3</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">总距离</div>
                            <div class="stat-value">5.2km</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">预计时间</div>
                            <div class="stat-value">35分钟</div>
                        </div>
                    </div>
                    
                    <div class="view-switch">
                        <button class="view-btn active" data-view="map-view">地图</button>
                        <button class="view-btn" data-view="list-view">列表</button>
                        <button class="view-btn" data-view="timeline-view">时间轴</button>
                    </div>
                    
                    <div class="map-view view-content active">
                        <div style="width: 100%; height: 100%; position: relative; overflow: hidden; border-radius: 8px;">
                            <div style="position: absolute; top: 5px; right: 5px; z-index: 1; background: white; border-radius: 4px; padding: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                <div style="font-size: 12px; color: #666;">🔍 缩放</div>
                            </div>
                            
                            <!-- 模拟地图背景 -->
                            <div style="width: 100%; height: 100%; background-color: #E8EEF4; position: relative;">
                                <!-- 模拟路线 -->
                                <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                    <path d="M50,150 C70,130 100,120 140,110 L200,80" stroke="#1890ff" stroke-width="3" fill="none" stroke-dasharray="5,3" />
                                    <path d="M140,110 C180,100 200,120 220,160" stroke="#1890ff" stroke-width="3" fill="none" stroke-dasharray="5,3" />
                                </svg>
                                
                                <!-- 模拟地图元素 -->
                                <div style="position: absolute; width: 100%; height: 100%;">
                                    <!-- 道路 -->
                                    <div style="position: absolute; top: 40px; left: 20px; width: 80%; height: 5px; background-color: #ccc;"></div>
                                    <div style="position: absolute; top: 90px; left: 50px; width: 5px; height: 70px; background-color: #ccc;"></div>
                                    <div style="position: absolute; top: 160px; left: 50px; width: 70%; height: 5px; background-color: #ccc;"></div>
                                    
                                    <!-- 位置标记 -->
                                    <div style="position: absolute; top: 50px; left: 50px; width: 20px; height: 20px; background-color: #1890ff; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white; font-size: 12px; border: 2px solid white; box-shadow: 0 1px 3px rgba(0,0,0,0.2);">我</div>
                                    
                                    <div style="position: absolute; top: 150px; left: 140px; width: 20px; height: 20px; background-color: #e6f7ff; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: #1890ff; font-size: 12px; border: 2px solid white; box-shadow: 0 1px 3px rgba(0,0,0,0.2);">1</div>
                                    
                                    <div style="position: absolute; top: 110px; left: 140px; width: 20px; height: 20px; background-color: #e6f7ff; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: #1890ff; font-size: 12px; border: 2px solid white; box-shadow: 0 1px 3px rgba(0,0,0,0.2);">2</div>
                                    
                                    <div style="position: absolute; top: 80px; left: 200px; width: 20px; height: 20px; background-color: #f6ffed; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: #52c41a; font-size: 12px; border: 2px solid white; box-shadow: 0 1px 3px rgba(0,0,0,0.2);">3</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="list-view view-content" style="display: none;">
                        <div style="padding: 20px 0; text-align: center;">
                            按取送顺序排列的订单列表视图
                        </div>
                    </div>
                    
                    <div class="timeline-view view-content" style="display: none;">
                        <div style="height: 150px; display: flex; align-items: center; padding: 0 15px;">
                            <div style="flex: 1; height: 4px; background-color: #eee; position: relative;">
                                <div style="position: absolute; top: -8px; left: 10%; width: 20px; height: 20px; border-radius: 50%; background-color: #e6f7ff; display: flex; justify-content: center; align-items: center; font-size: 10px; color: #1890ff;">取</div>
                                <div style="position: absolute; top: -8px; left: 30%; width: 20px; height: 20px; border-radius: 50%; background-color: #e6f7ff; display: flex; justify-content: center; align-items: center; font-size: 10px; color: #1890ff;">取</div>
                                <div style="position: absolute; top: -8px; left: 70%; width: 20px; height: 20px; border-radius: 50%; background-color: #f6ffed; display: flex; justify-content: center; align-items: center; font-size: 10px; color: #52c41a;">送</div>
                                
                                <div style="position: absolute; bottom: -25px; left: 10%; font-size: 11px; color: #999;">12:15</div>
                                <div style="position: absolute; bottom: -25px; left: 30%; font-size: 11px; color: #999;">12:30</div>
                                <div style="position: absolute; bottom: -25px; left: 70%; font-size: 11px; color: #999;">12:45</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">取餐</div>
                            <div class="order-time">预计12:30出餐</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon pickup-icon">取</div>
                            <div class="address-content">
                                <div class="address-name">鸡排小店</div>
                                <div class="address-detail">科技路15号时代广场1楼108铺</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">送餐</div>
                            <div class="order-time">12:45前送达</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon delivery-icon">送</div>
                            <div class="address-content">
                                <div class="address-name">李先生</div>
                                <div class="address-detail">中关村软件园3号楼B座505</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">取餐</div>
                            <div class="order-time">已出餐</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon pickup-icon">取</div>
                            <div class="address-content">
                                <div class="address-name">麦当劳</div>
                                <div class="address-detail">西三旗桥东商场2楼</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 订单排序/拖拽界面 -->
            <section>
                <span class="state-label">状态2</span>
                <h2>订单排序</h2>
                
                <div class="order-summary">
                    <div class="stats-bar">
                        <div class="stat-item">
                            <div class="stat-label">当前顺序</div>
                            <div class="stat-value">35分钟</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">优化顺序</div>
                            <div class="stat-value">28分钟</div>
                        </div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">取餐</div>
                            <div class="order-time">已出餐</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon pickup-icon">1</div>
                            <div class="address-content">
                                <div class="address-name">麦当劳</div>
                                <div class="address-detail">西三旗桥东商场2楼</div>
                            </div>
                        </div>
                        <div class="drag-handle">≡</div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">取餐</div>
                            <div class="order-time">预计12:30出餐</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon pickup-icon">2</div>
                            <div class="address-content">
                                <div class="address-name">鸡排小店</div>
                                <div class="address-detail">科技路15号时代广场1楼108铺</div>
                            </div>
                        </div>
                        <div class="drag-handle">≡</div>
                    </div>
                    
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-type">送餐</div>
                            <div class="order-time">12:45前送达</div>
                        </div>
                        <div class="order-address">
                            <div class="address-icon delivery-icon">3</div>
                            <div class="address-content">
                                <div class="address-name">李先生</div>
                                <div class="address-detail">中关村软件园3号楼B座505</div>
                            </div>
                        </div>
                        <div class="drag-handle">≡</div>
                    </div>
                    
                    <button class="optimize-button">一键优化顺序</button>
                </div>
            </section>
        </div>
        
        <!-- 导航页面 -->
        <div class="app-page" id="page-navigation">
            <!-- 骑行导航视图 -->
            <section>
                <span class="state-label">状态3</span>
                <h2>骑行导航</h2>
                
                <div class="navigation-view">
                    <div class="current-destination">
                        <div class="destination-title">麦当劳 (取餐)</div>
                        <div class="address-detail">西三旗桥东商场2楼</div>
                        
                        <div class="distance-info">
                            <div class="distance-value">1.2公里</div>
                            <div class="time-value">预计8分钟</div>
                        </div>
                    </div>
                    
                    <div class="navigation-map">
                        <div style="width: 100%; height: 100%; position: relative; overflow: hidden; border-radius: 8px;">
                            <!-- 模拟地图背景 -->
                            <div style="width: 100%; height: 100%; background-color: #E8EEF4; position: relative;">
                                <!-- 模拟路线 -->
                                <svg width="100%" height="100%" style="position: absolute; top: 0; left: 0;">
                                    <path d="M30,100 C50,90 80,80 120,70 L180,60" stroke="#1890ff" stroke-width="4" fill="none" />
                                    <polygon points="180,60 170,55 172,65" fill="#1890ff" />
                                </svg>
                                
                                <!-- 模拟地图元素 -->
                                <div style="position: absolute; width: 100%; height: 100%;">
                                    <!-- 道路 -->
                                    <div style="position: absolute; top: 40px; left: 20px; width: 80%; height: 6px; background-color: #ccc;"></div>
                                    <div style="position: absolute; top: 40px; left: 50px; width: 6px; height: 120px; background-color: #ccc;"></div>
                                    <div style="position: absolute; top: 160px; left: 50px; width: 70%; height: 6px; background-color: #ccc;"></div>
                                    
                                    <!-- 位置标记 -->
                                    <div style="position: absolute; top: 100px; left: 30px; width: 24px; height: 24px; background-color: #1890ff; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white; font-weight: bold; border: 2px solid white; box-shadow: 0 1px 5px rgba(0,0,0,0.3);">我</div>
                                    
                                    <div style="position: absolute; top: 60px; left: 180px; transform: translateX(-50%) translateY(-50%); text-align: center;">
                                        <div style="width: 30px; height: 30px; background-color: #e6f7ff; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: #1890ff; font-weight: bold; margin: 0 auto; border: 3px solid white; box-shadow: 0 1px 5px rgba(0,0,0,0.3);">取</div>
                                        <div style="margin-top: 5px; font-size: 12px; background-color: white; padding: 2px 5px; border-radius: 10px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">麦当劳</div>
                                    </div>
                                    
                                    <!-- 距离指示 -->
                                    <div style="position: absolute; top: 120px; left: 100px; background-color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <span style="color: #1890ff; font-weight: bold;">700m</span> · 5分钟
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 导航控制 -->
                            <div style="position: absolute; bottom: 10px; right: 10px; display: flex; flex-direction: column; gap: 5px;">
                                <div style="width: 30px; height: 30px; background-color: white; border-radius: 50%; display: flex; justify-content: center; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.2); font-size: 18px;">+</div>
                                <div style="width: 30px; height: 30px; background-color: white; border-radius: 50%; display: flex; justify-content: center; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.2); font-size: 18px;">-</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="next-order">
                        <div class="next-order-title">下一站</div>
                        <div class="address-name">鸡排小店 (取餐)</div>
                        <div class="address-detail">科技路15号时代广场1楼108铺</div>
                    </div>
                </div>
            </section>
            
            <!-- 状态更新操作界面 -->
            <section>
                <span class="state-label">状态4</span>
                <h2>状态更新 - 取餐</h2>
                
                <div class="navigation-view">
                    <div class="current-destination">
                        <div class="destination-title">已到达：麦当劳</div>
                        <div class="address-detail">西三旗桥东商场2楼</div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-button pickup-button">已取餐</button>
                        <button class="action-button report-button">报告问题</button>
                    </div>
                </div>
            </section>
            
            <section>
                <span class="state-label">状态5</span>
                <h2>状态更新 - 送达</h2>
                
                <div class="navigation-view">
                    <div class="current-destination">
                        <div class="destination-title">已到达：李先生</div>
                        <div class="address-detail">中关村软件园3号楼B座505</div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-button delivery-button">已送达</button>
                        <button class="action-button report-button">报告问题</button>
                    </div>
                </div>
            </section>
        </div>
        
        <!-- 个人中心页面 -->
        <div class="app-page" id="page-profile">
            <section>
                <div style="padding: 20px; text-align: center;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background-color: #f0f0f0; margin: 0 auto; display: flex; justify-content: center; align-items: center; font-size: 40px; color: #ccc;">
                        👤
                    </div>
                    <div style="margin-top: 10px; font-size: 18px; font-weight: bold;">骑手小王</div>
                    <div style="color: #999; margin-top: 5px;">骑手ID: 12345678</div>
                    
                    <div style="margin-top: 20px; display: flex; justify-content: space-around; text-align: center;">
                        <div>
                            <div style="font-size: 20px; font-weight: bold; color: #ff6b01;">127</div>
                            <div style="font-size: 12px; color: #999;">今日配送</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: bold; color: #ff6b01;">4.9</div>
                            <div style="font-size: 12px; color: #999;">平均评分</div>
                        </div>
                        <div>
                            <div style="font-size: 20px; font-weight: bold; color: #ff6b01;">98%</div>
                            <div style="font-size: 12px; color: #999;">准时率</div>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 20px; background-color: white; border-radius: 8px; overflow: hidden;">
                    <div style="padding: 15px; border-bottom: 1px solid #f5f5f5; display: flex; align-items: center; justify-content: space-between;">
                        <div>我的订单</div>
                        <div style="color: #999; font-size: 12px;">查看全部 ></div>
                    </div>
                    <div style="padding: 15px; border-bottom: 1px solid #f5f5f5; display: flex; align-items: center; justify-content: space-between;">
                        <div>收入统计</div>
                        <div style="color: #999; font-size: 12px;">查看详情 ></div>
                    </div>
                    <div style="padding: 15px; border-bottom: 1px solid #f5f5f5; display: flex; align-items: center; justify-content: space-between;">
                        <div>路线偏好设置</div>
                        <div style="color: #999; font-size: 12px;">></div>
                    </div>
                    <div style="padding: 15px; display: flex; align-items: center; justify-content: space-between;">
                        <div>系统设置</div>
                        <div style="color: #999; font-size: 12px;">></div>
                    </div>
                </div>
            </section>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="bottom-nav">
        <div class="nav-item active" data-page="page-overview">
            <span class="nav-icon">📋</span>
            <span>订单</span>
        </div>
        <div class="nav-item" data-page="page-navigation">
            <span class="nav-icon">🧭</span>
            <span>导航</span>
        </div>
        <div class="nav-item" data-page="page-profile">
            <span class="nav-icon">👤</span>
            <span>我的</span>
        </div>
    </div>
    
    <script>
        // 此处仅为交互原型展示，无实际功能
        document.addEventListener('DOMContentLoaded', function() {
            // 应用底部导航
            const navItems = document.querySelectorAll('.nav-item');
            const appPages = document.querySelectorAll('.app-page');
            
            function showPage(pageId) {
                appPages.forEach(page => {
                    if (page.id === pageId) {
                        page.classList.add('active');
                        page.classList.add('fade-in');
                    } else {
                        page.classList.remove('active');
                        page.classList.remove('fade-in');
                    }
                });
            }
            
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                    
                    const targetPage = this.getAttribute('data-page');
                    showPage(targetPage);
                    showLoading(300); // 模拟切换时的加载效果
                });
            });
            
            // 显示加载状态
            function showLoading(duration = 1000) {
                const loadingOverlay = document.querySelector('.loading-overlay');
                loadingOverlay.style.display = 'flex';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, duration);
            }
            
            // 显示提示消息
            function showToast(message, duration = 2000) {
                const toast = document.querySelector('.toast-message');
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(() => {
                    toast.style.display = 'none';
                }, duration);
            }
            
            // 视图切换按钮
            const viewButtons = document.querySelectorAll('.view-btn');
            const viewContents = document.querySelectorAll('.view-content');
            
            // 确保初始状态正确
            viewContents.forEach(content => {
                if (content.classList.contains('active')) {
                    content.style.display = 'flex';
                } else {
                    content.style.display = 'none';
                }
            });
            
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 更新按钮激活状态
                    viewButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容视图
                    const targetView = this.getAttribute('data-view');
                    viewContents.forEach(content => {
                        if (content.classList.contains(targetView)) {
                            content.style.display = 'flex';
                            content.classList.add('active');
                        } else {
                            content.style.display = 'none';
                            content.classList.remove('active');
                        }
                    });
                });
            });
            
            // 一键优化按钮
            const optimizeButton = document.querySelector('.optimize-button');
            optimizeButton.addEventListener('click', function() {
                showLoading(800);
                setTimeout(() => {
                    showToast('已为您优化配送顺序，预计可节省7分钟');
                }, 800);
            });
            
            // 状态更新按钮
            const actionButtons = document.querySelectorAll('.action-button');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.classList.contains('pickup-button')) {
                        showLoading(500);
                        setTimeout(() => {
                            showToast('已更新为已取餐状态，开始导航至下一个目的地');
                        }, 500);
                    } else if (this.classList.contains('delivery-button')) {
                        showLoading(500);
                        setTimeout(() => {
                            showToast('已更新为已送达状态，该订单已完成');
                        }, 500);
                    } else if (this.classList.contains('report-button')) {
                        showReportDialog();
                    }
                });
            });
            
            // 报告问题对话框
            function showReportDialog() {
                const issues = [
                    '无法联系商家/顾客',
                    '找不到地址',
                    '等待时间过长',
                    '订单商品问题',
                    '其他问题'
                ];
                
                let dialogHTML = '<div style="padding: 20px; background: white; border-radius: 10px; max-width: 300px;">';
                dialogHTML += '<h3 style="margin-bottom: 15px;">请选择问题类型</h3>';
                
                issues.forEach((issue, index) => {
                    dialogHTML += `<div style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;" onclick="reportIssue(${index})">${issue}</div>`;
                });
                
                dialogHTML += '</div>';
                
                // 创建并显示对话框
                const dialog = document.createElement('div');
                dialog.id = 'report-dialog';
                dialog.style.position = 'fixed';
                dialog.style.top = '0';
                dialog.style.left = '0';
                dialog.style.width = '100%';
                dialog.style.height = '100%';
                dialog.style.backgroundColor = 'rgba(0,0,0,0.5)';
                dialog.style.display = 'flex';
                dialog.style.justifyContent = 'center';
                dialog.style.alignItems = 'center';
                dialog.style.zIndex = '1001';
                dialog.innerHTML = dialogHTML;
                
                document.body.appendChild(dialog);
                
                // 点击背景关闭对话框
                dialog.addEventListener('click', function(e) {
                    if (e.target === dialog) {
                        document.body.removeChild(dialog);
                    }
                });
                
                // 添加全局函数用于处理问题报告
                window.reportIssue = function(index) {
                    document.body.removeChild(dialog);
                    showToast(`已报告问题: ${issues[index]}`);
                };
            }
            
            // 订单卡片点击查看详情
            const orderCards = document.querySelectorAll('.order-card');
            const orderDetailModal = document.querySelector('.order-detail-modal');
            const modalClose = document.querySelector('.modal-close');
            
            orderCards.forEach(card => {
                card.addEventListener('click', function() {
                    orderDetailModal.style.display = 'flex';
                });
            });
            
            if (modalClose) {
                modalClose.addEventListener('click', function() {
                    orderDetailModal.style.display = 'none';
                });
            }
            
            orderDetailModal.addEventListener('click', function(e) {
                if (e.target === orderDetailModal) {
                    orderDetailModal.style.display = 'none';
                }
            });
            
            // 模拟拖拽排序功能
            const orderList = document.querySelector('.order-summary');
            const dragHandles = document.querySelectorAll('.drag-handle');
            
            if (orderList && dragHandles.length > 0) {
                dragHandles.forEach(handle => {
                    handle.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                        
                        // 获取当前卡片
                        const card = this.closest('.order-card');
                        
                        // 添加拖动样式
                        card.style.opacity = '0.8';
                        card.style.transform = 'scale(1.02)';
                        card.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                        card.style.zIndex = '10';
                        card.style.position = 'relative';
                        
                        // 获取初始位置
                        const startY = e.clientY;
                        const initialTop = card.offsetTop;
                        
                        // 鼠标移动处理
                        function onMouseMove(e) {
                            const currentY = e.clientY;
                            const offsetY = currentY - startY;
                            card.style.top = `${initialTop + offsetY}px`;
                        }
                        
                        // 鼠标释放处理
                        function onMouseUp() {
                            // 恢复样式
                            card.style.opacity = '';
                            card.style.transform = '';
                            card.style.boxShadow = '';
                            card.style.zIndex = '';
                            card.style.position = '';
                            card.style.top = '';
                            
                            // 移除事件监听
                            document.removeEventListener('mousemove', onMouseMove);
                            document.removeEventListener('mouseup', onMouseUp);
                            
                            showToast('已更新订单顺序');
                        }
                        
                        // 添加事件监听
                        document.addEventListener('mousemove', onMouseMove);
                        document.addEventListener('mouseup', onMouseUp);
                    });
                    
                    // 触摸设备支持
                    handle.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        
                        const card = this.closest('.order-card');
                        card.style.opacity = '0.8';
                        card.style.transform = 'scale(1.02)';
                        card.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                        card.style.zIndex = '10';
                        card.style.position = 'relative';
                        
                        const touch = e.touches[0];
                        const startY = touch.clientY;
                        const initialTop = card.offsetTop;
                        
                        function onTouchMove(e) {
                            const touch = e.touches[0];
                            const currentY = touch.clientY;
                            const offsetY = currentY - startY;
                            card.style.top = `${initialTop + offsetY}px`;
                        }
                        
                        function onTouchEnd() {
                            card.style.opacity = '';
                            card.style.transform = '';
                            card.style.boxShadow = '';
                            card.style.zIndex = '';
                            card.style.position = '';
                            card.style.top = '';
                            
                            document.removeEventListener('touchmove', onTouchMove);
                            document.removeEventListener('touchend', onTouchEnd);
                            
                            showToast('已更新订单顺序');
                        }
                        
                        document.addEventListener('touchmove', onTouchMove);
                        document.addEventListener('touchend', onTouchEnd);
                    });
                });
            }
            
            // 智能规划按钮点击事件
            const smartPlanBtn = document.getElementById('smartPlanBtn');
            const smartPlanPanel = document.getElementById('smartPlanPanel');
            
            if (smartPlanBtn && smartPlanPanel) {
                smartPlanBtn.addEventListener('click', function() {
                    smartPlanPanel.classList.toggle('active');
                    showLoading(500);
                });
            }
            
            // 应用规划路线按钮
            const applyPlanBtn = document.querySelector('.apply-plan-btn');
            if (applyPlanBtn) {
                applyPlanBtn.addEventListener('click', function() {
                    showLoading(800);
                    setTimeout(() => {
                        showToast('已应用智能规划路线，请按顺序配送');
                        smartPlanPanel.classList.remove('active');
                    }, 800);
                });
            }
            
            // 智能规划选项切换
            const planOptions = document.querySelectorAll('.plan-option');
            planOptions.forEach(option => {
                option.addEventListener('click', function() {
                    planOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    showLoading(300);
                });
            });
        });
    </script>
</body>
</html> 