<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>跑腿APP - 任务页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            max-width: 414px;
            margin: 0 auto;
            position: relative;
        }
        
        /* 顶部状态栏 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 15px;
            background-color: #fff;
            font-size: 12px;
        }
        
        .status-bar-left {
            display: flex;
            align-items: center;
        }
        
        .status-bar-time {
            font-weight: bold;
        }
        
        .status-bar-icons {
            display: flex;
            gap: 5px;
        }
        
        .status-bar-right {
            display: flex;
            align-items: center;
        }
        
        .battery-icon {
            width: 20px;
            height: 10px;
            border: 1px solid #333;
            border-radius: 2px;
            position: relative;
            margin-left: 5px;
        }
        
        .battery-icon:after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 14px;
            height: 4px;
            background-color: #333;
            border-radius: 1px;
        }
        
        /* 导航栏 */
        .nav-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #fff;
            border-bottom: 1px solid #eee;
        }
        
        .back-button {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
        }
        
        .status-dropdown {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .status-dropdown:after {
            content: '▼';
            margin-left: 5px;
            font-size: 12px;
            color: #666;
        }
        
        .notification-icon {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
        }
        
        /* 标签导航 */
        .tab-nav {
            display: flex;
            background-color: #fff;
            border-bottom: 1px solid #eee;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            position: relative;
            color: #999;
            font-size: 15px;
        }
        
        .tab-item.active {
            color: #333;
        }
        
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background-color: #ff6b01;
        }
        
        /* 订单卡片 */
        .order-card {
            margin: 15px;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .order-header {
            padding: 10px 15px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .order-status {
            color: #ff6b01;
            font-size: 15px;
            font-weight: bold;
        }
        
        .address-block {
            padding: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .address-icon {
            width: 30px;
            height: 30px;
            background-color: #ff6b01;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 14px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .delivery-icon {
            background-color: #52c41a;
        }
        
        .address-content {
            flex: 1;
        }
        
        .address-line {
            margin-bottom: 5px;
            font-size: 15px;
        }
        
        .address-detail {
            font-size: 16px;
            font-weight: bold;
            line-height: 1.4;
        }
        
        .distance-info {
            text-align: right;
            font-size: 14px;
            color: #999;
            margin-top: 5px;
        }
        
        .divider {
            height: 1px;
            background-color: #f5f5f5;
            margin: 0 15px;
        }
        
        .action-bar {
            display: flex;
            padding: 12px 15px;
        }
        
        .action-button {
            flex: 1;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            font-size: 15px;
            font-weight: bold;
            margin: 0 5px;
        }
        
        .primary-action {
            background-color: #2ecc71;
            color: white;
        }
        
        .secondary-action {
            border: 1px solid #999;
            color: #333;
        }
        
        .action-note {
            font-size: 13px;
            color: #999;
            padding: 0 15px 15px;
            display: flex;
        }
        
        .note-label {
            color: #666;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <!-- 顶部状态栏 -->
    <div class="status-bar">
        <div class="status-bar-left">
            <div class="status-bar-time">03:40</div>
        </div>
        <div class="status-bar-icons">
            <span>🔄</span>
            <span>📱</span>
            <span>⏰</span>
        </div>
        <div class="status-bar-right">
            <span>🔵</span>
            <span>📍</span>
            <span>📶</span>
            <span>🔊</span>
            <span>4G</span>
            <span>📶</span>
            <div class="battery-icon"></div>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="back-button">👤</div>
        <div class="status-dropdown">在线</div>
        <div class="notification-icon">🔔</div>
    </div>
    
    <!-- 标签导航 -->
    <div class="tab-nav">
        <div class="tab-item">新任务</div>
        <div class="tab-item active">进行中</div>
    </div>
    
    <!-- 订单列表 -->
    <div class="order-list">
        <!-- 第一个订单 -->
        <div class="order-card">
            <div class="order-header">
                <div class="order-status">已超时3423小时 送达</div>
            </div>
            <div class="address-block">
                <div class="address-icon">取</div>
                <div class="address-content">
                    <div class="address-line">浙江省杭州市临安区滨湖天地</div>
                    <div class="distance-info">131m</div>
                </div>
            </div>
            <div class="divider"></div>
            <div class="address-block">
                <div class="address-line" style="margin-left: 40px; font-size: 14px;">1.32km</div>
                <div class="address-content">
                    <div class="address-detail">浙江省杭州市临安区滨湖天地购物中心801</div>
                </div>
            </div>
            <div class="action-bar">
                <div class="action-button secondary-action">帮我取</div>
                <div class="action-button secondary-action">餐饮</div>
            </div>
            <div class="action-note">
                <div class="note-label">备注：</div>
                <div>--</div>
            </div>
            <div class="action-bar" style="border-top: 1px solid #f5f5f5;">
                <div class="action-button primary-action">已到取货点</div>
            </div>
            <div class="action-bar">
                <div class="action-button secondary-action" style="border: none; justify-content: flex-end;">
                    <span style="width: 24px; height: 24px; border-radius: 50%; border: 1px solid #999; display: flex; justify-content: center; align-items: center; margin-right: 5px;">📞</span>
                    联系取件人
                </div>
            </div>
        </div>
        
        <!-- 第二个订单 -->
        <div class="order-card">
            <div class="order-header">
                <div class="order-status">已超时620小时 送达</div>
            </div>
            <div class="address-block">
                <div class="address-icon delivery-icon">送</div>
                <div class="address-content">
                    <div class="address-line">陕西省西安市未央区洋东新城昆明路与天台路交汇处西北角</div>
                    <div class="distance-info">1119.25km</div>
                </div>
            </div>
            <div class="divider"></div>
            <div class="address-block">
                <div class="address-content">
                    <div class="address-detail">浙江省杭州市余杭区洋东新城昆明路与天台路交汇处西北角</div>
                </div>
            </div>
            <div class="action-bar">
                <div class="action-button secondary-action">帮我送</div>
                <div class="action-button secondary-action">餐饮</div>
                <div class="action-button secondary-action">小于5kg</div>
            </div>
            <div class="action-note">
                <div class="note-label">备注：</div>
                <div>重生王者炒饭 分数等</div>
            </div>
            <div class="action-bar" style="border-top: 1px solid #f5f5f5;">
                <div class="action-button primary-action" style="background-color: #1890ff;">确认送达</div>
            </div>
            <div class="action-bar">
                <div class="action-button secondary-action" style="border: none; justify-content: flex-end;">
                    <span style="width: 24px; height: 24px; border-radius: 50%; border: 1px solid #999; display: flex; justify-content: center; align-items: center; margin-right: 5px;">📞</span>
                    联系收件人
                </div>
            </div>
        </div>
    </div>
</body>
</html> 