<?php

namespace App\Jobs;

use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\OpenApiAccount;
use App\Models\Rider;
use App\Services\MaiYaTianService;
use App\Services\RiderLocationService;
use App\Services\WrcService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Ixudra\Curl\Facades\Curl;

class DispatchOpenCallback implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    private $times;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order, $times)
    {
        $this->order = $order;
        $this->times = $times;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->order->app_key) {
            switch ($this->order->app_key) {
                case O2oErrandOrder::APP_KEY_MYT:
                    try {
                        $rider = Rider::query()->where("id", $this->order->rider_id)->first();
                        $riderLocationService = app(RiderLocationService::class);
                        $location = $riderLocationService->getRiderLocation($this->order->rider_id);

                        $otherData = [
                            "rider_name" => $rider->name,
                            "rider_phone" => $rider->phone,
                            "longitude" => $location ? $location['lng'] : "",
                            "latitude" => $location ? $location['lat'] : "",
                        ];
                        (new MaiYaTianService())->deliveryChange($this->order->user_id, $this->order->order_no, $this->order->out_order_no, $this->order->getMytStatus(), $otherData);
                    } catch (\Exception $e) {
                        Log::error($this->order->app_key . "-配送状态同步：" . $e->getMessage());
                    }
                    break;
                case O2oErrandOrder::APP_KEY_WRC:
                    try {
                        $rider = Rider::query()->where("id", $this->order->rider_id)->first();
                        $riderLocationService = app(RiderLocationService::class);
                        $location = $riderLocationService->getRiderLocation($this->order->rider_id);

                        $otherData = [
                            "rider_name" => $rider->name,
                            "rider_phone" => $rider->phone,
                            "longitude" => $location ? $location['lng'] : "",
                            "latitude" => $location ? $location['lat'] : "",
                        ];
                        (new WrcService())->deliveryChange($this->order->user_id, $this->order->order_no, $this->order->out_order_no, $this->order->getMytStatus(), $otherData);
                    } catch (\Exception $e) {
                        Log::error($this->order->app_key . "-配送状态同步：" . $e->getMessage());
                    }
                    break;
                case O2oErrandOrder::APP_KEY_HB:
                    try {
                        Log::info("海博回调开始");
                        $rider = Rider::query()->where("id", $this->order->rider_id)->first();
                        $riderLocationService = app(RiderLocationService::class);
                        $location = $riderLocationService->getRiderLocation($this->order->rider_id);

                        $otherData = [
                            "rider_name" => $rider ? $rider->name : "",
                            "rider_phone" => $rider ? $rider->phone : "",
                            "longitude" => $location ? $location['lng'] : "",
                            "latitude" => $location ? $location['lat'] : "",
                        ];

                        // 获取海博状态码
                        $haiboService = new \App\Services\HaiboService();
                        $haiboStatus = $haiboService->getHaiboStatus($this->order);
                        // 直接调用海博配送状态回调，与麦芽田实现保持一致
                        $haiboService->deliveryStatusCallback(
                            $this->order->user_id,
                            $this->order->order_no,
                            $this->order->out_order_no,
                            $haiboStatus,
                            $otherData
                        );
                    } catch (\Exception $e) {
                        Log::error($this->order->app_key . "-配送状态同步：" . $e->getMessage());
                    }
                    break;
                case O2oErrandOrder::APP_KEY_QY:
                    try {
                        Log::info("青云回调开始");
                        $rider = Rider::query()->where("id", $this->order->rider_id)->first();
                        $riderLocationService = app(RiderLocationService::class);
                        $location = $riderLocationService->getRiderLocation($this->order->rider_id);

                        $otherData = [
                            "rider_name" => $rider ? $rider->name : "",
                            "rider_phone" => $rider ? $rider->phone : "",
                            "longitude" => $location ? $location['lng'] : "",
                            "latitude" => $location ? $location['lat'] : "",
                        ];

                        // 获取青云状态码
                        $qingyunService = new \App\Services\QingyunService();
                        $qingyunStatus = $qingyunService->getQingyunStatus($this->order);
                        // 直接调用青云配送状态回调，与海博实现保持一致
                        $qingyunService->deliveryStatusCallback(
                            $this->order->user_id,
                            $this->order->order_no,
                            $this->order->out_order_no,
                            $qingyunStatus,
                            $otherData
                        );
                    } catch (\Exception $e) {
                        Log::error($this->order->app_key . "-配送状态同步：" . $e->getMessage());
                    }
                    break;
                default:
                    $openApiAccount = OpenApiAccount::query()->where('app_key', $this->order->app_key)->first();
                    if ($openApiAccount && $openApiAccount->callback_url) {
                        $header = [];
                        $header['app-key'] = $this->order->app_key;
                        $data = [
                            'out_order_no' => $this->order->out_order_no,
                            'order_no' => $this->order->order_no,
                            'order_status' => $this->order->order_status,
                            'rider_name' => $this->order->rider->name,
                            'rider_avatar' => $this->order->rider->avatar,
                            'rider_phone' => $this->order->rider->phone,
                        ];
                        $signStr = $this->getSortedData($data);
                        $header['sign'] = hash_hmac('sha256', $signStr, $openApiAccount->app_secret);
                        $header['Authorization'] = 'Bearer token';
                        Log::debug(sprintf("订单回调 回调地址%s", $openApiAccount->callback_url), $data);
                        $res = Curl::to($openApiAccount->callback_url)->withData($data)->withHeaders($header)->post();
                        Log::debug("订单回调结果", [$res]);
                        // 大写转小写
                        if (strtolower($res) != 'success') {
                            // 记录日志
                            if ($this->times < 3) {
                                $this->times++;
                                $this->delay(60 * $this->times);
                                $this->dispatch($this->order, $this->times);
                                dispatch(new DispatchOpenCallback($this->order, $this->times + 1));
                            } else {
                                // 记录日志
                                Log::warning("订单回调失败，订单号：" . $this->order->order_no . "，回调地址：" . $openApiAccount->callback_url . "，回调参数：" . json_encode($data));
                            }
                        }
                    }
            }
        }
    }

    private function getSortedData($data)
    {
        ksort($data);
        $query = http_build_query($data);
        $query = str_replace('+', '%20', $query);

        return $query;
    }
}
