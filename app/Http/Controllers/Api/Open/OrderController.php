<?php

namespace App\Http\Controllers\Api\Open;

use App\Exceptions\BusinessException;
use App\Http\Resources\O2oErrandOrderResource;
use App\Models\O2oErrandOrder;
use App\Services\O2oErrandOrderService;
use Illuminate\Http\Request;

class OrderController extends OpenController
{
    public function store(Request $request, O2oErrandOrderService $service)
    {
        try {
            $order = $service->createOrderFromOpen($request->user()->id, $request->out_order_no, $request->all(), $this->appKey);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage() . $e->getFile() . $e->getLine());
        }
        return $this->success("配送订单下单成功", [
            'out_order_no' => $order->out_order_no,
            'order_no' => $order->order_no,
            'order_status' => $order->order_status,
        ]);
    }

    public function cancel(Request $request)
    {
        if (!$request->out_order_no && !$request->order_no) {
            throw new BusinessException(500, '订单号不能为空');
        }

        $order = O2oErrandOrder::query()->where('out_order_no', $request->out_order_no)->orWhere('order_no', $request->order_no)->first();
        if (!$order) {
            throw new BusinessException(500, '订单不存在');
        }

        // 使用服务方法进行状态验证和取消操作
        try {
            $orderService = app(\App\Services\O2oErrandOrderService::class);
            $canceledOrder = $orderService->cancelOrder($order->order_no);
        } catch (\Exception $e) {
            throw new BusinessException(500, $e->getMessage());
        }

        return $this->success("配送订单取消成功", [
            'out_order_no' => $canceledOrder->out_order_no,
            'order_no' => $canceledOrder->order_no,
            'order_status' => $canceledOrder->order_status,
        ]);

    }
}
