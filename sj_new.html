<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骑手APP - 多路径规划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#ff6b01',
                            dark: '#e05a00',
                            light: '#ff8c3f',
                            50: '#fff8f1',
                            100: '#ffe9d7',
                            200: '#ffd2ae',
                            300: '#ffaf7a',
                            400: '#ff8c3f',
                            500: '#ff6b01',
                            600: '#e85a00',
                            700: '#c24a00',
                            800: '#9c3c03',
                            900: '#7e3206'
                        }
                    },
                    boxShadow: {
                        'soft': '0 2px 10px rgba(0,0,0,0.05)',
                        'card': '0 4px 12px rgba(0,0,0,0.08)',
                        'float': '0 8px 20px rgba(0,0,0,0.12)'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 自定义样式 */
        .task-card {
            transition: all 0.2s ease;
        }
        .task-card:active {
            transform: scale(0.98);
        }
        .btn-primary {
            background: linear-gradient(135deg, #ff8c3f 0%, #ff6b01 100%);
        }
        .status-indicator {
            box-shadow: 0 0 0 4px rgba(255,107,1,0.2);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-8 text-gray-800">骑手APP多路径规划功能预览</h1>
        
        <!-- 页面1: 路径概览页 -->
        <div class="border border-gray-200 rounded-2xl overflow-hidden shadow-card bg-white inline-block w-[360px] h-[720px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-900 text-white p-2 flex justify-between items-center text-xs">
                <span class="font-semibold">03:40</span>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-sync-alt"></i>
                    <i class="fas fa-mobile-alt"></i>
                    <i class="fas fa-bell"></i>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-bluetooth"></i>
                    <i class="fas fa-map-marker-alt"></i>
                    <i class="fas fa-broadcast-tower"></i>
                    <i class="fas fa-wifi"></i>
                    <span>4G</span>
                    <i class="fas fa-signal"></i>
                    <div class="w-5 h-2.5 border border-white rounded-sm ml-1 relative">
                        <div class="absolute inset-0 bg-white m-0.5"></div>
                    </div>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-soft p-3 flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-user text-gray-600"></i>
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-truck text-primary-500"></i>
                        <span class="font-medium text-gray-800">在线</span>
                        <i class="fas fa-caret-down text-xs text-gray-500"></i>
                    </div>
                </div>
                <i class="fas fa-bell text-gray-600"></i>
            </div>
            
            <!-- 标签导航 -->
            <div class="flex bg-white">
                <div class="flex-1 p-3 text-center text-gray-400 font-medium">新任务</div>
                <div class="flex-1 p-3 text-center text-primary-500 font-semibold border-b-2 border-primary-500 relative">
                    进行中
                    <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-600"></div>
                </div>
            </div>
            
            <!-- 多路径优化提示 -->
            <div class="p-4 bg-gradient-to-r from-primary-50 to-white border-b border-gray-100">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-primary-500/10 flex items-center justify-center mr-3">
                            <i class="fas fa-route text-primary-500"></i>
                        </div>
                        <div>
                            <span class="font-semibold text-gray-800 block">智能路径规划</span>
                            <p class="text-xs text-gray-500">优化配送路线可节省约 <span class="text-primary-600 font-medium">15 分钟</span></p>
                        </div>
                    </div>
                    <button class="py-1.5 px-4 bg-primary-500 text-white text-xs font-medium rounded-full shadow-sm hover:bg-primary-600 transition">
                        立即优化
                    </button>
                </div>
            </div>
            
            <!-- 任务1 -->
            <div class="task-card bg-white border-b border-gray-100 hover:bg-gray-50/50 transition">
                <div class="px-4 py-2 bg-primary-50 flex items-center border-l-4 border-primary-500">
                    <i class="fas fa-exclamation-circle text-primary-500 mr-2"></i>
                    <span class="text-primary-600 font-medium text-sm">已超时 20 分钟</span>
                    <span class="ml-2 text-gray-500 text-sm">送达</span>
                </div>
                
                <div class="p-4 flex">
                    <div class="w-9 h-9 rounded-full bg-primary-500 text-white flex items-center justify-center font-bold shadow-sm status-indicator">
                        取
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-gray-800 font-medium">浙江省杭州市临安区滨湖天地</p>
                        <div class="flex justify-end">
                            <span class="text-gray-500 text-sm flex items-center">
                                <i class="fas fa-location-arrow text-primary-400 mr-1 text-xs"></i>
                                131m
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 flex items-center">
                    <div class="w-9 flex justify-center">
                        <div class="h-8 w-0.5 bg-gray-200"></div>
                    </div>
                    <div class="ml-3 text-xs text-gray-500">
                        <i class="fas fa-road mr-1"></i>1.32km
                    </div>
                </div>
                
                <div class="p-4 flex items-start">
                    <div class="ml-12 flex-1">
                        <p class="text-gray-700 font-medium">浙江省杭州市临安区滨湖天地购物中心801</p>
                        <div class="flex items-center mt-2 space-x-2">
                            <span class="bg-blue-50 text-blue-600 px-2 py-1 rounded-full text-xs flex items-center">
                                <i class="fas fa-shopping-bag mr-1 text-xs"></i>
                                帮我取
                            </span>
                            <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                <i class="fas fa-utensils mr-1 text-xs"></i>
                                餐饮
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 py-1 flex">
                    <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                        <span class="mr-1">备注:</span>
                        <span>--</span>
                    </div>
                </div>
                
                <div class="p-4 flex space-x-3">
                    <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        已到取货点
                    </button>
                    
                    <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                        <i class="fas fa-phone text-gray-600"></i>
                    </button>
                </div>
                <div class="px-4 pb-3 text-right">
                    <span class="text-gray-500 text-xs">联系取件人</span>
                </div>
            </div>
            
            <!-- 任务2 -->
            <div class="task-card bg-white hover:bg-gray-50/50 transition">
                <div class="px-4 py-2 bg-green-50 flex items-center border-l-4 border-green-500">
                    <i class="fas fa-clock text-green-500 mr-2"></i>
                    <span class="text-green-600 font-medium text-sm">预计 15:30 前送达</span>
                    <span class="ml-2 text-gray-500 text-sm">剩余 30 分钟</span>
                </div>
                
                <div class="p-4 flex">
                    <div class="w-9 h-9 rounded-full bg-green-500 text-white flex items-center justify-center font-bold shadow-sm">
                        送
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-gray-800 font-medium">浙江省杭州市余杭区洋东新城昆明路</p>
                        <div class="flex justify-end">
                            <span class="text-gray-500 text-sm flex items-center">
                                <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                5.2km
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 flex items-start">
                    <div class="ml-12 flex-1">
                        <p class="text-gray-700 font-medium">浙江省杭州市余杭区洋东新城时代广场2栋3单元</p>
                        <div class="flex items-center mt-2 space-x-2">
                            <span class="bg-primary-50 text-primary-600 px-2 py-1 rounded-full text-xs flex items-center">
                                <i class="fas fa-people-carry mr-1 text-xs"></i>
                                帮我送
                            </span>
                            <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                <i class="fas fa-utensils mr-1 text-xs"></i>
                                餐饮
                            </span>
                            <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                <i class="fas fa-weight mr-1 text-xs"></i>
                                小于5kg
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 py-1 flex">
                    <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                        <span class="mr-1">备注:</span>
                        <span>重生王者炒饭 分数等</span>
                    </div>
                </div>
                
                <div class="p-4 flex space-x-3">
                    <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                        <i class="fas fa-location-arrow mr-2"></i>
                        开始导航
                    </button>
                    
                    <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                        <i class="fas fa-phone text-gray-600"></i>
                    </button>
                </div>
                <div class="px-4 pb-3 text-right">
                    <span class="text-gray-500 text-xs">联系收件人</span>
                </div>
            </div>
        </div>
        
        <!-- 页面2: 路径详情页 -->
        <div class="border border-gray-200 rounded-2xl overflow-hidden shadow-card bg-white inline-block w-[360px] h-[720px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-900 text-white p-2 flex justify-between items-center text-xs">
                <span class="font-semibold">03:42</span>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-sync-alt"></i>
                    <i class="fas fa-mobile-alt"></i>
                    <i class="fas fa-bell"></i>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-bluetooth"></i>
                    <i class="fas fa-map-marker-alt"></i>
                    <i class="fas fa-broadcast-tower"></i>
                    <i class="fas fa-wifi"></i>
                    <span>4G</span>
                    <i class="fas fa-signal"></i>
                    <div class="w-5 h-2.5 border border-white rounded-sm ml-1 relative">
                        <div class="absolute inset-0 bg-white m-0.5"></div>
                    </div>
                </div>
            </div>
            
            <!-- 顶部导航 -->
            <div class="bg-white shadow-soft p-3 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <span class="ml-4 font-medium text-gray-800">智能路径规划</span>
                </div>
                <i class="fas fa-share-alt text-gray-600"></i>
            </div>
            
            <!-- 地图导航预览 -->
            <div class="relative h-56 bg-blue-50">
                <img src="https://source.unsplash.com/random/360x224/?navigation" alt="导航地图" class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div class="absolute bottom-0 left-0 right-0 p-4">
                    <p class="text-white font-semibold">3 个任务 · 最短路径</p>
                    <div class="flex items-center mt-1">
                        <div class="flex items-center mr-3 text-white/80 text-sm">
                            <i class="fas fa-road mr-1 text-xs"></i>
                            7.5 公里
                        </div>
                        <div class="flex items-center text-white/80 text-sm">
                            <i class="fas fa-clock mr-1 text-xs"></i>
                            35 分钟
                        </div>
                    </div>
                </div>
                <div class="absolute top-4 right-4 flex flex-col gap-2">
                    <button class="bg-white rounded-full w-10 h-10 shadow-lg text-primary-500 flex items-center justify-center hover:bg-gray-50 transition">
                        <i class="fas fa-location-crosshairs"></i>
                    </button>
                    <button class="bg-white rounded-full w-10 h-10 shadow-lg text-gray-600 flex items-center justify-center hover:bg-gray-50 transition">
                        <i class="fas fa-layer-group"></i>
                    </button>
                </div>
            </div>
            
            <!-- 路径优化提示 -->
            <div class="p-4 bg-white border-b border-gray-100">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 text-white flex items-center justify-center shadow-sm">
                        <i class="fas fa-route text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <div class="flex items-center">
                            <h2 class="font-semibold text-gray-800">最优路径规划</h2>
                            <span class="ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-700 rounded-full flex items-center">
                                <i class="fas fa-clock mr-1 text-xs"></i>
                                省时15分钟
                            </span>
                        </div>
                        <p class="text-sm text-gray-500 mt-0.5">按照此顺序配送可节省时间和路程</p>
                    </div>
                </div>
            </div>
            
            <!-- 路段详情列表 -->
            <div class="bg-white overflow-y-auto" style="height: 350px;">
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center shadow-sm">
                            <i class="fas fa-walking text-xs"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">当前位置</p>
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-location-dot text-gray-400 mr-1 text-xs"></i>
                                    0.0km
                                </span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">杭州市临安区临天路328号</p>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                        <p class="text-xs text-gray-500 py-1 flex items-center">
                            <i class="fas fa-arrow-up text-xs mr-1.5 text-gray-400"></i>
                            直行 450 米，右转
                        </p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-7 h-7 rounded-full bg-primary-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                            取
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                    1.2km
                                </span>
                            </div>
                            <div class="flex justify-between mt-1">
                                <p class="text-xs text-gray-500">任务#2839</p>
                                <p class="text-xs text-primary-500 font-medium flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    已超时 20 分钟
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                        <p class="text-xs text-gray-500 py-1 flex items-center">
                            <i class="fas fa-arrow-left text-xs mr-1.5 text-gray-400"></i>
                            左转，沿湖滨路行驶 1.5 公里
                        </p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                            送
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">滨湖天地购物中心</p>
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                    1.5km
                                </span>
                            </div>
                            <div class="flex justify-between mt-1">
                                <p class="text-xs text-gray-500">任务#2840</p>
                                <p class="text-xs text-green-600 font-medium flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    预计15:20送达
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                        <p class="text-xs text-gray-500 py-1 flex items-center">
                            <i class="fas fa-arrow-right text-xs mr-1.5 text-gray-400"></i>
                            右转，进入城北大道 3.8 公里
                        </p>
                    </div>
                </div>
                
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                            送
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-800">余杭区洋东新城时代广场</p>
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                    4.8km
                                </span>
                            </div>
                            <div class="flex justify-between mt-1">
                                <p class="text-xs text-gray-500">任务#2845</p>
                                <p class="text-xs text-green-600 font-medium flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    预计15:30送达
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作栏 -->
            <div class="absolute bottom-0 left-0 right-0 flex border-t border-gray-100 bg-white p-4">
                <button class="flex-1 py-3 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                    <i class="fas fa-location-arrow mr-2"></i>开始导航
                </button>
            </div>
        </div>
        
        <!-- 页面3: 导航页面 -->
        <div class="border border-gray-200 rounded-2xl overflow-hidden shadow-card bg-white inline-block w-[360px] h-[720px] mx-2 relative">
            <!-- 状态栏 -->
            <div class="bg-gray-900 text-white p-2 flex justify-between items-center text-xs">
                <span class="font-semibold">03:45</span>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-sync-alt"></i>
                    <i class="fas fa-mobile-alt"></i>
                    <i class="fas fa-bell"></i>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-bluetooth"></i>
                    <i class="fas fa-map-marker-alt"></i>
                    <i class="fas fa-broadcast-tower"></i>
                    <i class="fas fa-wifi"></i>
                    <span>4G</span>
                    <i class="fas fa-signal"></i>
                    <div class="w-5 h-2.5 border border-white rounded-sm ml-1 relative">
                        <div class="absolute inset-0 bg-white m-0.5"></div>
                    </div>
                </div>
            </div>
            
            <!-- 全屏导航地图 -->
            <div class="relative h-full w-full bg-blue-50">
                <img src="https://source.unsplash.com/random/360x640/?navigation,map" alt="实时导航" class="w-full h-full object-cover">
                
                <!-- 顶部信息栏 -->
                <div class="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-4">
                    <div class="flex justify-between items-center">
                        <button class="bg-white/90 backdrop-blur-sm rounded-full w-10 h-10 shadow-lg flex items-center justify-center hover:bg-white transition">
                            <i class="fas fa-chevron-left text-gray-800"></i>
                        </button>
                        <div class="bg-white/90 backdrop-blur-sm rounded-full py-1.5 px-4 shadow-lg flex items-center">
                            <i class="fas fa-list-ol text-primary-500 mr-1.5"></i>
                            <p class="text-xs font-medium text-gray-800">任务 1/3</p>
                        </div>
                        <button class="bg-white/90 backdrop-blur-sm rounded-full w-10 h-10 shadow-lg flex items-center justify-center hover:bg-white transition">
                            <i class="fas fa-list text-gray-800"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 任务状态提示 -->
                <div class="absolute top-16 left-0 right-0 px-4">
                    <div class="bg-white/95 backdrop-blur-sm text-primary-600 p-3 rounded-xl text-sm flex items-center shadow-lg border-l-4 border-primary-500">
                        <i class="fas fa-exclamation-circle mr-2 text-lg"></i>
                        <span>此任务已超时 20 分钟，请加快配送</span>
                    </div>
                </div>
                
                <!-- 导航指示控件 -->
                <div class="absolute top-32 left-4 bg-white/95 backdrop-blur-sm rounded-full p-2.5 shadow-lg border border-gray-100">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-location-arrow text-primary-500 text-lg"></i>
                        <p class="text-xs font-semibold text-gray-800 mt-1">北</p>
                    </div>
                </div>
                
                <!-- 右侧控制栏 -->
                <div class="absolute top-32 right-4 flex flex-col gap-2">
                    <button class="bg-white/95 backdrop-blur-sm rounded-full w-10 h-10 shadow-lg flex items-center justify-center hover:bg-white transition border border-gray-100">
                        <i class="fas fa-plus text-gray-700"></i>
                    </button>
                    <button class="bg-white/95 backdrop-blur-sm rounded-full w-10 h-10 shadow-lg flex items-center justify-center hover:bg-white transition border border-gray-100">
                        <i class="fas fa-minus text-gray-700"></i>
                    </button>
                    <button class="bg-white/95 backdrop-blur-sm rounded-full w-10 h-10 shadow-lg flex items-center justify-center hover:bg-white transition border border-gray-100">
                        <i class="fas fa-location-crosshairs text-primary-500"></i>
                    </button>
                </div>
                
                <!-- 底部导航提示条 -->
                <div class="absolute bottom-[110px] left-0 right-0 px-4">
                    <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-gray-100">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-br from-primary-400 to-primary-600 rounded-full p-2 mr-3 shadow-sm">
                                <i class="fas fa-directions text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800">右转进入湖滨路</h3>
                                <p class="text-sm text-gray-600 flex items-center">
                                    <i class="fas fa-map-pin text-gray-400 mr-1"></i>
                                    前方 300 米
                                </p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-gray-700">距离取货点</p>
                                <p class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-road mr-1"></i>
                                    1.2 公里 · 
                                    <i class="fas fa-clock mx-1"></i>
                                    预计 5 分钟
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-700">临安区滨湖天地</p>
                                <p class="text-xs text-primary-600 font-medium">取件 · 任务#2839</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部控制栏 -->
                <div class="absolute bottom-8 left-0 right-0 p-3 bg-white/95 backdrop-blur-sm border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <button class="bg-white rounded-full w-11 h-11 shadow border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition">
                            <i class="fas fa-volume-up text-gray-700"></i>
                        </button>
                        <button class="flex-1 mx-2 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            已到取货点
                        </button>
                        <button class="bg-white rounded-full w-11 h-11 shadow border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition">
                            <i class="fas fa-phone text-gray-700"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 