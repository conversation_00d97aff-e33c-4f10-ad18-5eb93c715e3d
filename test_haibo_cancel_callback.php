<?php

/**
 * 海博取消订单回调测试脚本
 * 
 * 用于测试海博订单取消时的主动回调功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\HaiboService;
use App\Models\O2oErrandOrder;
use Illuminate\Support\Facades\Log;

// 模拟测试数据
$testData = [
    'orderId' => 'HB_TEST_ORDER_' . time(),
    'cancelReasonCode' => 1,
    'cancelReasonDesc' => '商户取消订单测试'
];

echo "=== 海博取消订单回调测试 ===\n";
echo "测试订单ID: " . $testData['orderId'] . "\n";
echo "取消原因: " . $testData['cancelReasonDesc'] . "\n\n";

try {
    // 1. 首先创建一个测试订单（如果不存在）
    echo "1. 检查测试订单是否存在...\n";
    $order = O2oErrandOrder::where('out_order_no', $testData['orderId'])
        ->where('app_key', O2oErrandOrder::APP_KEY_HB)
        ->first();
    
    if (!$order) {
        echo "   测试订单不存在，请先创建一个海博测试订单\n";
        echo "   可以使用海博发单接口创建测试订单\n";
        exit(1);
    }
    
    echo "   找到测试订单: " . $order->order_no . "\n";
    echo "   当前状态: " . ($order->order_status) . "\n";
    echo "   退款状态: " . ($order->refund_status) . "\n\n";
    
    // 2. 测试取消订单功能
    echo "2. 执行取消订单操作...\n";
    $haiboService = new HaiboService();
    $result = $haiboService->cancelOrder($testData);
    
    echo "   取消结果: \n";
    echo "   - Code: " . $result['code'] . "\n";
    echo "   - Message: " . $result['message'] . "\n";
    if (isset($result['data']['cancelFee'])) {
        echo "   - 取消费用: " . $result['data']['cancelFee'] . "\n";
    }
    echo "\n";
    
    // 3. 检查订单状态
    echo "3. 检查订单状态变更...\n";
    $order->refresh();
    echo "   更新后状态: " . ($order->order_status) . "\n";
    echo "   退款状态: " . ($order->refund_status) . "\n";
    echo "   关闭原因: " . ($order->close_reason ?? '无') . "\n\n";
    
    // 4. 验证回调是否触发
    echo "4. 验证回调触发情况...\n";
    echo "   请检查日志文件中的海博回调记录\n";
    echo "   日志关键词: '海博配送状态回调请求', '海博取消订单回调结果'\n\n";
    
    echo "=== 测试完成 ===\n";
    echo "如果看到上述信息且没有错误，说明取消订单和回调功能正常工作\n";
    
} catch (\Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈: \n" . $e->getTraceAsString() . "\n";
    exit(1);
}
