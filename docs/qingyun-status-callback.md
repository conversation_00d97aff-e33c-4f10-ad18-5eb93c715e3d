# 青云配送状态回调实现文档

## 概述

本文档描述了跑腿系统与青云配送平台的集成实现，包括订单状态同步、配送状态回调等功能。

## 功能特性

- ✅ 完整的青云API接口实现（7个接口）
- ✅ 自动配送状态回调机制
- ✅ 手动回调命令支持
- ✅ 完整的错误处理和日志记录
- ✅ 签名验证和生成
- ✅ 限流保护机制

## 核心组件

### 1. QingyunController
位置：`app/Http/Controllers/Api/QingyunController.php`

提供青云平台所需的7个API接口：
- `POST /api/qingyun/store` - 创建/修改配送商门店
- `POST /api/qingyun/valuating` - 询价接口
- `POST /api/qingyun/send` - 发单接口
- `POST /api/qingyun/cancel` - 取消订单接口
- `POST /api/qingyun/order-detail` - 订单详情接口
- `POST /api/qingyun/rider-location` - 骑手位置查询接口
- `POST /api/qingyun/add-tip` - 添加小费接口

### 2. QingyunService
位置：`app/Services/QingyunService.php`

核心业务逻辑处理类，包含：
- 订单创建和管理
- 状态映射和转换
- 配送状态回调
- 签名生成和验证
- 错误处理

### 3. 自动回调机制
位置：`app/Jobs/DispatchOpenCallback.php`

当订单状态发生变化时，系统会自动触发回调：
1. 订单状态更新 → 触发 `DispatchOpenCallback` 队列任务
2. 识别青云订单 → 调用 `QingyunService::deliveryStatusCallback`
3. 构建回调数据 → 按青云协议格式
4. 生成签名 → 使用青云签名算法
5. 发送回调 → 向青云系统推送状态变更

### 4. 手动回调命令
位置：`app/Console/Commands/QingyunCallback.php`

提供手动触发回调的命令：
```bash
php artisan qingyun:callback {order_no}
```

## 配置说明

配置文件位于 `config/qingyun.php`，包含：
- 回调地址配置
- 青云调用协议配置
- 环境配置（测试/联调/生产）
- 状态映射配置
- 商品分类映射
- 订单来源映射

## 青云调用协议

本实现完全符合青云技术服务平台的调用协议规范：

### 请求规则

- **传输协议**：HTTPS
- **请求方式**：POST
- **参数格式**：application/x-www-form-urlencoded
- **字符编码**：UTF-8

### 公共参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| developerId | String | 是 | 青云分配的配送商标识 |
| timestamp | long | 是 | 时间戳（GMT+8） |
| version | String | 是 | API协议版本（1.0） |
| sign | String | 是 | 签名计算结果 |

### 签名协议

1. 将所有参数（除sign、byte[]及空值参数）按参数名字典顺序排序
2. 将参数以"参数1值1参数2值2..."的顺序拼接
3. 按照"secret + 排序后的参数"的顺序连接
4. 对字符串进行SHA1加密并转为小写

### 环境地址

- **测试环境**：https://pre-carrieropen.qingyun.com
- **联调环境**：https://carrieropen-tool.qingyun.com
- **生产环境**：https://carrieropen.qingyun.com

## 使用方式

### 1. 环境配置

复制 `.env.qingyun.example` 中的配置到你的 `.env` 文件：

```bash
# 青云配送平台配置
QINGYUN_CALLBACK_URL=https://pre-carrieropen.qingyun.com/api/delivery/statusCallback
QINGYUN_DEVELOPER_ID=your_developer_id
QINGYUN_SECRET=your_secret
QINGYUN_ENVIRONMENT=test
```

### 2. 签名验证

青云API已经启用了签名验证中间件 `VerifyQingyunSignature`，所有青云API请求都需要通过签名验证：

```php
// 青云路由已启用签名验证
Route::prefix('qingyun')->middleware('verify.qingyun.signature')->group(function () {
    Route::post('store', [QingyunController::class, 'createOrUpdateStore']);
    Route::post('valuating', [QingyunController::class, 'valuating']);
    Route::post('send', [QingyunController::class, 'send']);
    Route::post('cancel', [QingyunController::class, 'cancel']);
    Route::post('order-detail', [QingyunController::class, 'orderDetail']);
    Route::post('rider-location', [QingyunController::class, 'riderLocation']);
    Route::post('add-tip', [QingyunController::class, 'addTip']);
});
```

**签名验证要求：**
- ✅ 必需的系统参数：`developerId`、`timestamp`、`version`、`sign`
- ✅ developerId必须与配置文件中的值匹配
- ✅ API版本必须为 `1.0`
- ✅ 签名必须按照青云协议正确计算
- ✅ 时间戳必须在当前时间的5分钟内（防重放攻击）

**验签失败处理：**
- 缺少必需参数 → 返回错误信息
- developerId无效 → 返回 "developerId无效"
- 签名不匹配 → 返回 "签名验证失败"
- 时间戳过期 → 返回 "请求时间戳过期"

### 3. 测试签名算法

访问测试接口验证签名算法：
```
GET /api/qingyun/test-signature
```

### 4. 手动触发回调

使用命令行手动触发特定订单的回调：
```bash
php artisan qingyun:callback QY_ORDER_123456
```

### 5. 查看日志

青云相关日志记录在独立的日志文件中：
```bash
tail -f storage/logs/qingyun.log
```

## 状态映射

### 系统状态 → 青云状态

| 系统状态 | 青云状态码 | 青云状态描述 |
|----------|------------|--------------|
| 待支付(10) | 10 | 已创建(待接单) |
| 待接单(20) | 10 | 已创建(待接单) |
| 待取货(26) | 20 | 骑手已接单 |
| 到达取货点(28) | 25 | 已到店 |
| 派送中(30) | 30 | 已取货 |
| 已完成(40) | 50 | 已送达 |
| 已取消(0) | 99 | 已取消 |

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 1 | 系统异常 |
| 2 | 缺少参数，或参数格式错误 |
| 3 | 签名验证失败 |
| 4 | 未授权或授权过期 |
| 5 | 接口流控 |
| 10 | 该地区暂无运力 |
| 101 | 订单不存在 |
| 102 | 订单已完成，不能取消 |

## 签名验证详解

### 签名算法

青云API使用SHA1签名算法，具体步骤如下：

1. **参数过滤**：排除 `sign` 参数和空值参数
2. **参数排序**：按参数名的字典顺序排序
3. **参数拼接**：以 `参数1值1参数2值2...` 的格式拼接
4. **加密字符串**：`secret + 排序后的参数字符串`
5. **生成签名**：对加密字符串进行 SHA1 加密并转为小写

### 签名示例

假设请求参数如下：
```
secret: test
系统参数：
  developerId=test
  timestamp=1477395862
  version=1.0
应用参数：
  number=123
  string=测试
  double=123.123
  boolean=true
  empty=
```

**计算过程：**
1. 过滤空值参数：`empty=` 被排除
2. 按字典顺序排序：`boolean`, `developerId`, `double`, `number`, `string`, `timestamp`, `version`
3. 拼接参数：`booleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0`
4. 加密前字符串：`testbooleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0`
5. SHA1签名：`8943ba698f4b009f80dc2fd69ff9b313381263bd`

### 验签中间件

系统提供了 `VerifyQingyunSignature` 中间件，自动处理签名验证：

```php
// 中间件已在青云路由组中启用
Route::prefix('qingyun')->middleware('verify.qingyun.signature')->group(function () {
    // 所有青云API路由
});
```

### 测试签名

可以通过测试接口验证签名算法：
```bash
GET /api/qingyun/test-signature
```

返回示例：
```json
{
  "doc_example": {
    "test_params": {...},
    "generated_sign": "8943ba698f4b009f80dc2fd69ff9b313381263bd",
    "expected_sign": "8943ba698f4b009f80dc2fd69ff9b313381263bd",
    "is_correct": true
  },
  "actual_usage": {
    "test_params": {...},
    "generated_sign": "...",
    "note": "实际API使用developerId参数"
  }
}
```

## 注意事项

1. **签名验证**：所有请求都需要正确的签名，签名错误会导致请求被拒绝
2. **限流保护**：每个接口都有30次/秒的限流保护
3. **异步回调**：订单状态变更回调是异步处理的，不会影响主业务流程
4. **日志记录**：所有操作都有详细的日志记录，便于问题排查
5. **错误处理**：完整的错误处理机制，确保系统稳定性
6. **时间戳验证**：请求时间戳必须在当前时间的5分钟内，防止重放攻击

## 开发调试

### 测试签名生成

系统提供了签名测试功能，可以验证签名算法是否正确：

```php
$qingyunService = new QingyunService();
$result = $qingyunService->testQingyunSignature();
```

### 查看回调日志

```bash
# 查看青云回调日志
tail -f storage/logs/qingyun.log | grep "回调"

# 查看特定订单的处理日志
tail -f storage/logs/qingyun.log | grep "ORDER_123456"
```

### 手动测试回调

```bash
# 手动触发特定订单的回调
php artisan qingyun:callback QY_ORDER_123456
```
