<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>配送路径规划Demo</title>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.css">
    <style>
        /* 基础样式 */
        body {
            background-color: #f1f1f1;
            margin: 0;
            padding: 0;
        }

        /* 主容器 */
        .route-main {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        /* 订单列表 */
        .order-list {
            width: 300px;
            height: 100%;
            overflow: auto;
            background: #fff;
            border-right: 1px solid #ddd;
        }

        /* 地图容器 */
        .map-container {
            flex: 1;
            height: 100%;
            position: relative; /* 添加相对定位 */
        }

        /* 路径详情 */
        .route-detail {
            width: 300px;
            height: 100%;
            overflow: auto;
            background: #fff;
            border-left: 1px solid #ddd;
        }

        /* 订单项样式 */
        .order-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }

        .order-item:hover {
            background: #f5f5f5;
        }

        .order-item.selected {
            background: #e3f2fd;
        }

        /* 路径详情项 */
        .route-step {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .route-step .time {
            color: #666;
            font-size: 12px;
        }

        .route-step .address {
            margin-top: 5px;
        }

        /* 控制面板 */
        .control-panel {
            position: absolute;
            top: 70px; /* 调整位置，避免与地图控件重叠 */
            right: 10px;
            background: rgba(255, 255, 255, 0.9); /* 半透明背景 */
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            z-index: 100;
            max-width: 250px; /* 限制最大宽度 */
            backdrop-filter: blur(5px); /* 背景模糊效果 */
            transition: all 0.3s ease; /* 添加过渡效果 */
        }

        /* 控制面板折叠状态 */
        .control-panel.collapsed {
            width: 40px;
            height: 40px;
            overflow: hidden;
            cursor: pointer;
        }

        /* 控制面板的折叠按钮 */
        .panel-toggle {
            position: absolute;
            top: 5px;
            right: 5px;
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            color: #666;
            z-index: 101;
        }

        /* 折叠状态下显示的图标 */
        .control-panel.collapsed .expand-icon {
            display: block;
            text-align: center;
            font-size: 24px;
            line-height: 40px;
        }
        
        .control-panel.collapsed .panel-content {
            display: none;
        }
        
        .expand-icon {
            display: none;
        }

        .control-panel button {
            margin: 5px;
            padding: 8px 15px;
            border: none;
            background: #4286f4;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
            display: block; /* 让按钮独占一行 */
            width: calc(100% - 10px); /* 按钮宽度占满 */
        }

        .control-panel button:hover {
            background: #3367d6;
        }

        .rider-position {
            margin-bottom: 10px;
        }

        .rider-position h4 {
            margin-bottom: 5px;
        }

        .rider-position input[type="text"] {
            width: 45%; /* 调整宽度 */
            margin-bottom: 5px; /* 添加底部间距 */
        }

        .secondary {
            background: #ccc !important;
        }

        .success {
            background: #4caf50 !important;
        }

        /* 点位标记说明 */
        .marker-info {
            position: absolute;
            top: 70px; /* 调整位置，避免与地图控件重叠 */
            left: 10px;
            background: rgba(255, 255, 255, 0.9); /* 半透明背景 */
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            z-index: 100;
            max-height: 80vh; /* 限制最大高度 */
            overflow-y: auto; /* 超出部分显示滚动条 */
            max-width: 200px; /* 限制最大宽度 */
            backdrop-filter: blur(5px); /* 背景模糊效果 */
            transition: all 0.3s ease; /* 添加过渡效果 */
        }
        
        /* 点位说明折叠状态 */
        .marker-info.collapsed {
            width: 40px;
            height: 40px;
            overflow: hidden;
            cursor: pointer;
        }
        
        /* 显示的折叠图标 */
        .marker-info.collapsed .expand-icon {
            display: block;
            text-align: center;
            font-size: 24px;
            line-height: 40px;
        }
        
        .marker-info.collapsed .marker-info-content {
            display: none;
        }

        .marker-type {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .marker-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0; /* 防止颜色点被压缩 */
        }

        .pickup-color {
            background-color: #2196f3;
        }

        .delivery-color {
            background-color: #4caf50;
        }

        .rider-color {
            background-color: #ff9800;
        }
        
        /* 路线类型切换 */
        .route-type {
            margin-top: 10px;
            margin-bottom: 10px; /* 添加底部间距 */
        }
        
        .route-type select {
            width: 100%;
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        /* 颜色说明分类 */
        .marker-info-section {
            margin-bottom: 15px;
        }
        
        .marker-info-section h4 {
            margin-top: 10px;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        /* 响应式调整 */
        @media (max-height: 700px) {
            .marker-info {
                max-height: 60vh;
            }
        }
        
        @media (max-width: 1200px) {
            .control-panel, .marker-info {
                padding: 8px;
            }
            
            .marker-type {
                margin-bottom: 3px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
<div class="route-main">
    <!-- 订单列表 -->
    <div class="order-list">
        <h3 style="padding: 15px; margin: 0; border-bottom: 1px solid #ddd;">配送订单列表</h3>
        <div id="orderList">
            <!-- 订单列表将通过JavaScript动态填充 -->
        </div>
    </div>

    <!-- 地图容器 -->
    <div id="container" class="map-container"></div>

    <!-- 路径详情 -->
    <div class="route-detail">
        <h3 style="padding: 15px; margin: 0; border-bottom: 1px solid #ddd;">路径详情</h3>
        <div id="routeDetail">
            <!-- 路径详情将通过JavaScript动态填充 -->
        </div>
    </div>

    <!-- 点位标记说明 -->
    <div class="marker-info" id="markerInfo">
        <button class="panel-toggle" onclick="toggleMarkerInfo()">✖</button>
        <div class="expand-icon">📋</div>
        <div class="marker-info-content">
            <div class="marker-info-section">
                <h4 style="margin-top: 0;">点位说明</h4>
                <div class="marker-type">
                    <div class="marker-color rider-color"></div>
                    <div>骑手位置</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color pickup-color"></div>
                    <div>取货点</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color delivery-color"></div>
                    <div>送货点</div>
                </div>
            </div>
            
            <div class="marker-info-section">
                <h4>优先级说明</h4>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #FF0000;"></div>
                    <div>高优先级</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #FFA500;"></div>
                    <div>中优先级</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #FFFF00;"></div>
                    <div>低优先级</div>
                </div>
            </div>
            
            <div class="marker-info-section">
                <h4>路径颜色说明</h4>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #1976D2;"></div>
                    <div>骑手出发路线</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #FF3131;"></div>
                    <div>高优先级取货</div>
                </div>
                <div class="marker-type">
                    <div class="marker-color" style="background-color: #003131;"></div>
                    <div>高优先级送货</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel" id="controlPanel">
        <button class="panel-toggle" onclick="toggleControlPanel()">✖</button>
        <div class="expand-icon">⚙️</div>
        <div class="panel-content">
            <div class="rider-position">
                <h4>骑手起始位置</h4>
                <input type="text" id="riderLng" placeholder="经度" value="120.0129">
                <input type="text" id="riderLat" placeholder="纬度" value="30.2755">
                <button onclick="updateRiderPosition()">更新位置</button>
            </div>
            <div class="route-type">
                <select id="routeType">
                    <option value="driving">驾车模式</option>
                    <option value="riding">骑行模式</option>
                    <option value="walking">步行模式</option>
                </select>
            </div>
            <button class="success" onclick="calculateRoute()">计算最优路径</button>
            <button class="secondary" onclick="clearRoute()">清除路径</button>
        </div>
    </div>
</div>

<!-- 引入高德地图API及其插件 -->
<script type="text/javascript">
    window._AMapSecurityConfig = {
        // 替换成您的有效安全码
        securityJsCode: '87764e891f8b01aef5e8b62d333b008c',
    }
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a126d4406aa25746600f622e9605ce8d"></script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=a126d4406aa25746600f622e9605ce8d&plugin=AMap.Scale,AMap.ToolBar,AMap.Driving,AMap.Riding,AMap.Walking,AMap.TruckDriving"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.js"></script>

<script>
    let map;
    let routeInstance; // 当前路线规划实例
    let riderMarker;
    let riderPosition = [120.0129, 30.2755]; // 默认骑手起始位置
    let pickupMarkers = [];
    let deliveryMarkers = [];
    let pathPolyline;
    let routePlanType = 'driving'; // 默认驾车模式

    // 3个配送订单数据，每个订单包含一个取货点和一个送货点
    const orderData = [
        {
            id: 1,
            order_no: 'ORDER001',
            priority: 1, // 添加优先级字段，1为最高优先级
            pickup: {
                address: '杭州市余杭区文一西路998号海创科技中心',
                lng: 120.0129,
                lat: 30.2555
            },
            delivery: {
                address: '杭州市余杭区五常大道168号西溪水岸花苑',
                lng: 120.0235,
                lat: 30.2696
            }
        },
        {
            id: 2,
            order_no: 'ORDER002',
            priority: 2, // 中等优先级
            pickup: {
                address: '杭州市余杭区仓前街道余杭塘路2318号恒生科技园',
                lng: 120.0012,
                lat: 30.2831
            },
            delivery: {
                address: '杭州市余杭区五常街道永福路口西溪印象城',
                lng: 120.0156,
                lat: 30.2712
            }
        },
        {
            id: 3,
            order_no: 'ORDER003',
            priority: 3, // 最低优先级
            pickup: {
                address: '杭州市余杭区仓前街道龙舟路6号海创园',
                lng: 120.0078,
                lat: 30.2892
            },
            delivery: {
                address: '杭州市余杭区五常街道永福路西溪天堂商业街',
                lng: 120.0198,
                lat: 30.2678
            }
        }
    ];

    // 监听路径规划类型变化
    $('#routeType').change(function() {
        routePlanType = $(this).val();
    });

    // 初始化地图
    function initMap() {
        // 创建地图实例
        map = new AMap.Map('container', {
            zoom: 14,
            center: riderPosition,
            resizeEnable: true
        });

        // 添加骑手位置标记
        riderMarker = new AMap.Marker({
            position: riderPosition,
            map: map,
            content: `<div style="background-color: #ff9800; padding: 5px 10px; border-radius: 12px; color: white;">
                        <span>🛵</span> 骑手
                     </div>`,
            offset: new AMap.Pixel(-40, -15),
            title: '骑手位置'
        });

        // 初始化完成后渲染订单和标记点
        renderOrders();
        markOrderPoints();
    }

    // 渲染订单列表
    function renderOrders() {
        const orderList = $('#orderList');
        orderList.empty();

        orderData.forEach(order => {
            const priorityColor = getPriorityColor(order.priority);
            const priorityLabel = getPriorityLabel(order.priority);
            
            const orderItem = $(`
                <div class="order-item" data-id="${order.id}">
                    <div class="order-no">
                        <strong>订单号:</strong> ${order.order_no} 
                        <span style="background-color: ${priorityColor}; padding: 2px 6px; border-radius: 10px; color: black; font-size: 12px; margin-left: 5px;">优先级: ${priorityLabel}</span>
                    </div>
                    <div class="address">
                        <span style="color: #2196f3;">⬤</span> <strong>取货点:</strong> ${order.pickup.address}
                    </div>
                    <div class="address">
                        <span style="color: #4caf50;">⬤</span> <strong>送货点:</strong> ${order.delivery.address}
                    </div>
                </div>
            `);

            orderList.append(orderItem);
        });
    }

    // 在地图上标记所有订单的取货点和送货点
    function markOrderPoints() {
        // 清除之前的标记
        pickupMarkers.forEach(marker => marker.setMap(null));
        deliveryMarkers.forEach(marker => marker.setMap(null));
        pickupMarkers = [];
        deliveryMarkers = [];

        // 添加新的标记
        orderData.forEach((order, index) => {
            // 获取优先级对应颜色
            const priorityColor = getPriorityColor(order.priority);
            const priorityLabel = getPriorityLabel(order.priority);
            
            // 添加取货点标记
            const pickupMarker = new AMap.Marker({
                position: [order.pickup.lng, order.pickup.lat],
                map: map,
                content: `<div style="background-color: #2196f3; padding: 5px 10px; border-radius: 12px; color: white;">
                            <span>📦</span> 取货点 ${index + 1} <span style="background-color: ${priorityColor}; padding: 2px 6px; border-radius: 10px; font-size: 12px;">${priorityLabel}</span>
                          </div>`,
                offset: new AMap.Pixel(-70, -15),
                title: order.pickup.address
            });
            pickupMarkers.push(pickupMarker);

            // 添加送货点标记
            const deliveryMarker = new AMap.Marker({
                position: [order.delivery.lng, order.delivery.lat],
                map: map,
                content: `<div style="background-color: #4caf50; padding: 5px 10px; border-radius: 12px; color: white;">
                            <span>🏠</span> 送货点 ${index + 1} <span style="background-color: ${priorityColor}; padding: 2px 6px; border-radius: 10px; font-size: 12px;">${priorityLabel}</span>
                          </div>`,
                offset: new AMap.Pixel(-70, -15),
                title: order.delivery.address
            });
            deliveryMarkers.push(deliveryMarker);
        });

        // 调整地图视野以包含所有标记
        map.setFitView([riderMarker, ...pickupMarkers, ...deliveryMarkers]);
    }

    // 根据优先级获取颜色
    function getPriorityColor(priority) {
        switch(priority) {
            case 1: return '#FF0000'; // 红色，最高优先级
            case 2: return '#FFA500'; // 橙色，中等优先级
            case 3: return '#FFFF00'; // 黄色，低优先级
            default: return '#FFFFFF'; // 白色，无优先级
        }
    }
    
    // 根据优先级获取标签文本
    function getPriorityLabel(priority) {
        switch(priority) {
            case 1: return '高';
            case 2: return '中';
            case 3: return '低';
            default: return '无';
        }
    }

    // 更新骑手位置
    function updateRiderPosition() {
        const lng = parseFloat($('#riderLng').val());
        const lat = parseFloat($('#riderLat').val());
        
        if (isNaN(lng) || isNaN(lat)) {
            $.alert('请输入有效的经纬度');
            return;
        }

        riderPosition = [lng, lat];
        riderMarker.setPosition(riderPosition);
        map.setCenter(riderPosition);
    }

    // 计算最优配送路径
    function computeOptimalRoute(riderLng, riderLat) {
        try {
            console.log('开始计算最优路径');

            // 收集所有点位
            let allPoints = [];
            
            // 添加起点（骑手位置）
            allPoints.push({
                type: 'rider',
                lng: riderLng,
                lat: riderLat,
                address: '骑手当前位置'
            });

            // 构建取货点和送货点
            const pickupPoints = [];
            const deliveryPoints = [];

            orderData.forEach(order => {
                // 添加取货点
                const pickup = {
                    type: 'pickup',
                    order_id: order.id,
                    order_no: order.order_no,
                    priority: order.priority,
                    lng: order.pickup.lng,
                    lat: order.pickup.lat,
                    address: order.pickup.address
                };
                pickupPoints.push(pickup);
                
                // 添加送货点
                const delivery = {
                    type: 'delivery',
                    order_id: order.id,
                    order_no: order.order_no,
                    priority: order.priority,
                    lng: order.delivery.lng,
                    lat: order.delivery.lat,
                    address: order.delivery.address
                };
                deliveryPoints.push(delivery);
            });

            // 按照优先级排序订单，优先级数字越小越优先
            const sortedOrders = [...orderData].sort((a, b) => a.priority - b.priority);

            // 根据排序结果，按优先级重新排列取货和送货点
            allPoints = [allPoints[0]]; // 先保留骑手位置
            
            // 按优先级顺序为每个订单添加取货后送货
            // 重要业务规则：同一个订单必须先取货后送货
            sortedOrders.forEach(order => {
                const pickup = pickupPoints.find(p => p.order_id === order.id);
                const delivery = deliveryPoints.find(d => d.order_id === order.id);
                allPoints.push(pickup, delivery);
            });
            
            // 验证路径是否符合"先取货后送货"规则
            if (!validatePickupBeforeDelivery(allPoints)) {
                showError('路径计算错误：存在送货点在对应取货点之前的情况');
                return;
            }
            
            // 在控制台输出路径信息用于调试
            console.log('路径点数量:', allPoints.length);
            console.log('路径点详情:', allPoints);

            // 创建分段路径规划
            createSegmentedRoute(allPoints);
        } catch (error) {
            console.error('路径计算错误:', error);
            $.alert({
                title: '错误',
                content: '路径计算出错: ' + error.message
            });
        }
    }
    
    // 验证同一订单的取货点必须在送货点之前
    function validatePickupBeforeDelivery(points) {
        // 用于记录已取货的订单ID
        const pickedUpOrders = new Set();
        
        for (let i = 0; i < points.length; i++) {
            const point = points[i];
            
            // 跳过骑手位置点
            if (point.type === 'rider') continue;
            
            if (point.type === 'pickup') {
                // 记录已取货的订单
                pickedUpOrders.add(point.order_id);
            } else if (point.type === 'delivery') {
                // 如果送货时尚未取货，则违反规则
                if (!pickedUpOrders.has(point.order_id)) {
                    console.error(`订单 ${point.order_no} 违反送货规则：送货点在取货点之前`);
                    return false;
                }
            }
        }
        
        return true;
    }

    // 创建分段路线，每段使用不同颜色
    function createSegmentedRoute(allPoints) {
        // 清除之前的路线
        if (routeInstance) {
            routeInstance.clear();
        }
        
        // 清空路径详情
        const routeDetail = $('#routeDetail');
        routeDetail.empty();
        
        // 添加总览信息标题
        routeDetail.append(`
            <div class="route-step" style="background-color: #f5f5f5;">
                <div><strong>配送路径详情</strong></div>
                <div><strong>规划方式:</strong> ${getRoutePlanTypeName(routePlanType)}</div>
            </div>
        `);
        
        let totalDistance = 0;
        let totalTime = 0;
        
        // 存储所有路段的Promise
        const segmentPromises = [];
        
        // 创建分段路线
        for (let i = 0; i < allPoints.length - 1; i++) {
            const startPoint = allPoints[i];
            const endPoint = allPoints[i + 1];
            const origin = [startPoint.lng, startPoint.lat];
            const destination = [endPoint.lng, endPoint.lat];
            
            // 决定路段颜色：按目的地的优先级决定颜色
            const priority = endPoint.priority || 0;
            const pathColor = getPriorityPathColor(priority, endPoint.type);
            
            // 创建单段路线的Promise
            const segmentPromise = new Promise((resolve, reject) => {
                // 根据选择的规划类型创建路线规划实例
                let segmentRouteInstance;
                switch(routePlanType) {
                    case 'driving':
                        segmentRouteInstance = new AMap.Driving({
                            map: map,
                            hideMarkers: true // 隐藏默认标记点
                        });
                        break;
                    case 'riding':
                        segmentRouteInstance = new AMap.Riding({
                            map: map,
                            hideMarkers: true
                        });
                        break;
                    case 'walking':
                        segmentRouteInstance = new AMap.Walking({
                            map: map,
                            hideMarkers: true
                        });
                        break;
                    default:
                        segmentRouteInstance = new AMap.Driving({
                            map: map,
                            hideMarkers: true
                        });
                }
                
                // 计算路线
                segmentRouteInstance.search(
                    origin,
                    destination,
                    function(status, result) {
                        if (status === 'complete') {
                            // 使用自定义颜色渲染路径
                            const route = result.routes[0];
                            const path = route.steps.map(step => step.path).flat();
                            
                            const polyline = new AMap.Polyline({
                                path: path,
                                strokeColor: pathColor,
                                strokeWeight: 6,
                                strokeOpacity: 0.8
                            });
                            
                            map.add(polyline);
                            
                            // 更新总距离和时间
                            totalDistance += route.distance;
                            totalTime += route.time;
                            
                            // 添加路段信息到详情面板
                            const segmentInfo = {
                                start: startPoint,
                                end: endPoint,
                                distance: route.distance,
                                time: route.time,
                                pathColor: pathColor
                            };
                            
                            resolve(segmentInfo);
                        } else {
                            reject(new Error('路段规划失败'));
                        }
                    }
                );
            });
            
            segmentPromises.push(segmentPromise);
        }
        
        // 当所有路段都计算完成后更新详情面板
        Promise.all(segmentPromises)
            .then(segmentInfos => {
                // 更新总距离和时间信息
                const totalDistanceInfo = $(`
                    <div class="route-step" style="background-color: #f5f5f5;">
                        <div><strong>总距离:</strong> ${(totalDistance/1000).toFixed(1)}公里</div>
                        <div><strong>预计用时:</strong> ${Math.floor(totalTime/60)}分钟</div>
                    </div>
                `);
                
                $(totalDistanceInfo).insertAfter($('#routeDetail .route-step').first());
                
                // 添加每个路段的详情
                segmentInfos.forEach((segment, index) => {
                    const startPoint = segment.start;
                    const endPoint = segment.end;
                    
                    let startIcon, startTitle, endIcon, endTitle;
                    if (startPoint.type === 'rider') {
                        startIcon = '🛵';
                        startTitle = '骑手位置';
                    } else if (startPoint.type === 'pickup') {
                        startIcon = '📦';
                        startTitle = '取货点';
                    } else if (startPoint.type === 'delivery') {
                        startIcon = '🏠';
                        startTitle = '送货点';
                    }
                    
                    if (endPoint.type === 'pickup') {
                        endIcon = '📦';
                        endTitle = '取货点';
                    } else if (endPoint.type === 'delivery') {
                        endIcon = '🏠';
                        endTitle = '送货点';
                    }
                    
                    const priorityLabel = endPoint.priority ? `优先级: ${getPriorityLabel(endPoint.priority)}` : '';
                    
                    const segmentDetail = $(`
                        <div class="route-step">
                            <div style="border-left: 4px solid ${segment.pathColor}; padding-left: 10px; margin-bottom: 5px;">
                                <div><strong>路段 ${index + 1}</strong> ${priorityLabel ? `<span style="background-color: ${getPriorityColor(endPoint.priority)}; padding: 2px 6px; border-radius: 10px; color: black; font-size: 12px; margin-left: 5px;">${priorityLabel}</span>` : ''}</div>
                                <div><strong>起点:</strong> ${startIcon} ${startTitle} - ${startPoint.address}</div>
                                <div><strong>终点:</strong> ${endIcon} ${endTitle} - ${endPoint.address}</div>
                                <div><strong>距离:</strong> ${(segment.distance/1000).toFixed(1)}公里</div>
                                <div><strong>用时:</strong> ${Math.floor(segment.time/60)}分钟</div>
                                ${endPoint.order_no ? `<div><strong>订单号:</strong> ${endPoint.order_no}</div>` : ''}
                            </div>
                        </div>
                    `);
                    
                    $('#routeDetail').append(segmentDetail);
                });
                
                // 自动调整视野以显示整个路线
                map.setFitView();
            })
            .catch(error => {
                console.error('路径规划错误:', error);
                $.alert({
                    title: '错误',
                    content: '路径规划出错: ' + error.message
                });
            });
    }
    
    // 根据优先级和点位类型获取路径颜色
    function getPriorityPathColor(priority, pointType) {
        if (priority === 0) {
            return '#1976D2'; // 骑手出发路线使用蓝色
        }
        
        // 取送类型颜色基础
        const baseColor = pointType === 'pickup' ? '#FF' : '#00'; // 取货用红系列，送货用绿系列
        
        switch(priority) {
            case 1: return baseColor + '3131'; // 高优先级
            case 2: return baseColor + '7F00'; // 中优先级
            case 3: return baseColor + 'AA00'; // 低优先级
            default: return '#888888';         // 默认灰色
        }
    }

    // 计算最优路径 - 直接使用高德地图API，不调用后端
    function calculateRoute() {
        // 清除之前的路线
        clearRoute();
        
        // 获取当前选择的路线规划类型
        routePlanType = $('#routeType').val();

        // 从页面获取骑手位置
        const riderLng = parseFloat($('#riderLng').val());
        const riderLat = parseFloat($('#riderLat').val());

        if (isNaN(riderLng) || isNaN(riderLat)) {
            $.alert('请输入有效的骑手位置坐标');
            return;
        }

        // 显示加载提示
        const loadingDialog = $.dialog({
            title: '计算中',
            content: '正在计算配送路径，请稍候...',
            closeIcon: false,
            buttons: false,
            onOpen: function() {
                // 异步计算路径，避免界面卡顿
                setTimeout(function() {
                    computeOptimalRoute(riderLng, riderLat);
                    loadingDialog.close();
                }, 100);
            }
        });
    }

    // 更新路径详情
    function updateRouteDetail(result, allPoints) {
        const routeDetail = $('#routeDetail');
        routeDetail.empty();
        
        // 添加总览信息
        const routes = result.routes[0];
        const totalDistance = routes.distance;
        const totalTime = routes.time;
        
        routeDetail.append(`
            <div class="route-step" style="background-color: #f5f5f5;">
                <div><strong>总距离:</strong> ${(totalDistance/1000).toFixed(1)}公里</div>
                <div><strong>预计用时:</strong> ${Math.floor(totalTime/60)}分钟</div>
                <div><strong>规划方式:</strong> ${getRoutePlanTypeName(routePlanType)}</div>
            </div>
        `);
        
        // 添加每个站点信息
        allPoints.forEach((point, index) => {
            let icon, title, color;
            if (point.type === 'rider') {
                icon = '🛵';
                title = '骑手位置';
                color = '#ff9800';
            } else if (point.type === 'pickup') {
                icon = '📦';
                title = '取货点';
                color = '#2196f3';
            } else if (point.type === 'delivery') {
                icon = '🏠';
                title = '送货点';
                color = '#4caf50';
            }

            routeDetail.append(`
                <div class="route-step">
                    <div style="color: ${color}"><strong>${index + 1}. ${icon} ${title}</strong></div>
                    <div class="address">${point.address}</div>
                    ${point.order_no ? `<div>订单号: ${point.order_no}</div>` : ''}
                </div>
            `);
        });
    }
    
    // 获取路径规划类型名称
    function getRoutePlanTypeName(type) {
        switch(type) {
            case 'driving':
                return '驾车模式';
            case 'riding':
                return '骑行模式';
            case 'walking':
                return '步行模式';
            default:
                return '驾车模式';
        }
    }

    // 清除路径
    function clearRoute() {
        if (routeInstance) {
            routeInstance.clear();
        }
        
        // 清除路径详情
        $('#routeDetail').empty();
    }

    // 显示错误信息
    function showError(message) {
        $.alert({
            title: '错误',
            content: message
        });
    }

    // 切换控制面板折叠/展开状态
    function toggleControlPanel() {
        const panel = document.getElementById('controlPanel');
        panel.classList.toggle('collapsed');
    }
    
    // 切换点位说明折叠/展开状态
    function toggleMarkerInfo() {
        const info = document.getElementById('markerInfo');
        info.classList.toggle('collapsed');
    }

    // 页面加载完成后初始化
    $(function() {
        initMap();
        
        // 点击折叠后的面板时展开
        $('#controlPanel.collapsed').click(function(e) {
            if ($(e.target).hasClass('panel-toggle')) return;
            toggleControlPanel();
        });
        
        $('#markerInfo.collapsed').click(function(e) {
            if ($(e.target).hasClass('panel-toggle')) return;
            toggleMarkerInfo();
        });
    });
</script>
</body>
</html> 