<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>雨骑士</title>
    <link rel="stylesheet" href="/bootstrap-3.3.7-dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/css/homepage.css" />
    <script src="https://cdn.staticfile.org/jquery/3.3.1/jquery.min.js"></script>
    <script src="/bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
</head>

<body>
<nav class="navbar navbar-default navbar-fixed-top shadow-10 bg-white lay-pt10 lay-pb10">
    <div class="container-fluid">
        <div class="navbar-header">
            <!-- <a class="navbar-brand" href="#">Brand</a> -->
            <a href=""> <img class="lay-w100" src="/images/homepage/yqs/logo2.png" alt="" />雨骑士</a>
        </div>
        <div class="collapse navbar-collapse" id="bs-navbar-collapse-1">
            <ul class="nav navbar-nav navbar-right">
                <li><a href="#">首页</a></li>
                <li><a href="#block1">简介</a></li>
                <li><a href="#block2">关于</a></li>
                <li><a href="#block5">联系我们</a></li>
            </ul>
        </div>
        <!-- /.navbar-collapse -->
    </div>
    <!-- /.container-fluid -->
</nav>
<div style="padding-top: 90px;"></div>
<main class="m-auto">
    <div class="container-fluid banner-block" id="block1">
        <div class="row">
            <div class="col-sm-4">
                <div class="lay-pt70"></div>
                <div class="flex-column-ct">
                    <div class="">
                        <h1 class="text-bold lay-pb10">雨骑士</h1>
                        <h5 class="text-bold"></h5>
                        <div class="lay-pt70">
                            <a href="#" class="btn mybtn-1 lay-w160" style="background-color: #d54b2f" target="_blank">Android版</a>
                            <a href="#" target="_blank" class="btn mybtn-2 lay-w160 lay-mt30" style="background-color: #d54b2f">Ios版</a>
                        </div>
                    </div>
                </div>
                <div class="lay-pt70 visible-xs-block"></div>
            </div>
            <div class="col-sm-6 col-sm-offset-2">
                <img class="lay-w100p" src="/images/homepage/yqs/index.png" alt="" />
            </div>
        </div>
    </div>
    <div class="bg-1 lay-pt100 lay-pb100">
        <div class="container-fluid" id="block2">
            <div class="row">
                <div class="col-sm-4">
                    <img class="lay-w100p" src="/images/homepage/<EMAIL>" alt="" />
                </div>
                <div class="col-sm-8">
                    <div class="flex-column-ct lay-pb70">
                        <div class="col-xs-6 col-sm-6 col-md-6 lay-pt70">
                            <div class="">
                                <div>
                                    <h3 class="text-bold">雨骑士</h3>
                                    <div class="text-grey lh180">
                                        同城生活服务平台，为附近的人提供帮取送，帮买，全能帮等多样化同城即时服务，为中小企业，电商，本地商户提供安全可靠的专业高端配送服务。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid" id="block3">
        <div class="row lay-pb100 about-block">
            <div class="col-sm-12">
                <header class="text-center lay-pt70">
                    <h3>关于雨骑士APP</h3>
                    <p class="text-grey lh180">一呼百应，雨骑士为您提供高效便捷的同城即时服务</p>
                </header>
            </div>
        </div>
    </div>
{{--    <div class="bg-2" id="block4">--}}
{{--        <div class="container-fluid">--}}

{{--            <div class="row odds-block lay-pb30">--}}
{{--                <div class="container-fluid">--}}
{{--                    <header class="text-center lay-pb30 lay-pt30">--}}
{{--                        <h3 class="vm white">--}}
{{--                            <div class="circle-block"></div> <span class="lay-pl30 lay-pr30 display-ib">我们的优势</span>--}}
{{--                            <div class="circle-block"></div>--}}
{{--                        </h3>--}}
{{--                    </header>--}}
{{--                </div>--}}
{{--                <div class="col-sm-4">--}}
{{--                    <div class="odds-block-item lay-pb30">--}}
{{--                        <div class="">--}}
{{--                            <div class="m-auto">--}}
{{--                                <img class="lay-w100p" src="/images/homepage/<EMAIL>" alt="" />--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="caption lay-pr30 lay-pl30">--}}
{{--                            <h4 class="text-center">淘客共享APP</h4>--}}
{{--                            <div class="text-grey text-center lh180">--}}
{{--                                绑定淘客自己的妈妈账号，自定义返利佣金比--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--                <div class="col-sm-4">--}}
{{--                    <div class="odds-block-item lay-pb30">--}}
{{--                        <div class="">--}}
{{--                            <div class="m-auto">--}}
{{--                                <img class="lay-w100p" src="/images/homepage/<EMAIL>" alt="" />--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="caption lay-pr30 lay-pl30">--}}
{{--                            <h4 class="text-center">独家生活优惠券</h4>--}}
{{--                            <div class="text-grey text-center lh180">--}}
{{--                                线下店铺优惠券，助力收入开源--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--                <div class="col-sm-4">--}}
{{--                    <div class="odds-block-item lay-pb30">--}}
{{--                        <div class="">--}}
{{--                            <div class="m-auto">--}}
{{--                                <img class="lay-w100p" src="/images/homepage/<EMAIL>" alt="" />--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="caption lay-pr30 lay-pl30">--}}
{{--                            <h4 class="text-center">内容沉淀</h4>--}}
{{--                            <div class="text-grey text-center lh180">--}}
{{--                                文章、专辑等精选内容可供发圈选择--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
    <div class="container-fluid contact-block" id="block5">
        <div class="row lay-pb30 ">
            <div class="col-sm-12">
                <div class="container-fluid lay-pt30 lay-pb30">
                    <h3 class="text-center text-bold">联系我们</h3>
                </div>
                <div class="col-sm-4">
                    <table class="table contact-block-table">
                        <tbody>
                        <tr>
                            <td> <img class="lay-w30 lay-h30" src="/images/homepage/<EMAIL>" alt="" />
                            </td>
                            <td>电话号码</td>
                            <td>暂无</td>
                        </tr>
                        <tr>
                            <td> <img class="lay-w30 lay-h30" src="/images/homepage/<EMAIL>" alt="" /> </td>
                            <td>QQ</td>
                            <td>暂无</td>
                        </tr>
                        <tr>
                            <td> <img class="lay-w30 lay-h30" src="/images/homepage/<EMAIL>" alt="" />
                            </td>
                            <td>微信</td>
                            <td>暂无</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-sm-6 col-sm-offset-2">
                    <form class="jsSubmitForm ">
                        <div class="form-group">
                            <label for="exampleInputEmail1">您的姓名</label>
                            <input required name="name" type="text" class="form-control" placeholder="" />
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">您的联系方式</label>
                            <input required name="contact" type="text" class="form-control" placeholder="" />
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">您想咨询的问题</label>
                            <textarea required name="message" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="alert alert-success" role="alert"></div>
                        <div class="alert alert-danger" role="alert"></div>
                        <button type="submit" class="btn mybtn-2 lay-w160" style="background-color: #d54b2f">
                            <div class="loading-cirlce" data-status="0"></div>提交
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

</main>
<div class="lay-pt100 lay-pb100 text-center footer">
    <div style="margin-top: 10px">
        <span style="color: white">
            © 2014-2019 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" style="color: white" target="_blank">浙ICP备2022032094号</a>
        </span>
    </div>
</div>

<script>
    (function() {
        bindSubmitMessageEvent(".jsSubmitForm");

        function bindSubmitMessageEvent(formClassName) {
            $(formClassName).children(".alert-success").hide();
            $(formClassName).children(".alert-danger").hide();

            var btnLoading = $(formClassName).children(".btn").children(".loading-cirlce");
            btnLoading.hide();

            btnLoading.attr("data-status", "0");

            $(formClassName).submit(function(e) {
                $(formClassName).children(".alert-success").hide();
                $(formClassName).children(".alert-danger").hide();
                var formData = $(this).serialize();
                var _this = this;

                if (btnLoading.attr("data-status") === "1") {
                    return false;
                }

                btnLoading.show();
                btnLoading.attr("data-status", "1");

                $.ajax({
                    url: "/api/message/submit",
                    method: "post",
                    data: formData,
                    success: function(res) {
                        btnLoading.hide();
                        btnLoading.attr("data-status", "0");
                        if (res.success) {
                            $(_this).children(".alert-success").show().text("提交成功");
                        } else {
                            $(_this).children(".alert-danger").show().text(res.msg);
                        }
                    },
                    error: function(err) {
                        btnLoading.attr("data-status", "0");
                        btnLoading.hide();
                        $(_this).children(".alert-danger").show().text(err.statusText || err.message);
                    }
                })
                return false; // 阻止form重载页面
            })
        }


    })();
</script>
</body>

</html>
