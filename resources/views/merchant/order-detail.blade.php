@extends('merchant.layouts.app')

@section('title', '订单详情')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex items-center mb-6">
            <a href="{{ route('merchant.orders') }}" class="text-gray-500 hover:text-primary mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-gray-800">订单详情</h1>
        </div>
        
        @if(session('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p>{{ session('error') }}</p>
        </div>
        @endif
        
        <!-- 订单状态 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-700">订单状态</h2>
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                        @if($order->order_status == \App\Models\O2oErrandOrder::STATUS_CANCEL)
                            bg-gray-100 text-gray-800
                        @elseif($order->order_status == \App\Models\O2oErrandOrder::STATUS_WAITING_PAY)
                            bg-yellow-100 text-yellow-800
                        @elseif($order->order_status == \App\Models\O2oErrandOrder::STATUS_FINISH)
                            bg-green-100 text-green-800
                        @else
                            bg-blue-100 text-blue-800
                        @endif
                    ">
                        {{ \App\Models\O2oErrandOrder::StatusMap[$order->order_status] ?? '未知状态' }}
                    </span>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">订单号</p>
                        <p class="text-gray-800">{{ $order->order_no }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">订单类型</p>
                        <p class="text-gray-800">{{ \App\Models\O2oErrandOrder::TypeMap[$order->type] ?? '未知' }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">创建时间</p>
                        <p class="text-gray-800">{{ $order->created_at->format('Y-m-d H:i:s') }}</p>
                    </div>
                    @if($order->paid_at)
                    <div>
                        <p class="text-sm text-gray-500">支付时间</p>
                        <p class="text-gray-800">{{ $order->paid_at->format('Y-m-d H:i:s') }}</p>
                    </div>
                    @endif
                    @if($order->finish_time)
                    <div>
                        <p class="text-sm text-gray-500">完成时间</p>
                        <p class="text-gray-800">{{ $order->finish_time->format('Y-m-d H:i:s') }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- 配送信息 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">配送信息</h2>
                
                <div class="mb-6">
                    <h3 class="text-md font-medium text-gray-700 mb-2">取货信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">取货地址</p>
                            <p class="text-gray-800">{{ $order->pickup_address }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">取货联系人</p>
                            <p class="text-gray-800">{{ $order->pickup_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">取货电话</p>
                            <p class="text-gray-800">{{ $order->pickup_phone }}</p>
                        </div>
                        @if($order->pickup_code)
                        <div>
                            <p class="text-sm text-gray-500">取货验证码</p>
                            <p class="text-gray-800">{{ $order->pickup_code }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">收货信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">收货地址</p>
                            <p class="text-gray-800">{{ $order->deliver_address }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">收货联系人</p>
                            <p class="text-gray-800">{{ $order->deliver_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">收货电话</p>
                            <p class="text-gray-800">{{ $order->deliver_phone }}</p>
                        </div>
                        @if($order->receive_code)
                        <div>
                            <p class="text-sm text-gray-500">收货验证码</p>
                            <p class="text-gray-800">{{ $order->receive_code }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 商品信息 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">商品信息</h2>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-500">商品描述</p>
                    <p class="text-gray-800">{{ $order->goods_desc }}</p>
                </div>
                
                @if($order->goods_category_id)
                <div class="mb-4">
                    <p class="text-sm text-gray-500">商品类别</p>
                    <p class="text-gray-800">{{ $order->goodsCategory->name ?? '未知类别' }}</p>
                </div>
                @endif
                
                @if($order->goods_price > 0)
                <div class="mb-4">
                    <p class="text-sm text-gray-500">商品价值</p>
                    <p class="text-gray-800">¥ {{ number_format($order->goods_price / 100, 2) }}</p>
                </div>
                @endif
                
                @if($order->weight > 0)
                <div class="mb-4">
                    <p class="text-sm text-gray-500">商品重量</p>
                    <p class="text-gray-800">{{ $order->weight }} kg</p>
                </div>
                @endif
                
                @if($order->volume > 0)
                <div class="mb-4">
                    <p class="text-sm text-gray-500">商品体积</p>
                    <p class="text-gray-800">{{ $order->volume }} m³</p>
                </div>
                @endif
                
                @if($order->goods_imgs)
                <div>
                    <p class="text-sm text-gray-500 mb-2">商品图片</p>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        @foreach($order->goods_imgs as $img)
                        <div class="relative">
                            <img src="{{ $img }}" alt="商品图片" class="w-full h-24 object-cover rounded">
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <!-- 费用信息 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">费用信息</h2>
                
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">基础配送费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->freight / 100, 2) }}</span>
                    </div>
                    
                    @if($order->distance_price > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">距离附加费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->distance_price / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    @if($order->time_price > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">时段附加费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->time_price / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    @if($order->weather_price > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">天气附加费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->weather_price / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    @if($order->weight_price > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">重量附加费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->weight_price / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    @if($order->coupon_amount > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">优惠券</span>
                        <span class="text-red-500">-¥ {{ number_format($order->coupon_amount / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    @if($order->gratuity > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">小费</span>
                        <span class="text-gray-800">¥ {{ number_format($order->gratuity / 100, 2) }}</span>
                    </div>
                    @endif
                    
                    <div class="border-t border-gray-200 pt-2 mt-2">
                        <div class="flex justify-between font-medium">
                            <span class="text-gray-700">实付金额</span>
                            <span class="text-primary text-lg">¥ {{ number_format($order->actual_amount / 100, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 骑手信息 -->
        @if($order->rider)
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">骑手信息</h2>
                
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <img src="{{ $order->rider->avatar }}" alt="{{ $order->rider->name }}" class="h-12 w-12 rounded-full">
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">{{ $order->rider->name }}</p>
                        <p class="text-sm text-gray-500">{{ $order->rider->phone }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif
        
        <!-- 备注信息 -->
        @if($order->remark)
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">备注信息</h2>
                <p class="text-gray-800">{{ $order->remark }}</p>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection 