<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') - 雨骑士商家管理系统</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
    </style>
    @stack('styles')
</head>
<body class="bg-gray-100 font-sans min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-8 w-auto">
                        <span class="ml-2 text-xl font-bold text-gray-800">商家管理系统</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <nav class="hidden md:flex space-x-6">
                        <a href="{{ route('merchant.dashboard') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('merchant.dashboard') ? 'text-primary' : '' }}">
                            首页
                        </a>
                        <a href="{{ route('merchant.orders') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('merchant.orders*') ? 'text-primary' : '' }}">
                            订单管理
                        </a>
                        <a href="{{ route('merchant.recharge') }}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('merchant.recharge*') ? 'text-primary' : '' }}">
                            账户充值
                        </a>
                    </nav>
                    <div class="ml-6 relative">
                        <div class="flex items-center">
                            <span class="text-gray-700 mr-2">{{ Auth::guard('merchant')->user()->shop_name }}</span>
                            <button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <span class="sr-only">打开用户菜单</span>
                                <div class="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center">
                                    {{ mb_substr(Auth::guard('merchant')->user()->shop_name, 0, 1) }}
                                </div>
                            </button>
                        </div>
                    </div>
                    <form method="POST" action="{{ route('merchant.logout') }}" class="ml-4">
                        @csrf
                        <button type="submit" class="text-gray-500 hover:text-primary">退出登录</button>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="flex-grow">
        @yield('content')
    </main>

    <!-- 页脚 -->
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-gray-500 text-sm">© 2014-2023 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-primary transition-colors duration-200">浙ICP备2022032094号</a></p>
        </div>
    </footer>

    @stack('scripts')
</body>
</html> 