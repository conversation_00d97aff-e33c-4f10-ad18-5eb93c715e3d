<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册成功 - 雨骑士商家管理系统</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .bg-pattern {
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d54b2f' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
    </style>
</head>
<body class="bg-pattern font-sans min-h-screen flex flex-col">
    <div class="flex-grow flex items-center justify-center p-6">
        <div class="w-full max-w-md animate-fade-in">
            <div class="flex flex-col items-center mb-10">
                <div class="w-24 h-24 rounded-full gradient-primary flex items-center justify-center mb-6 shadow-lg">
                    <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-14 invert">
                </div>
                <h1 class="text-2xl font-bold text-gray-800">雨骑士商家管理系统</h1>
            </div>
            
            <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                <div class="flex flex-col items-center">
                    <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">注册成功</h2>
                    
                    <p class="text-gray-600 text-center mb-8">
                        {{ session('message') ?? '您的商家账号已创建成功，请登录使用。' }}
                    </p>
                    
                    <div class="w-full space-y-4">
                        <a href="{{ route('merchant.login') }}" class="block w-full text-center gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">
                            返回登录页
                        </a>
                        
                        <a href="/" class="block w-full text-center bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                            返回首页
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 text-center">
                <a href="/" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">返回首页</a>
                <span class="mx-2 text-gray-400">|</span>
                <a href="#" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">帮助中心</a>
            </div>
        </div>
    </div>
    
    <footer class="bg-white py-5 text-center text-gray-500 text-sm border-t border-gray-100">
        <p>© 2014-2023 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-primary transition-colors duration-200">浙ICP备2022032094号</a></p>
    </footer>
</body>
</html> 