<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨骑士商家管理系统 - 登录/注册</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .bg-pattern {
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d54b2f' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
        
        .input-focus-effect:focus {
            box-shadow: 0 0 0 2px rgba(213, 75, 47, 0.2);
        }
        
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-active {
            color: #d54b2f;
            border-bottom: 2px solid #d54b2f;
        }

        .form-container {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-pattern font-sans min-h-screen flex flex-col">
    <div class="flex-grow flex flex-col md:flex-row">
        <!-- 左侧品牌区域 - 仅在中等屏幕及以上显示 -->
        <div class="hidden md:flex md:w-1/2 gradient-primary items-center justify-center relative overflow-hidden">
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=\"100\" height=\"100\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\" fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"/%3E%3C/svg%3E')"></div>
            </div>
            <div class="max-w-md p-8 z-10 animate-fade-in">
                <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-20 mb-8 invert">
                <h1 class="text-5xl font-bold text-white mb-6">雨骑士商家管理系统</h1>
                <p class="text-white text-xl mb-10 opacity-90">为商家提供专业的配送解决方案，提升您的业务效率</p>
                <div class="grid grid-cols-2 gap-6">
                    <div class="bg-white bg-opacity-15 p-5 rounded-xl backdrop-blur-sm card-hover">
                        <h3 class="text-white font-bold text-lg mb-2">实时订单跟踪</h3>
                        <p class="text-white text-sm opacity-90">掌握每一个订单的配送动态</p>
                    </div>
                    <div class="bg-white bg-opacity-15 p-5 rounded-xl backdrop-blur-sm card-hover">
                        <h3 class="text-white font-bold text-lg mb-2">灵活配送调度</h3>
                        <p class="text-white text-sm opacity-90">提高配送效率，降低运营成本</p>
                    </div>
                    <div class="bg-white bg-opacity-15 p-5 rounded-xl backdrop-blur-sm card-hover">
                        <h3 class="text-white font-bold text-lg mb-2">专业配送团队</h3>
                        <p class="text-white text-sm opacity-90">保障服务质量，提升客户满意度</p>
                    </div>
                    <div class="bg-white bg-opacity-15 p-5 rounded-xl backdrop-blur-sm card-hover">
                        <h3 class="text-white font-bold text-lg mb-2">数据分析报表</h3>
                        <p class="text-white text-sm opacity-90">优化业务决策，提升经营效益</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧登录/注册表单 -->
        <div class="flex-1 flex items-center justify-center p-6 animate-fade-in">
            <div class="w-full max-w-md">
                <!-- 移动端Logo - 仅在小屏幕显示 -->
                <div class="md:hidden flex flex-col items-center mb-10 animate-slide-up">
                    <div class="w-24 h-24 rounded-full gradient-primary flex items-center justify-center mb-6 shadow-lg">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-14 invert">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800">雨骑士商家管理系统</h1>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                    <!-- 选项卡切换 -->
                    <div class="flex border-b border-gray-200 mb-8">
                        <button id="login-tab" class="flex-1 py-3 text-center font-medium text-lg tab-active" onclick="switchTab('login')">登录</button>
                        <button id="register-tab" class="flex-1 py-3 text-center font-medium text-lg text-gray-500" onclick="switchTab('register')">注册</button>
                    </div>
                    
                    <!-- 登录表单 -->
                    <div id="login-form" class="form-container">
                        <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">欢迎回来</h2>
                        
                        <form action="{{ route('merchant.login') }}" method="POST">
                            @csrf
                            <div class="mb-6">
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                        </svg>
                                    </div>
                                    <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入手机号" pattern="[0-9]{11}" maxlength="11">
                                </div>
                                @error('phone')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-8">
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="password" id="password" name="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入密码">
                                </div>
                                @error('password')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <!-- 添加图形验证码到登录表单 -->
                            <div class="mb-6">
                                <label for="login_captcha" class="block text-sm font-medium text-gray-700 mb-2">图形验证码</label>
                                <div class="flex space-x-3">
                                    <div class="relative flex-1">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <input type="text" id="login_captcha" name="captcha" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入图形验证码" maxlength="6">
                                    </div>
                                    <div class="h-[46px]">
                                        <img id="login-captcha-image" src="{{ route('merchant.captcha') }}" alt="验证码" class="h-full rounded-lg cursor-pointer" onclick="refreshLoginCaptcha()">
                                    </div>
                                </div>
                                @error('captcha')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="flex items-center justify-between mb-8">
                                <div class="flex items-center">
                                    <input type="checkbox" id="remember" name="remember" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="remember" class="ml-2 block text-sm text-gray-700">记住我</label>
                                </div>
                                <a href="{{ route('merchant.password.request') }}" class="text-sm text-primary hover:text-secondary transition-colors duration-200">忘记密码？</a>
                            </div>
                            
                            <div class="form-group row mb-0">
                                <div class="col-md-8 offset-md-4">
                                    <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">登录</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 注册表单 -->
                    <div id="register-form" class="form-container hidden">
                        <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">商家注册</h2>
                        
                        <form action="{{ route('merchant.register') }}" method="POST">
                            @csrf
                            <div class="mb-6">
                                <label for="shop_name" class="block text-sm font-medium text-gray-700 mb-2">商家名称</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="text" id="shop_name" name="shop_name" value="{{ old('shop_name') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入商家名称">
                                </div>
                                @error('shop_name')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-6">
                                <label for="register_phone" class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                        </svg>
                                    </div>
                                    <input type="tel" id="register_phone" name="phone" value="{{ old('phone') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入手机号" pattern="[0-9]{11}" maxlength="11">
                                </div>
                                @error('phone')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <!-- 城市选择部分 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">所在地区</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <div>
                                        <select id="province" name="province" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200">
                                            <option value="">请选择省份</option>
                                        </select>
                                    </div>
                                    <div>
                                        <select id="city" name="city" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" disabled>
                                            <option value="">请选择城市</option>
                                        </select>
                                    </div>
                                    <div>
                                        <select id="district" name="district" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" disabled>
                                            <option value="">请选择区县</option>
                                        </select>
                                    </div>
                                </div>
                                <input type="hidden" id="city_code" name="city_code" value="{{ old('city_code') }}">
                                @error('province')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                                @error('city')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                                @error('district')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                                @error('city_code')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-6">
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">详细地址</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="text" id="address" name="address" value="{{ old('address') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入详细地址">
                                </div>
                                @error('address')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <!-- 添加图形验证码在验证码字段前 -->
                            <div class="mb-6">
                                <label for="captcha" class="block text-sm font-medium text-gray-700 mb-2">图形验证码</label>
                                <div class="flex space-x-3">
                                    <div class="relative flex-1">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <input type="text" id="captcha" name="captcha" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入图形验证码" maxlength="6">
                                    </div>
                                    <div class="h-[46px]">
                                        <img id="captcha-image" src="{{ route('merchant.captcha') }}" alt="验证码" class="h-full rounded-lg cursor-pointer" onclick="refreshCaptcha()">
                                    </div>
                                </div>
                                @error('captcha')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-6">
                                <label for="verification_code" class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                                <div class="flex space-x-3">
                                    <div class="relative flex-1">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 2a1 1 0 00-1 1v1.323l-3.954 1.582A1 1 0 004 6.868V16a1 1 0 001 1h10a1 1 0 001-1V6.868a1 1 0 00-.546-.963L11 4.323V3a1 1 0 00-1-1zM9.5 6.868L4.5 8.434v6.566h11V8.434L10.5 6.868V8a1 1 0 001 1h3a1 1 0 100-2h-3a1 1 0 00-1 1v-1.132z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <input type="text" id="verification_code" name="verification_code" value="{{ old('verification_code') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入验证码" maxlength="4">
                                    </div>
                                    <button type="button" id="send_code_btn" class="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 whitespace-nowrap">获取验证码</button>
                                </div>
                                @error('verification_code')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-6">
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">设置密码</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="password" id="password" name="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请设置密码">
                                </div>
                                <p class="mt-1 text-xs text-gray-500">密码长度至少为8位，包含字母和数字</p>
                                @error('password')
                                    <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-8">
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="password" id="password_confirmation" name="password_confirmation" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请再次输入密码">
                                </div>
                            </div>
                            
                            <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">注册</button>
                        </form>
                    </div>
                </div>
                
                <div class="mt-8 text-center animate-fade-in" style="animation-delay: 0.3s;">
                    <a href="/" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">返回首页</a>
                    <span class="mx-2 text-gray-400">|</span>
                    <a href="#" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">帮助中心</a>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white py-5 text-center text-gray-500 text-sm border-t border-gray-100">
        <p>© 2014-2023 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-primary transition-colors duration-200">浙ICP备2022032094号</a></p>
    </footer>

    <script>
        // 刷新图形验证码
        function refreshCaptcha() {
            fetch('{{ route("merchant.refresh.captcha") }}')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('captcha-image').src = data.captcha + '&t=' + new Date().getTime();
                })
                .catch(error => console.error('Error refreshing captcha:', error));
        }
        
        // 刷新登录表单的验证码
        function refreshLoginCaptcha() {
            fetch('{{ route("merchant.refresh.captcha") }}')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('login-captcha-image').src = data.captcha + '&t=' + new Date().getTime();
                })
                .catch(error => console.error('Error refreshing captcha:', error));
        }
        
        function switchTab(tab) {
            const loginTab = document.getElementById('login-tab');
            const registerTab = document.getElementById('register-tab');
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');
            
            if (tab === 'login') {
                loginTab.classList.add('tab-active');
                loginTab.classList.remove('text-gray-500');
                registerTab.classList.remove('tab-active');
                registerTab.classList.add('text-gray-500');
                
                loginForm.classList.remove('hidden');
                registerForm.classList.add('hidden');
            } else {
                registerTab.classList.add('tab-active');
                registerTab.classList.remove('text-gray-500');
                loginTab.classList.remove('tab-active');
                loginTab.classList.add('text-gray-500');
                
                registerForm.classList.remove('hidden');
                loginForm.classList.add('hidden');
            }
        }

        // 验证码发送功能
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否应该显示注册选项卡
            @if(isset($showRegisterTab) && $showRegisterTab)
                switchTab('register');
            @endif
            
            const sendCodeBtn = document.getElementById('send_code_btn');
            if (sendCodeBtn) {
                sendCodeBtn.addEventListener('click', function() {
                    const phoneInput = document.getElementById('register_phone');
                    const phone = phoneInput.value.trim();
                    const captchaInput = document.getElementById('captcha');
                    const captchaValue = captchaInput.value.trim();
                    
                    // 验证手机号格式
                    if (!phone) {
                        alert('请输入手机号');
                        return;
                    }
                    
                    if (!/^1[3-9]\d{9}$/.test(phone)) {
                        alert('请输入正确的手机号格式');
                        return;
                    }
                    
                    // 验证图形验证码
                    if (!captchaValue) {
                        alert('请输入图形验证码');
                        return;
                    }
                    
                    // 禁用按钮，防止重复点击
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.textContent = '发送中...';
                    
                    // 发送AJAX请求
                    const formData = new FormData();
                    formData.append('phone', phone);
                    formData.append('captcha', captchaValue);
                    formData.append('_token', '{{ csrf_token() }}');
                    formData.append('type', 'register'); // 指定为注册验证码
                    
                    fetch('/merchant/send-verification-code', {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 开始倒计时
                            let countdown = 60;
                            const timer = setInterval(() => {
                                countdown--;
                                sendCodeBtn.textContent = `${countdown}秒后重试`;
                                
                                if (countdown <= 0) {
                                    clearInterval(timer);
                                    sendCodeBtn.disabled = false;
                                    sendCodeBtn.textContent = '获取验证码';
                                }
                            }, 1000);
                            
                            alert('验证码已发送，请查收');
                        } else {
                            alert(data.message || '发送失败，请稍后重试');
                            sendCodeBtn.disabled = false;
                            sendCodeBtn.textContent = '获取验证码';
                            // 刷新图形验证码
                            refreshCaptcha();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('发送失败，请稍后重试');
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '获取验证码';
                        // 刷新图形验证码
                        refreshCaptcha();
                    });
                });
            }
            
            // 添加验证码图片点击事件
            const captchaImage = document.getElementById('captcha-image');
            if (captchaImage) {
                captchaImage.addEventListener('click', refreshCaptcha);
            }
            
            // 添加登录表单验证码图片点击事件
            const loginCaptchaImage = document.getElementById('login-captcha-image');
            if (loginCaptchaImage) {
                loginCaptchaImage.addEventListener('click', refreshLoginCaptcha);
            }
            
            // 在页面加载时加载验证码
            refreshCaptcha();
            refreshLoginCaptcha();
            
            // 省市区联动功能
            const provinceSelect = document.getElementById('province');
            const citySelect = document.getElementById('city');
            const districtSelect = document.getElementById('district');
            const cityCodeInput = document.getElementById('city_code');

            if (provinceSelect && citySelect && districtSelect) {
                // 加载省份数据
                fetch('/api/v1/regions/provinces')
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(province => {
                            const option = document.createElement('option');
                            option.value = province.code;
                            option.textContent = province.name;
                            provinceSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error loading provinces:', error));

                // 省份选择事件
                provinceSelect.addEventListener('change', function() {
                    // 清空城市和区县选择框
                    citySelect.innerHTML = '<option value="">请选择城市</option>';
                    districtSelect.innerHTML = '<option value="">请选择区县</option>';
                    citySelect.disabled = true;
                    districtSelect.disabled = true;
                    cityCodeInput.value = '';

                    const provinceCode = this.value;
                    if (!provinceCode) return;

                    // 加载城市数据
                    fetch(`/api/v1/regions/provinces/${provinceCode}/cities`)
                        .then(response => response.json())
                        .then(data => {
                            citySelect.disabled = false;
                            data.forEach(city => {
                                const option = document.createElement('option');
                                option.value = city.code;
                                option.textContent = city.name;
                                citySelect.appendChild(option);
                            });
                        })
                        .catch(error => console.error('Error loading cities:', error));
                });

                // 城市选择事件
                citySelect.addEventListener('change', function() {
                    // 清空区县选择框
                    districtSelect.innerHTML = '<option value="">请选择区县</option>';
                    districtSelect.disabled = true;
                    cityCodeInput.value = this.value; // 设置城市编码

                    const provinceCode = provinceSelect.value;
                    const cityCode = this.value;
                    if (!provinceCode || !cityCode) return;

                    // 加载区县数据
                    fetch(`/api/v1/regions/provinces/${provinceCode}/cities/${cityCode}/districts`)
                        .then(response => response.json())
                        .then(data => {
                            districtSelect.disabled = false;
                            data.forEach(district => {
                                const option = document.createElement('option');
                                option.value = district.code;
                                option.textContent = district.name;
                                districtSelect.appendChild(option);
                            });
                        })
                        .catch(error => console.error('Error loading districts:', error));
                });
            }
        });
    </script>
</body>
</html> 