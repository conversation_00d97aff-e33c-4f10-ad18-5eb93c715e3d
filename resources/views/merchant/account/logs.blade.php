@extends('merchant.layouts.app')

@section('title', '账户流水')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">账户流水</h1>
        
        <!-- 筛选表单 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-4">
                <form action="{{ route('merchant.account.logs') }}" method="GET" class="space-y-4">
                    <div class="flex flex-wrap gap-4">
                        <!-- 类型筛选 -->
                        <div class="flex-1">
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">变动类型</label>
                            <select name="type" id="type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                <option value="">全部类型</option>
                                @foreach(\App\Models\MerchantAccountLog::TypeMap as $key => $value)
                                    <option value="{{ $key }}" {{ $currentType == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <!-- 时间范围 -->
                        <div class="flex-1">
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                            <input type="date" name="start_date" id="start_date" value="{{ $startDate }}" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                        </div>
                        <div class="flex-1">
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                            <input type="date" name="end_date" id="end_date" value="{{ $endDate }}"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="flex items-end">
                            <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 流水列表 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                时间
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                类型
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                变动金额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                变动前余额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                变动后余额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                关联订单
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                备注
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($logs as $log)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $log->created_at->format('Y-m-d H:i:s') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    @if($log->type == \App\Models\MerchantAccountLog::TYPE_RECHARGE)
                                        bg-green-100 text-green-800
                                    @elseif($log->type == \App\Models\MerchantAccountLog::TYPE_ORDER)
                                        bg-blue-100 text-blue-800
                                    @elseif($log->type == \App\Models\MerchantAccountLog::TYPE_REFUND)
                                        bg-yellow-100 text-yellow-800
                                    @else
                                        bg-gray-100 text-gray-800
                                    @endif
                                ">
                                    {{ $log->type_text }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium 
                                @if($log->amount > 0)
                                    text-green-600
                                @else
                                    text-red-600
                                @endif
                            ">
                                {{ $log->amount > 0 ? '+' : '' }}{{ $log->amount_yuan }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $log->before_balance_yuan }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $log->after_balance_yuan }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @if($log->order_no)
                                    <a href="{{ route('merchant.orders.show', ['id' => $log->order_no]) }}" class="text-primary hover:underline">
                                        {{ $log->order_no }}
                                    </a>
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $log->remark ?: '-' }}
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                暂无流水记录
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            @if($logs->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $logs->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection 