@extends('merchant.layouts.app')


@section('title', '充值支付')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">充值支付</h1>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">订单信息</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">订单号</p>
                            <p class="text-gray-800">{{ $order->order_no }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">充值金额</p>
                            <p class="text-xl font-bold text-primary">¥ {{ number_format($order->amount, 2) }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">创建时间</p>
                            <p class="text-gray-800">{{ $order->created_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">订单状态</p>
                            <p class="text-gray-800">{{ \App\Models\MerchantRechargeOrder::$statusMap[$order->status] }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 pt-6">
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">选择支付方式</h2>
                    
                    <div class="flex flex-col items-center">
                        <div class="mb-6 w-full">
                            <div class="flex justify-center space-x-4 mb-6">
                                <div class="relative">
                                    <input type="radio" name="payment_method" id="pay_alipay" value="{{ \App\Models\MerchantRechargeOrder::PAY_METHOD_ALIPAY }}" class="hidden peer payment-radio" checked>
                                    <label for="pay_alipay" class="flex items-center px-6 py-3 border-2 rounded-lg cursor-pointer transition-all duration-200 peer-checked:border-primary hover:bg-gray-50">
                                        <div class="flex-shrink-0 mr-2">
                                            <img src="/images/payment/alipay.svg" alt="支付宝" class="h-6">
                                        </div>
                                        <span class="font-medium text-gray-800">支付宝</span>
                                    </label>
                                </div>
                                
                                <div class="relative">
                                    <input type="radio" name="payment_method" id="pay_wechat" value="{{ \App\Models\MerchantRechargeOrder::PAY_METHOD_WECHAT }}" class="hidden peer payment-radio">
                                    <label for="pay_wechat" class="flex items-center px-6 py-3 border-2 rounded-lg cursor-pointer transition-all duration-200 peer-checked:border-primary hover:bg-gray-50">
                                        <div class="flex-shrink-0 mr-2">
                                            <img src="/images/payment/wechat.svg" alt="微信支付" class="h-6">
                                        </div>
                                        <span class="font-medium text-gray-800">微信支付</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div id="qrcode-container" class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg">
                                <div id="qrcode" class="mb-4 flex items-center justify-center">
                                    <div class="animate-pulse flex flex-col items-center">
                                        <div class="w-48 h-48 bg-gray-200 rounded-lg"></div>
                                        <div class="mt-4 text-gray-500">正在生成支付二维码...</div>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-500 text-center">请使用<span id="payment-method-text">支付宝</span>扫描二维码完成支付</p>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <button id="refresh-qrcode" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                刷新二维码
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">支付说明</h2>
                <ul class="text-gray-600 space-y-2">
                    <li>1. 请在30分钟内完成支付，超时订单将自动关闭</li>
                    <li>2. 支付成功后，充值金额将自动添加到您的账户余额</li>
                    <li>3. 如遇支付问题，请点击"刷新二维码"按钮重新获取</li>
                    <li>4. 支付完成后，系统将自动跳转到充值记录页面</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- 使用多个CDN源加载QRCode库，提高可用性 -->
<script>
    // QRCode库加载状态
    window.qrcodeLoaded = false;
    
    // 尝试加载QRCode库的函数
    function loadQRCodeLib(urls, index = 0) {
        if (index >= urls.length) {
            console.error('所有QRCode库源都加载失败');
            return;
        }
        
        var script = document.createElement('script');
        script.src = urls[index];
        script.onload = function() {
            console.log('QRCode库成功加载: ' + urls[index]);
            window.qrcodeLoaded = true;
        };
        script.onerror = function() {
            console.warn('QRCode库加载失败: ' + urls[index] + '，尝试下一个源');
            loadQRCodeLib(urls, index + 1);
        };
        document.head.appendChild(script);
    }
    
    // 加载QRCode库的多个源（按优先级排序）
    loadQRCodeLib([
        // 国内CDN源优先
        'https://lib.baomitu.com/qrcodejs/1.0.0/qrcode.min.js', // 360奇舞团CDN
        'https://cdn.bootcdn.cn/ajax/libs/qrcodejs/1.0.0/qrcode.min.js', // BootCDN
        'https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js', // 字节跳动CDN
        'https://cdn.staticfile.org/qrcodejs/1.0.0/qrcode.min.js', // 七牛云Staticfile CDN
        // 备用国际CDN源
        'https://cdn.jsdelivr.net/npm/qrcode.js@1.0.0/qrcode.min.js',
        'https://unpkg.com/qrcode.js@1.0.0/qrcode.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/qrcode.js/1.0.0/qrcode.min.js',
        // 本地备份
        '{{ asset("js/libs/qrcode.min.js") }}' // 本地备份，需要下载文件到public/js/libs/目录
    ]);
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const qrcodeContainer = document.getElementById('qrcode');
        const paymentMethodText = document.getElementById('payment-method-text');
        const refreshButton = document.getElementById('refresh-qrcode');
        const paymentRadios = document.querySelectorAll('.payment-radio');
        
        // 初始加载二维码，但检查库是否已加载
        checkQRCodeLibAndLoad();
        
        // 切换支付方式时重新加载二维码
        paymentRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.id === 'pay_alipay') {
                    paymentMethodText.textContent = '支付宝';
                } else {
                    paymentMethodText.textContent = '微信';
                }
                checkQRCodeLibAndLoad();
            });
        });
        
        // 刷新二维码
        refreshButton.addEventListener('click', function() {
            checkQRCodeLibAndLoad();
        });
        
        // 检查QRCode库是否已加载，然后执行加载二维码
        function checkQRCodeLibAndLoad() {
            if (window.qrcodeLoaded && typeof QRCode === 'function') {
                loadQRCode();
            } else {
                // 如果库尚未加载，显示加载状态并等待
                qrcodeContainer.innerHTML = `
                    <div class="flex flex-col items-center text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                        <p class="text-gray-500">正在加载二维码生成库...</p>
                    </div>
                `;
                
                // 定时检查库是否加载完成
                let checkCount = 0;
                const checkLibInterval = setInterval(function() {
                    checkCount++;
                    if (window.qrcodeLoaded && typeof QRCode === 'function') {
                        clearInterval(checkLibInterval);
                        loadQRCode();
                    } else if (checkCount > 10) { // 5秒后仍未加载成功
                        clearInterval(checkLibInterval);
                        qrcodeContainer.innerHTML = `
                            <div class="text-red-500 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p>二维码生成库加载失败</p>
                                <button class="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark" onclick="checkQRCodeLibAndLoad()">重试</button>
                            </div>
                        `;
                    }
                }, 500); // 每500毫秒检查一次
            }
        }
        
        // 加载二维码
        function loadQRCode() {
            const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
            
            // 显示加载状态
            qrcodeContainer.innerHTML = `
                <div class="animate-pulse flex flex-col items-center">
                    <div class="w-48 h-48 bg-gray-200 rounded-lg"></div>
                    <div class="mt-4 text-gray-500">正在生成支付二维码...</div>
                </div>
            `;
            
            // 发送请求获取支付二维码
            fetch('{{ route('merchant.recharge.qrcode', ['order_no' => $order->order_no]) }}?payment_method=' + paymentMethod)
                .then(response => response.json())
                .then(data => {
                    console.log('二维码API返回数据:', data);  // 添加调试信息
                    
                    if (data.success && data.code_url) {
                        try {
                            // 清空容器
                            qrcodeContainer.innerHTML = '';
                            
                            // 创建二维码容器
                            const qrDiv = document.createElement('div');
                            qrDiv.className = 'mb-4 flex justify-center';
                            qrcodeContainer.appendChild(qrDiv);
                            
                            // 使用 QRCode.js 生成二维码
                            new QRCode(qrDiv, {
                                text: data.code_url,
                                width: 200,
                                height: 200,
                                colorDark: "#000000",
                                colorLight: "#ffffff",
                                correctLevel: QRCode.CorrectLevel.H,
                                margin: 2
                            });
                            
                            // 检查二维码是否生成成功
                            if (qrDiv.querySelector('img') || qrDiv.querySelector('canvas')) {
                                console.log('二维码生成成功');
                                // 开始轮询订单状态
                                checkOrderStatus();
                            } else {
                                throw new Error('二维码元素未创建');
                            }
                        } catch (err) {
                            console.error('生成二维码出错:', err);
                            qrcodeContainer.innerHTML = `
                                <div class="text-red-500 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p>生成二维码失败: ${err.message}</p>
                                    <button class="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark" onclick="checkQRCodeLibAndLoad()">重试</button>
                                </div>
                            `;
                        }
                    } else {
                        console.error('API返回错误或无code_url:', data);
                        qrcodeContainer.innerHTML = `
                            <div class="text-red-500 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p>${data.message || '获取二维码链接失败，请点击重试'}</p>
                                <button class="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark" onclick="checkQRCodeLibAndLoad()">重试</button>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('请求二维码API出错:', error);
                    qrcodeContainer.innerHTML = `
                        <div class="text-red-500 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p>网络错误，请点击重试</p>
                            <button class="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark" onclick="checkQRCodeLibAndLoad()">重试</button>
                        </div>
                    `;
                });
        }
        
        // 检查订单状态
        function checkOrderStatus() {
            const checkInterval = setInterval(function() {
                fetch('{{ route('merchant.recharge.check-status', ['order_no' => $order->order_no]) }}')
                    .then(response => response.json())
                    .then(data => {
                        if (data.paid) {
                            clearInterval(checkInterval);
                            // 显示支付成功
                            qrcodeContainer.innerHTML = `
                                <div class="text-green-500 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <p class="text-lg font-semibold">支付成功！</p>
                                    <p class="mt-2">页面将在3秒后跳转...</p>
                                </div>
                            `;
                            
                            // 3秒后跳转到充值记录页面
                            setTimeout(function() {
                                window.location.href = '{{ route('merchant.recharge.records') }}';
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('检查订单状态出错:', error);
                    });
            }, 3000); // 每3秒检查一次
            
            // 5分钟后停止检查
            setTimeout(function() {
                clearInterval(checkInterval);
            }, 5 * 60 * 1000);
        }
    });
</script>
@endpush 