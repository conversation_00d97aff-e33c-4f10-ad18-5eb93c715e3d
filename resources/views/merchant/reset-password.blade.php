<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨骑士商家管理系统 - 重置密码</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .bg-pattern {
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d54b2f' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
        
        .input-focus-effect:focus {
            box-shadow: 0 0 0 2px rgba(213, 75, 47, 0.2);
        }
    </style>
</head>
<body class="bg-pattern font-sans min-h-screen flex flex-col">
    <div class="flex-grow flex flex-col md:flex-row">
        <!-- 左侧品牌区域 - 仅在中等屏幕及以上显示 -->
        <div class="hidden md:flex md:w-1/2 gradient-primary items-center justify-center relative overflow-hidden">
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=\"100\" height=\"100\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\" fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"/%3E%3C/svg%3E')"></div>
            </div>
            <div class="max-w-md p-8 z-10 animate-fade-in">
                <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-20 mb-8 invert">
                <h1 class="text-5xl font-bold text-white mb-6">雨骑士商家管理系统</h1>
                <p class="text-white text-xl mb-10 opacity-90">为商家提供专业的配送解决方案，提升您的业务效率</p>
            </div>
        </div>
        
        <!-- 右侧重置密码表单 -->
        <div class="flex-1 flex items-center justify-center p-6 animate-fade-in">
            <div class="w-full max-w-md">
                <!-- 移动端Logo - 仅在小屏幕显示 -->
                <div class="md:hidden flex flex-col items-center mb-10 animate-slide-up">
                    <div class="w-24 h-24 rounded-full gradient-primary flex items-center justify-center mb-6 shadow-lg">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-14 invert">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800">雨骑士商家管理系统</h1>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">重置密码</h2>
                    
                    <form method="POST" action="{{ route('merchant.password.update') }}">
                        @csrf

                        <input type="hidden" name="token" value="{{ $token }}">
                        
                        <div class="mb-6">
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">电子邮箱</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                </div>
                                <input id="email" type="email" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" name="email" value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus readonly>
                            </div>
                            @error('email')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input id="password" type="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" name="password" required autocomplete="new-password" placeholder="请输入新密码">
                            </div>
                            @error('password')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-8">
                            <label for="password-confirm" class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input id="password-confirm" type="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" name="password_confirmation" required autocomplete="new-password" placeholder="请再次输入新密码">
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">
                                重置密码
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="mt-8 text-center animate-fade-in" style="animation-delay: 0.3s;">
                    <a href="/" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">返回首页</a>
                    <span class="mx-2 text-gray-400">|</span>
                    <a href="#" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">帮助中心</a>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white py-5 text-center text-gray-500 text-sm border-t border-gray-100">
        <p>© 2014-2023 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-primary transition-colors duration-200">浙ICP备2022032094号</a></p>
    </footer>
</body>
</html> 