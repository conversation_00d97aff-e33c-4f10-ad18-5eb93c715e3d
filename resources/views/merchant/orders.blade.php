@extends('merchant.layouts.app')

@section('title', '订单管理')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">订单管理</h1>
        
        @if(session('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p>{{ session('error') }}</p>
        </div>
        @endif
        
        <!-- 订单状态筛选 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-4">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('merchant.orders') }}" class="px-4 py-2 rounded-md {{ !$currentStatus ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        全部
                    </a>
                    <a href="{{ route('merchant.orders', ['status' => \App\Models\O2oErrandOrder::STATUS_PAID]) }}" class="px-4 py-2 rounded-md {{ $currentStatus == \App\Models\O2oErrandOrder::STATUS_PAID ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        待接单
                    </a>
                    <a href="{{ route('merchant.orders', ['status' => \App\Models\O2oErrandOrder::STATUS_PICKUP]) }}" class="px-4 py-2 rounded-md {{ $currentStatus == \App\Models\O2oErrandOrder::STATUS_PICKUP ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        待取货
                    </a>
                    <a href="{{ route('merchant.orders', ['status' => \App\Models\O2oErrandOrder::STATUS_DELIVERY]) }}" class="px-4 py-2 rounded-md {{ $currentStatus == \App\Models\O2oErrandOrder::STATUS_DELIVERY ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        派送中
                    </a>
                    <a href="{{ route('merchant.orders', ['status' => \App\Models\O2oErrandOrder::STATUS_FINISH]) }}" class="px-4 py-2 rounded-md {{ $currentStatus == \App\Models\O2oErrandOrder::STATUS_FINISH ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        已完成
                    </a>
                    <a href="{{ route('merchant.orders', ['status' => \App\Models\O2oErrandOrder::STATUS_CANCEL]) }}" class="px-4 py-2 rounded-md {{ $currentStatus == \App\Models\O2oErrandOrder::STATUS_CANCEL ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                        已取消
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 订单列表 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                订单号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                订单类型
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                配送地址
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                订单金额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                创建时间
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($orders as $order)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $order->order_no }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ \App\Models\O2oErrandOrder::TypeMap[$order->type] ?? '未知' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $order->deliver_address }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ¥ {{ number_format($order->actual_amount / 100, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $order->created_at->format('Y-m-d H:i:s') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    @if($order->order_status == \App\Models\O2oErrandOrder::STATUS_CANCEL)
                                        bg-gray-100 text-gray-800
                                    @elseif($order->order_status == \App\Models\O2oErrandOrder::STATUS_WAITING_PAY)
                                        bg-yellow-100 text-yellow-800
                                    @elseif($order->order_status == \App\Models\O2oErrandOrder::STATUS_FINISH)
                                        bg-green-100 text-green-800
                                    @else
                                        bg-blue-100 text-blue-800
                                    @endif
                                ">
                                    {{ \App\Models\O2oErrandOrder::StatusMap[$order->order_status] ?? '未知状态' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="{{ route('merchant.orders.show', ['id' => $order->id]) }}" class="text-primary hover:underline">查看详情</a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                暂无订单记录
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            @if($orders->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $orders->appends(['status' => $currentStatus])->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection 