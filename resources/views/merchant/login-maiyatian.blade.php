<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨骑士商家管理系统 - 麦芽田授权登录</title>
    <meta name="description" content="雨骑士商家管理系统，为商家提供专业的配送解决方案">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .bg-pattern {
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d54b2f' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #d54b2f 0%, #ff9500 100%);
        }
        
        .input-focus-effect:focus {
            box-shadow: 0 0 0 2px rgba(213, 75, 47, 0.2);
        }
        
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-pattern font-sans min-h-screen flex flex-col">
    <div class="flex-grow flex flex-col md:flex-row">
        <!-- 左侧品牌区域 - 仅在中等屏幕及以上显示 -->
        <div class="hidden md:flex md:w-1/2 gradient-primary items-center justify-center relative overflow-hidden">
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=\"100\" height=\"100\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\" fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"/%3E%3C/svg%3E')"></div>
            </div>
            <div class="max-w-md p-8 z-10 animate-fade-in">
                <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-20 mb-8 invert">
                <h1 class="text-5xl font-bold text-white mb-6">雨骑士商家管理系统</h1>
                <p class="text-white text-xl mb-6 opacity-90">麦芽田授权绑定</p>
                <p class="text-white text-md mb-10 opacity-80">将您的麦芽田账号与雨骑士商家账号关联，享受便捷配送服务</p>
            </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="flex-1 flex items-center justify-center p-6 animate-fade-in">
            <div class="w-full max-w-md">
                <!-- 移动端Logo - 仅在小屏幕显示 -->
                <div class="md:hidden flex flex-col items-center mb-10 animate-slide-up">
                    <div class="w-24 h-24 rounded-full gradient-primary flex items-center justify-center mb-6 shadow-lg">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-14 invert">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800">麦芽田授权绑定</h1>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-xl animate-slide-up">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">麦芽田授权登录</h2>
                    
                    <div class="mb-6">
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        请使用您的雨骑士商家账号登录，完成与麦芽田的授权绑定
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form action="{{ route('merchant.login.maiyatian.post') }}" method="POST">
                        @csrf
                        <!-- 隐藏字段，传递麦芽田授权参数 -->
                        <input type="hidden" name="code" value="{{ $code }}">
                        <input type="hidden" name="redirect_uri" value="{{ $redirect_uri }}">
                        <input type="hidden" name="state" value="{{ $state }}">
                        <input type="hidden" name="source" value="{{ $source }}">
                        
                        <!-- 显示授权绑定错误 -->
                        @error('binding_error')
                            <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700">
                                            {{ $message }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @enderror
                        
                        <div class="mb-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                    </svg>
                                </div>
                                <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入手机号" pattern="[0-9]{11}" maxlength="11">
                            </div>
                            @error('phone')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-8">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input type="password" id="password" name="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent input-focus-effect transition-all duration-200" placeholder="请输入密码">
                            </div>
                            @error('password')
                                <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <a href="{{ route('merchant.register') }}" class="text-sm text-primary hover:text-secondary transition-colors duration-200">还没有账号？立即注册</a>
                        </div>
                        
                        <button type="submit" class="w-full gradient-primary text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-transform">授权绑定</button>
                    </form>
                </div>
                
                <div class="mt-8 text-center animate-fade-in" style="animation-delay: 0.3s;">
                    <a href="/" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">返回首页</a>
                    <span class="mx-2 text-gray-400">|</span>
                    <a href="#" class="text-sm text-gray-600 hover:text-primary transition-colors duration-200">帮助中心</a>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white py-5 text-center text-gray-500 text-sm border-t border-gray-100">
        <p>© 2014-2023 宁波市万戬生活服务有限公司 版权所有 <a href="https://beian.miit.gov.cn/" target="_blank" class="hover:text-primary transition-colors duration-200">浙ICP备2022032094号</a></p>
    </footer>
</body>
</html> 