@extends('merchant.layouts.app')

@section('title', '账户充值')

@section('content')
<div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">账户充值</h1>
        <p class="mt-1 text-sm text-gray-600">为您的商家账户充值，以便使用配送服务</p>
    </div>

    @if (session('error'))
    <div class="mb-4 bg-red-50 border-l-4 border-red-400 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-red-700">{{ session('error') }}</p>
            </div>
        </div>
    </div>
    @endif

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-primary rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">当前账户余额</h3>
                        <div class="mt-2 text-3xl font-bold text-gray-900">¥ {{ number_format($merchant->balance/100, 2) }}</div>
                    </div>
                </div>
            </div>

            <form action="{{ route('merchant.recharge') }}" method="POST" class="space-y-6">
                @csrf
                
                <!-- 自定义充值金额 - 优化后更加明显 -->
                <div class="bg-blue-50 p-4 rounded-lg border-2 border-blue-200">
                    <label for="amount" class="block text-base font-medium text-gray-800">
                        <span class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                            </svg>
                            自定义充值金额
                        </span>
                    </label>
                    <p class="text-sm text-blue-600 mb-2">您可以在下方输入任意金额进行充值（最低1元）</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">¥</span>
                        </div>
                        <input type="number" name="amount" id="amount" min="1" step="0.01" class="focus:ring-primary focus:border-primary block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md text-lg py-3" placeholder="请输入充值金额" required>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">CNY</span>
                        </div>
                    </div>
                    @error('amount')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">快速选择金额</label>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <button type="button" class="amount-btn py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" data-amount="100">¥100</button>
                        <button type="button" class="amount-btn py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" data-amount="200">¥200</button>
                        <button type="button" class="amount-btn py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" data-amount="500">¥500</button>
                        <button type="button" class="amount-btn py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" data-amount="1000">¥1000</button>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        立即充值
                    </button>
                </div>
            </form>

            <div class="mt-8">
                <h4 class="text-sm font-medium text-gray-900 mb-2">充值说明</h4>
                <ul class="text-sm text-gray-600 space-y-1 list-disc pl-5">
                    <li>充值金额最低1元，无上限</li>
                    <li>充值成功后，金额将立即添加到您的账户余额中</li>
                    <li>账户余额可用于支付配送费用</li>
                    <li>如有充值问题，请联系客服</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount');
        const amountButtons = document.querySelectorAll('.amount-btn');
        
        amountButtons.forEach(button => {
            button.addEventListener('click', function() {
                const amount = this.getAttribute('data-amount');
                amountInput.value = amount;
                
                // 移除所有按钮的选中状态
                amountButtons.forEach(btn => {
                    btn.classList.remove('bg-primary-light', 'text-primary', 'border-primary');
                    btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
                });
                
                // 添加当前按钮的选中状态
                this.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
                this.classList.add('bg-primary-light', 'text-primary', 'border-primary');
            });
        });
    });
</script>
@endsection 