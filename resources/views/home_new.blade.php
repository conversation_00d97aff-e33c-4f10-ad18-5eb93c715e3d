<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雨骑士 - 同城即时配送服务平台</title>
    <meta name="description" content="雨骑士是一家专业的同城即时配送服务平台，为用户提供帮取送、帮买、全能帮等多样化同城即时服务">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#d54b2f',
                        secondary: '#ff9500',
                        dark: '#333333',
                        light: '#f8f9fa',
                    },
                    fontFamily: {
                        sans: ['Helvetica', 'Arial', 'sans-serif'],
                    },
                    boxShadow: {
                        'custom': '0 4px 20px rgba(0, 0, 0, 0.08)',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, #d54b2f, #ff9500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background-color: #d54b2f;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .hero-bg {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3QgaWQ9InBhdHRlcm4tYmFja2dyb3VuZCIgd2lkdGg9IjQwMCUiIGhlaWdodD0iNDAwJSIgZmlsbD0icmdiYSgyNDgsIDI0OSwgMjUwLCAxKSI+PC9yZWN0PjxjaXJjbGUgZmlsbD0icmdiYSgyMTMsIDc1LCA0NywgMC4wMylAIiBjeD0iMjAiIGN5PSIyMCIgcj0iMSI+PC9jaXJjbGU+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCBmaWxsPSJ1cmwoI3BhdHRlcm4pIiBoZWlnaHQ9IjEwMCUiIHdpZHRoPSIxMDAlIj48L3JlY3Q+PC9zdmc+');
            background-color: #f8f9fa;
        }
    </style>
</head>
<body class="font-sans text-gray-800 bg-light">
    <!-- 导航栏 -->
    <header class="bg-white shadow-custom fixed w-full z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="#" class="flex items-center">
                        <img src="/images/homepage/yqs/logo.svg" alt="雨骑士" class="h-12">
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="nav-link text-dark hover:text-primary transition duration-300 font-medium">首页</a>
                    <a href="#intro" class="nav-link text-dark hover:text-primary transition duration-300 font-medium">服务介绍</a>
                    <a href="#about" class="nav-link text-dark hover:text-primary transition duration-300 font-medium">关于我们</a>
                    <a href="#contact" class="nav-link text-dark hover:text-primary transition duration-300 font-medium">联系我们</a>
                    <a href="/merchant/login" class="bg-primary text-white px-5 py-2.5 rounded-full hover:bg-opacity-90 transition duration-300 shadow-md transform hover:-translate-y-0.5">商家登录</a>
                </div>
                
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-dark hover:text-primary transition duration-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-100">
            <div class="container mx-auto px-4 py-3">
                <a href="#" class="block py-2.5 text-dark hover:text-primary transition duration-300">首页</a>
                <a href="#intro" class="block py-2.5 text-dark hover:text-primary transition duration-300">服务介绍</a>
                <a href="#about" class="block py-2.5 text-dark hover:text-primary transition duration-300">关于我们</a>
                <a href="#contact" class="block py-2.5 text-dark hover:text-primary transition duration-300">联系我们</a>
                <a href="/merchant/login" class="block py-2.5 text-primary font-medium">商家登录</a>
            </div>
        </div>
    </header>

    <!-- 英雄区域 -->
    <section class="pt-32 pb-20 hero-bg">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="max-w-lg">
                        <h5 class="text-primary font-semibold mb-3 flex items-center">
                            <span class="inline-block w-8 h-0.5 bg-primary mr-3"></span>
                            同城即时配送专家
                        </h5>
                        <h1 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                            <span class="gradient-text">雨骑士</span> 
                            <span class="block mt-2">让配送更高效</span>
                        </h1>
                        <p class="text-lg text-gray-600 mb-8 leading-relaxed">一呼百应，为您提供高效便捷的同城即时服务，最快3秒响应，10分钟上门，平均45分钟完成服务。</p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="https://sj.qq.com/appdetail/com.dingdong.yuqishi" class="bg-primary text-white px-8 py-3.5 rounded-full text-center hover:bg-opacity-90 transition duration-300 shadow-md transform hover:-translate-y-0.5 flex items-center justify-center" target="_blank">
                                <i class="fab fa-android mr-2"></i> Android下载
                            </a>
                            <a href="https://apps.apple.com/cn/app/%E9%9B%A8%E9%AA%91%E5%A3%AB%E4%BC%97%E5%8C%85/id6447380380" class="bg-white text-primary border-2 border-primary px-8 py-3.5 rounded-full text-center hover:bg-gray-50 transition duration-300 shadow-md transform hover:-translate-y-0.5 flex items-center justify-center" target="_blank">
                                <i class="fab fa-apple mr-2"></i> iOS下载
                            </a>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 relative">
                    <div class="absolute -top-10 -left-10 w-20 h-20 bg-primary bg-opacity-10 rounded-full"></div>
                    <div class="absolute -bottom-5 -right-5 w-16 h-16 bg-secondary bg-opacity-10 rounded-full"></div>
                    <div class="relative bg-white p-3 rounded-2xl shadow-custom">
                        <img src="/images/homepage/yqs/index.png" alt="雨骑士APP展示" class="w-full max-w-lg mx-auto rounded-xl animate-float">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务特点 -->
    <section class="py-24 bg-white" id="intro">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h5 class="text-primary font-semibold mb-3 inline-flex items-center justify-center">
                    <span class="inline-block w-8 h-0.5 bg-primary mr-3"></span>
                    我们的服务
                    <span class="inline-block w-8 h-0.5 bg-primary ml-3"></span>
                </h5>
                <h2 class="text-3xl md:text-4xl font-bold mb-4">为您提供多样化的同城即时服务</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">雨骑士致力于打造高效、便捷、安全的同城配送生态系统，满足您的各种需求</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 帮买服务 -->
                <div class="bg-white rounded-xl p-8 text-center hover:shadow-custom transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                    <div class="bg-primary bg-opacity-10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">帮买服务</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">为您代买商品，送货上门，省时省力。美食生鲜百货，足不出户轻松购全城。</p>
                    <div class="flex justify-center">
                        <a href="#" class="text-primary font-medium inline-flex items-center hover:underline">
                            了解更多
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- 帮取送 -->
                <div class="bg-white rounded-xl p-8 text-center hover:shadow-custom transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                    <div class="bg-primary bg-opacity-10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">帮取送</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">快速取件、送件，安全可靠的配送服务。文件资料重要物品，安心托付急速送同城。</p>
                    <div class="flex justify-center">
                        <a href="#" class="text-primary font-medium inline-flex items-center hover:underline">
                            了解更多
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- 全能帮 -->
                <div class="bg-white rounded-xl p-8 text-center hover:shadow-custom transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                    <div class="bg-primary bg-opacity-10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">全能帮</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">多样化的生活服务，满足您的各种需求。宝贵时间不浪费，雨骑士轻松应对帮您解决。</p>
                    <div class="flex justify-center">
                        <a href="#" class="text-primary font-medium inline-flex items-center hover:underline">
                            了解更多
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 服务优势 -->
            <div class="mt-20 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="flex flex-col items-center p-6 bg-gray-50 rounded-lg">
                    <div class="text-primary text-4xl font-bold mb-2">3秒</div>
                    <p class="text-gray-700 text-center">最快响应时间</p>
                </div>
                <div class="flex flex-col items-center p-6 bg-gray-50 rounded-lg">
                    <div class="text-primary text-4xl font-bold mb-2">10分钟</div>
                    <p class="text-gray-700 text-center">最快上门时间</p>
                </div>
                <div class="flex flex-col items-center p-6 bg-gray-50 rounded-lg">
                    <div class="text-primary text-4xl font-bold mb-2">45分钟</div>
                    <p class="text-gray-700 text-center">平均完成服务时间</p>
                </div>
                <div class="flex flex-col items-center p-6 bg-gray-50 rounded-lg">
                    <div class="text-primary text-4xl font-bold mb-2">24小时</div>
                    <p class="text-gray-700 text-center">全天候服务</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section class="py-24 bg-gray-50" id="about">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h5 class="text-primary font-semibold mb-3 inline-flex items-center justify-center">
                    <span class="inline-block w-8 h-0.5 bg-primary mr-3"></span>
                    关于我们
                    <span class="inline-block w-8 h-0.5 bg-primary ml-3"></span>
                </h5>
                <h2 class="text-3xl md:text-4xl font-bold mb-4">了解雨骑士</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">我们致力于打造高效、便捷、安全的同城配送生态系统</p>
            </div>
            
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0 relative">
                    <div class="absolute -top-5 -left-5 w-24 h-24 bg-primary bg-opacity-10 rounded-full z-0"></div>
                    <div class="absolute -bottom-5 -right-5 w-32 h-32 bg-secondary bg-opacity-10 rounded-full z-0"></div>
                    <img src="/images/homepage/<EMAIL>" alt="关于雨骑士" class="w-full max-w-lg mx-auto rounded-lg shadow-custom relative z-10">
                </div>
                <div class="md:w-1/2 md:pl-16">
                    <h2 class="text-3xl font-bold mb-6">雨骑士的故事</h2>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        雨骑士是一家专业的同城生活服务平台，为附近的人提供帮取送，帮买，全能帮等多样化同城即时服务，为中小企业，电商，本地商户提供安全可靠的专业高端配送服务。
                    </p>
                    <p class="text-gray-600 mb-8 leading-relaxed">
                        我们致力于打造高效、便捷、安全的同城配送生态系统，让用户享受更好的服务体验，让商家获得更高效的配送解决方案。
                    </p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                        <div class="flex items-start">
                            <div class="bg-primary bg-opacity-10 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold mb-2">安全可靠</h4>
                                <p class="text-gray-600">严格的骑手筛选和培训，确保您的物品安全送达</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-primary bg-opacity-10 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold mb-2">高效便捷</h4>
                                <p class="text-gray-600">智能调度系统，最快3秒响应，10分钟上门</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#" class="bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-opacity-90 transition duration-300 shadow-md transform hover:-translate-y-0.5 flex items-center justify-center">
                            <i class="fas fa-info-circle mr-2"></i> 了解更多
                        </a>
                        <a href="#contact" class="bg-white text-primary border-2 border-primary px-6 py-3 rounded-full text-center hover:bg-gray-50 transition duration-300 shadow-md transform hover:-translate-y-0.5 flex items-center justify-center">
                            <i class="fas fa-handshake mr-2"></i> 商家合作
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 数据统计 -->
            <div class="mt-20 py-10 bg-white rounded-xl shadow-custom">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="flex flex-col items-center">
                        <div class="text-primary text-5xl font-bold mb-2">5000+</div>
                        <p class="text-gray-700">活跃骑手</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-primary text-5xl font-bold mb-2">100万+</div>
                        <p class="text-gray-700">累计用户</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-primary text-5xl font-bold mb-2">98%</div>
                        <p class="text-gray-700">用户满意度</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-primary text-5xl font-bold mb-2">50+</div>
                        <p class="text-gray-700">覆盖城市</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 商家管理系统 -->
    <section class="py-24 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h5 class="text-primary font-semibold mb-3 inline-flex items-center justify-center">
                    <span class="inline-block w-8 h-0.5 bg-primary mr-3"></span>
                    商家解决方案
                    <span class="inline-block w-8 h-0.5 bg-primary ml-3"></span>
                </h5>
                <h2 class="text-3xl md:text-4xl font-bold mb-4">专业的商家管理系统</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">为商家提供专业的配送解决方案，提升您的业务效率</p>
            </div>
            
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-16 mb-10 md:mb-0 order-2 md:order-1">
                    <h3 class="text-2xl font-bold mb-6">提升您的业务效率</h3>
                    <p class="text-gray-600 mb-8 leading-relaxed">
                        雨骑士商家管理系统是一个专为商家设计的配送管理平台，帮助您高效管理订单、调度骑手、跟踪配送进度，提升客户满意度。
                    </p>
                    
                    <div class="space-y-6 mb-10">
                        <div class="flex items-start bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md">
                            <div class="bg-primary bg-opacity-10 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold mb-1">实时订单跟踪</h4>
                                <p class="text-gray-600">全程掌握每一个订单的配送动态，随时了解配送进度</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md">
                            <div class="bg-primary bg-opacity-10 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold mb-1">灵活的配送调度</h4>
                                <p class="text-gray-600">智能调度系统，自动匹配最优骑手，提高配送效率</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md">
                            <div class="bg-primary bg-opacity-10 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold mb-1">数据分析报表</h4>
                                <p class="text-gray-600">全面的数据分析，帮助您优化业务决策，提升经营效益</p>
                            </div>
                        </div>
                    </div>
                    
                    <a href="/merchant/login" class="bg-secondary text-white px-8 py-3.5 rounded-full text-center hover:bg-opacity-90 transition duration-300 shadow-md transform hover:-translate-y-0.5 inline-flex items-center">
                        <i class="fas fa-sign-in-alt mr-2"></i> 立即登录商家系统
                    </a>
                </div>
                <div class="md:w-1/2 order-1 md:order-2 relative">
                    <div class="absolute -top-10 -right-10 w-24 h-24 bg-secondary bg-opacity-10 rounded-full z-0"></div>
                    <div class="absolute -bottom-10 -left-10 w-32 h-32 bg-primary bg-opacity-10 rounded-full z-0"></div>
                    <div class="relative z-10 bg-white p-4 rounded-2xl shadow-custom transform rotate-1 hover:rotate-0 transition-all duration-500">
                        <div class="bg-gray-50 p-2 rounded-xl">
                            <img src="/images/homepage/yqs/index.png" alt="雨骑士商家版" class="w-full max-w-lg mx-auto rounded-lg">
                            <div class="mt-4 p-4 bg-white rounded-lg shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <div class="text-xs text-gray-500">商家管理系统</div>
                                </div>
                                <div class="h-2 bg-gray-200 rounded-full w-full mb-2"></div>
                                <div class="h-2 bg-gray-200 rounded-full w-3/4 mb-2"></div>
                                <div class="h-2 bg-gray-200 rounded-full w-1/2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section class="py-20 bg-gray-50" id="contact">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4">联系我们</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">如有任何问题或合作意向，请随时与我们联系</p>
            </div>
            
            <div class="flex flex-col md:flex-row">
                <div class="md:w-1/3 mb-10 md:mb-0">
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-6">联系方式</h3>
                        <ul class="space-y-4">
                            <li class="flex items-center">
                                <div class="bg-primary bg-opacity-10 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">电话</p>
                                    <p class="text-gray-700">暂无</p>
                                </div>
                            </li>
                            <li class="flex items-center">
                                <div class="bg-primary bg-opacity-10 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">QQ</p>
                                    <p class="text-gray-700">暂无</p>
                                </div>
                            </li>
                            <li class="flex items-center">
                                <div class="bg-primary bg-opacity-10 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">微信</p>
                                    <p class="text-gray-700">暂无</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="md:w-2/3 md:pl-8">
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-6">留言咨询</h3>
                        <form class="jsSubmitForm">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">您的姓名</label>
                                    <input type="text" id="name" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label for="contact" class="block text-sm font-medium text-gray-700 mb-2">您的联系方式</label>
                                    <input type="text" id="contact" name="contact" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>
                            <div class="mb-6">
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">您想咨询的问题</label>
                                <textarea id="message" name="message" rows="4" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
                            </div>
                            <div class="alert alert-success hidden bg-green-100 text-green-700 p-4 rounded-md mb-4"></div>
                            <div class="alert alert-danger hidden bg-red-100 text-red-700 p-4 rounded-md mb-4"></div>
                            <button type="submit" class="bg-primary text-white px-6 py-3 rounded-md hover:bg-opacity-90 transition flex items-center justify-center">
                                <div class="loading-cirlce hidden mr-2" data-status="0">
                                    <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </div>
                                提交
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <a href="#" class="flex items-center">
                        <img src="/images/homepage/yqs/logo2.png" alt="雨骑士" class="h-10 invert">
                        <span class="ml-2 text-xl font-bold">雨骑士</span>
                    </a>
                    <p class="mt-2 text-gray-400">同城即时配送服务平台</p>
                </div>
                <div class="text-center md:text-right">
                    <p class="text-gray-400">© 2014-2023 宁波市万戬生活服务有限公司 版权所有</p>
                    <p class="mt-2">
                        <a href="https://beian.miit.gov.cn/" target="_blank" class="text-gray-400 hover:text-white transition">浙ICP备2022032094号</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // 表单提交
        (function() {
            const forms = document.querySelectorAll('.jsSubmitForm');
            
            forms.forEach(form => {
                const successAlert = form.querySelector('.alert-success');
                const dangerAlert = form.querySelector('.alert-danger');
                const btnLoading = form.querySelector('.loading-cirlce');
                
                successAlert.classList.add('hidden');
                dangerAlert.classList.add('hidden');
                
                btnLoading.setAttribute('data-status', '0');
                
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    successAlert.classList.add('hidden');
                    dangerAlert.classList.add('hidden');
                    
                    const formData = new FormData(form);
                    const data = {};
                    
                    for (let [key, value] of formData.entries()) {
                        data[key] = value;
                    }
                    
                    if (btnLoading.getAttribute('data-status') === '1') {
                        return false;
                    }
                    
                    btnLoading.classList.remove('hidden');
                    btnLoading.setAttribute('data-status', '1');
                    
                    fetch('/api/message/submit', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify(data)
                    })
                    .then(response => response.json())
                    .then(res => {
                        btnLoading.classList.add('hidden');
                        btnLoading.setAttribute('data-status', '0');
                        
                        if (res.success) {
                            successAlert.classList.remove('hidden');
                            successAlert.textContent = '提交成功';
                            form.reset();
                        } else {
                            dangerAlert.classList.remove('hidden');
                            dangerAlert.textContent = res.msg || '提交失败';
                        }
                    })
                    .catch(err => {
                        btnLoading.classList.add('hidden');
                        btnLoading.setAttribute('data-status', '0');
                        
                        dangerAlert.classList.remove('hidden');
                        dangerAlert.textContent = '提交失败，请稍后再试';
                    });
                    
                    return false;
                });
            });
        })();
    </script>
</body>
</html> 