
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>图标点标记</title>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.css">
    <style>
        /* 更改 body 的背景颜色 */
        body {
            background-color: #f1f1f1;
        }

        /* 美化按钮样式 */
        .button {
            background-color: #4286f4;
            color: #fff;
            border-radius: 5px;
            border: none;
            padding: 10px 20px;
            font-size: 18px;
        }

        /* 让页面元素水平居中 */
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 添加边框和背景色 */
        .panel {
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 10px #ddd;
        }

        /* 让文本居中对齐 */
        h1 {
            text-align: center;
        }

        /* 调整文本大小 */
        p {
            font-size: 16px;
        }
        .amap-icon img {
            width: 25px;
            height: 34px;
        }

        .order-main {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        .order-list {
            width: 12%;
            height: 100%;
            overflow: hidden auto;
        }

        .rider-list {
            width: 10%;
            height: 100%;
            overflow: hidden auto;
        }

        .site-info {
            background-color: yellow;
        }

        .rider-item {
            list-style: none;
            border-bottom: 1px solid grey;
            padding: 20px 10px 20px 10px;
            cursor: pointer;
        }

        .rider-item:hover {
            background-color: #fdf0c1;

        }

        .order-item {
            list-style: none;
            border-bottom: 1px solid grey;
            padding: 20px 10px 20px 10px;
        }

        .selected {
            background-color: #fdf0c1;
        }

        .rider-info {
            display: flex;
        }

        .jconfirm .jconfirm-row {
            display: flex;
            justify-content: center;
            align-items: center
        }
        .jalert .jconfirm-row {
            display: flex;
            justify-content: center;
            align-items: center
        }
        /* 骑手头像容器 */
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden; /* 让超出容器的部分隐藏 */
        }

        /* 骑手头像图片 */
        .avatar img {
            width: 100%; /* 让图片宽度充满整个容器 */
            height: auto; /* 让高度自适应 */
            object-fit: cover; /* 让图片按照比例填充容器 */
            object-position: center; /* 让图片居中对齐 */
        }
    </style>
</head>
<body>
<div id="app" class="order-main">
    <div class="order-list">
        <div class="site-info">
            <div style="padding: 10px;text-align: center;">
                <h3 id="site_name">{{ $site->name }}</h3>
            </div>
        </div>
    </div>
    <div id="container" style="flex: 1">

    </div>
    <div class="rider-list">
        <ul style="padding: 0 0 0 0">
            @foreach($riders as $rider)
                <li class="rider-item" id="{{ $rider['id'] }}">
                    <div class="rider-info">
                        <div class="avatar">
                            <img src="{{ $rider['avatar'] }}" alt="" width="40px">
                        </div>
                        <div>
                            <div>{{ $rider['name'] }}</div>
                            <div>{{ $rider['order_count'] }}</div>
                        </div>
                        <div style="display: none" class="rider-detail">
                            {{ json_encode($rider) }}
                        </div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>
</div>

<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=6789e1455814df5fcaf4a6dd533aa2d5"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.js"></script>
<script type="text/javascript">
    // 创建地图实例
    $(function () {
        init();
    });

    function init() {
        getSiteInfo();

    }

    function map_init(lon, lat) {
        window.map = new AMap.Map("container", {
            zoom: 13,
            center: [lon, lat],
            resizeEnable: true
        });

        $.find('.rider-item').forEach(function (item) {
            let v = $(item).find('.rider-detail')[0].innerHTML
            let data = JSON.parse(v);
            if(data['location'] === null) {
                return;
            }
            let marker = new AMap.Marker({
                icon: data['avatar'],
                position: [data['location']['lng'], data['location']['lat']],
                anchor: 'bottom-center',
            });
            // 添加地图文字说明
            marker.setLabel({
                offset: new AMap.Pixel(0, 0),  //设置文本标注偏移量
                content: data['name']
            });
            window.map.add(marker);
        })

    }

    function getSiteInfo() {
        const siteId = GetQueryString('site_id');
        $.ajax({
            type: "get",
            url: "api/v1/sites/" + siteId,
            datatype: "json",
            success: function (data) {
                if (data.code === 0) {
                    // 成功
                    $("#site_name").text(data.data.name)
                    map_init(data.data.site.longitude, data.data.site.latitude, data.data.regions);
                }
            },
            error: function (err) {
                console.log(err)
            }
        })
    }


    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
</body>
</html>
