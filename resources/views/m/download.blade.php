<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta
        name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover"
    />
    <title>下载</title>

    <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css"/>
    <link rel="stylesheet" href="{{ asset('/css/reset.css') }}"/>

    <style>
        body {
            --primary-color: #ff9345;

            background-color: #feeedf;
        }

        [v-cloak] {
            visibility: hidden;
        }

        .container {
            width: 5.34rem;
            margin: 0.8rem auto;
        }

        .btn-submit {
            display: block;
            width: 100%;
            height: 1.06rem;
            border-radius: 0.6rem;
            background: var(--primary-color);
            border: 0;
            font-size: 0.36rem;
            color: white;
            font-weight: 500;
            margin-top: 1rem;
        }

        .input-group {
            position: relative;
            margin-bottom: 0.24rem;
        }

        .input-group input {
            display: block;
            width: 100%;
            height: 0.76rem;
            line-height: 0.76rem;
            border: 2px solid #e0e0e0;
            border-radius: 0.16rem;
            padding: 0 0.28rem;
            font-size: 0.28rem;
            font-weight: 500;
        }

        .btn-sms {
            width: 1.96rem;
            height: 0.76rem;
            position: absolute;
            right: 0;
            top: 0;
            background-color: var(--primary-color);
            border-radius: 0 0.16rem 0.16rem 0;
            font-size: 0.28rem;
            color: white;
            font-weight: 500;
            border: 0;
        }
    </style>
</head>
<body>
<main id="app" v-cloak>
    <img src="https://img.dac6.cn/users/0/0/326ac0bd288c75077b0238664176beb4.jpg" alt=""/>
    <section class="container">
        <div class="form">
            <div class="input-group">
                <input v-model="form.phone" type="tel" placeholder="请输入需要绑定的手机号"/>
            </div>
            <div class="input-group">
                <input v-model="form.sms" type="number" placeholder="输入验证码"/>
                <button class="btn-sms" @click="onSendSms">@{{ smsBtnText }}</button>
            </div>
        </div>
        <button class="btn-submit" @click="onSubmit">点击下载</button>
    </section>
</main>

<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<script src="https://fastly.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
<script src="https://cdn.bootcss.com/qs/6.7.0/qs.min.js"></script>
<script src="https://unpkg.com/axios@1.1.2/dist/axios.min.js"></script>
<script>
    var qs = window.Qs
    var {createApp} = Vue
    var SMS_COUNT = 10
    var smsTimer

    var http = axios.create({
        // baseURL: 'https://some-domain.com/api/',
        timeout: 60000,
        validateStatus: function (status) {
            return status < 600
        }
    })

    createApp({
        data() {
            return {
                form: {
                    phone: '',
                    sms: '',
                    smsKey: '',
                    invite_code: qs.parse(location.search.substring(1)).code || ''
                },
                sms_fetching: false,
                sms_counting: false,
                sms_countdown: SMS_COUNT
            }
        },
        computed: {
            smsBtnText: function () {
                return this.sms_counting ? `${this.sms_countdown}s重发` : '发送验证码'
            }
        },
        methods: {
            onSubmit: function () {
                console.log('submit:', this.form)
                if (!this.form.phone || !this.form.sms) {
                    return vant.showToast('请填写手机号、验证码')
                }
                vant.showLoadingToast({message: '正在提交...', duration: 0})
                let params = {verification_key: this.form.smsKey, verification_code: this.form.sms + ''}
                if (this.form.invite_code) {
                    params.invite_code = this.form.invite_code
                }
                http
                    .post('{{ route('api.v1.users.store') }}', params)
                    .then(function (response) {
                        const {data} = response
                        if (data.code != 0) {
                            if (data.message !== '您已注册，直接去登录吧～') {
                                throw new Error(data.message)
                            }
                        }
                        let downloadUrl = '{{ $download_url }}'
                        // 下载
                        if (downloadUrl) {
                            location.href = downloadUrl
                        } else {
                            vant.showToast('IOS版本搭建中，敬请期待...');
                        }
                        vant.closeToast()
                    })
                    .catch(function (e) {
                        vant.showToast(e.message)
                    })
            },
            onSendSms: function () {
                var _this = this
                if (this.sms_counting || this.sms_fetching) return
                if (!this.form.phone) {
                    return vant.showToast('请填写手机号')
                }
                this.sms_fetching = true
                vant.showLoadingToast({message: '正在发送...', duration: 0})
                http
                    .post('{{ route('api.v1.verificationCodes.store') }}', {phone: this.form.phone})
                    .then(function (response) {
                        const {config, data, status} = response
                        if (data.code != 0) throw new Error(data.message)
                        _this.form.smsKey = data.data.key
                        console.log(_this.form.smsKey,data.data.key)
                        vant.closeToast()
                        vant.showSuccessToast('已发送')
                        _this.sms_counting = true
                        smsTimer = setInterval(_this.smsCountDownFn, 1000)
                    })
                    .catch(function (e) {
                        vant.closeToast()
                        vant.showToast(e.message)
                    })
                    .finally(function () {
                        _this.sms_fetching = false
                    })
            },
            smsCountDownFn: function () {
                var n = this.sms_countdown - 1
                if (n <= 0) {
                    clearInterval(smsTimer)
                    this.sms_counting = false
                    return
                }
                this.sms_countdown = n
            }
        }
    }).mount('#app')
</script>
<script charset="UTF-8" src="//unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
</body>
</html>
