<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width,height=device-height,initial-scale=1,user-scalable=0,minimum-scale=1,maximum-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="apple-touch-fullscreen" content="yes"/>
    <meta name="format-detection" content="telephone=no, email=no"/>
    <meta http-equiv="Cache-control" content="max-age=0"/>
    <meta name="theme-color" content="#333">
    <link rel="shortcut icon" href="/favicon.ico">
    <title>{{ $notify->title }}</title>
    <style>
        img{
            max-width: 100%;
        }
        .article-title {
            font-family: PingFangSC-Medium;
            font-size: 24px;
            line-height: 33px;
            color: #222;
            text-align: left;
            word-break: break-all
        }
        .article-time {
            margin: 13px 0 16px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #666;
        }
        p {
            margin: 0;
            padding: 5px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
<div id="root">
    <div class="page">
        <div class="page-article">
            <div class="article-title">{{ $notify->title }}</div>
            <div class="article-time">发布时间: {{ $notify->created_at }}</div>
            <div class="article-info">
                {!! $notify->content !!}
            </div>
        </div>
    </div>
</div>
<script>
(function(){

    var myVideo = document.getElementById("myVideo")
    if(myVideo != null){
        myVideo.disablePictureInPicture = true
    }

})();
</script>
</body>
</html>
