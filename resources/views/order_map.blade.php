
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>图标点标记</title>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.css">
    <style>
        .amap-icon img {
            width: 25px;
            height: 34px;
        }

        .order-main {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        .order-list {
            width: 12%;
            height: 100%;
            overflow: hidden auto;
        }

        .rider-list {
            width: 10%;
            height: 100%;
            overflow: hidden auto;
        }

        .site-info {
            background-color: yellow;
        }

        .rider-item {
            list-style: none;
            border-bottom: 1px solid grey;
            padding: 20px 10px 20px 10px;
            cursor: pointer;
        }

        .rider-item:hover {
            background-color: #fdf0c1;

        }

        .order-item {
            list-style: none;
            border-bottom: 1px solid grey;
            padding: 20px 10px 20px 10px;
        }

        .selected {
            background-color: #fdf0c1;
        }

        .rider-info {
            display: flex;
        }

        .jconfirm .jconfirm-row {
            display: flex;
            justify-content: center;
            align-items: center
        }
        .jalert .jconfirm-row {
            display: flex;
            justify-content: center;
            align-items: center
        }
    </style>
</head>
<body>
<div id="app" class="order-main">
    <div class="order-list">
        <div class="site-info">
            <div style="padding: 10px;text-align: center;">
                <h3 id="site_name">{{ $site->name }}</h3>
            </div>
        </div>
        <div>
            @if(count($orders) > 0)
                <ul style="padding: 0 0 0 0">
                    @foreach($orders as $order)
                        <li class="order-item {{ $order['order_no'] == request()->get('order_no') ? 'selected' : '' }}">
                            <div>
                                <div>{{ $order['order_no'] }}</div>
                                <div>{{ $order['order_status_text'] }}/ {{ $order['dispatch_status_text'] }}</div>
                                <a href="{{ route('admin.order.map',['site_id' => request()->get('site_id'), 'order_no' => $order['order_no']]) }}" style="text-decoration: none">
                                    <div style="display: flex; justify-content: space-between">
                                        <div><span>{{ $order['type_text'] }}</span></div>
                                        <div>
                                            <span style="color: red">{{ $order['order_price_pro'] }} 元</span>
                                        </div>
                                        <div><span>{{ $order['time_text'] }}</span></div>
                                    </div>
                                    <div>
                                        <img src="https://img.dingdongjuhe.com/resource/start.png" width="20px" alt="">
                                        {{ $order['pickup_address'] }}
                                    </div>
                                    <div>
                                        <img src="https://img.dingdongjuhe.com/resource/end.png" width="20px" alt="">
                                        {{ $order['deliver_address'] }}
                                    </div>
                                </a>
                            </div>
                        </li>
                    @endforeach
                </ul>
            @endif
        </div>
    </div>
    <div id="container" style="flex: 1">

    </div>
    <div class="rider-list">
        <ul style="padding: 0 0 0 0">
            @foreach($riders as $rider)
                <li class="rider-item" id="{{ $rider['id'] }}">
                    <div class="rider-info">
                        <div>
                            <img src="{{ $rider['avatar'] }}" alt="" width="40px">
                        </div>
                        <div>
                            <div>{{ $rider['name'] }}</div>
                            <div>当前订单: {{ $rider['order_count'] }}单</div>
                            <div>{{ $rider['direction'] }}</div>
                            <div>{{ $rider['distance'] }} KM</div>
                        </div>
                        <div style="display: none" class="rider-detail">
                            {{ json_encode($rider) }}
                        </div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>
</div>
<div style="display: none" class="order-detail">
    {{ json_encode($firstOrder) }}
</div>

<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=483b4c91cdde5e5a90d58429407dfb51"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.js"></script>
<script type="text/javascript">
    // 创建地图实例
    $(function () {
        init();
    });

    function init() {
        getSiteInfo();

        $(document).on('click', '.rider-item', function () {
            let v = $(this).find('.rider-detail')[0].innerHTML
            let data = JSON.parse(v);
            let ov = $('.order-detail')[0].innerHTML;
            let odata = JSON.parse(ov);
            var marker = new AMap.Marker({
                icon: data['avatar'],
                position: [data['lgn'], data['lat']],
                anchor: 'bottom-center'
            });
            window.map.add(marker);
            $.confirm({
                title: '确认',
                content: '请确认是否派单给跑男' + data['name'],
                type: 'green',
                icon: 'glyphicon glyphicon-question-sign',
                buttons: {
                    ok: {
                        text: '确认',
                        btnClass: 'btn-primary',
                        action: function() {
                            $.ajax({
                                type: "post",
                                url: "api/admin/v1/dispatch_order/",
                                datatype: "json",
                                data: {order_no: odata.order_no, rider_id: data['id']},
                                success: function (data) {
                                    if (data.code === 0) {
                                        alert("分配成功，待骑手确认");
                                    }
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    alert(JSON.parse(jqXHR.responseText).message);
                                }
                            })
                        }
                    },
                    cancel: {
                        text: '取消',
                        btnClass: 'btn-primary'
                    }
                }
            });
        });
    }

    function getStartIcon() {
        // 创建一个 Icon
        return new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(25, 34),
            // 图标的取图地址
            image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
            // 图标所用图片大小
            imageSize: new AMap.Size(135, 40),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(-9, -3)
        })
    }

    function getEndIcon() {
        // 创建一个 icon
        return new AMap.Icon({
            size: new AMap.Size(25, 34),
            image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
            imageSize: new AMap.Size(135, 40),
            imageOffset: new AMap.Pixel(-95, -3)
        });
    }

    function map_init(lon, lat) {
        window.map = new AMap.Map("container", {
            zoom: 13,
            center: [lon, lat],
            resizeEnable: true
        });
        let v = $('.order-detail')[0].innerHTML;
        let data = JSON.parse(v);
        // 将 icon 传入 marker
        if (data) {
            if (data['pickup_lng']) {
                var startMarker = new AMap.Marker({
                    position: new AMap.LngLat(data['pickup_lng'], data['pickup_lat']),
                    icon: getStartIcon(),
                    offset: new AMap.Pixel(-13, -30)
                });
                window.map.add(startMarker);
            }
            // 将 icon 传入 marker
            var endMarker = new AMap.Marker({
                position: new AMap.LngLat(data['deliver_lng'], data['deliver_lat']),
                icon: getEndIcon(),
                offset: new AMap.Pixel(-13, -30)
            });

            window.map.add(endMarker);
        }
    }

    function getSiteInfo() {
        const siteId = GetQueryString('site_id');
        $.ajax({
            type: "get",
            url: "api/v1/sites/" + siteId,
            datatype: "json",
            success: function (data) {
                if (data.code === 0) {
                    // 成功
                    $("#site_name").text(data.data.name)
                    map_init(data.data.site.longitude, data.data.site.latitude, data.data.regions);
                }
            },
            error: function (err) {
                console.log(err)
            }
        })
    }


    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
</body>
</html>
