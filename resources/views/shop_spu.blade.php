<table>
    <thead>
    <tr>
        <th style="width: 100px;">ID(新商品不填，老商品不变)</th>
        <th style="width: 125px;">名称</th>
        <th style="width: 150px;">图片</th>
        <th style="width: 75px;">店铺ID</th>
        <th style="width: 125px;">店铺名称</th>
        <th style="width: 200px;">描述</th>
        <th style="width: 80px;">原价/元</th>
        <th style="width: 80px;">优惠价/元</th>
        <th style="width: 200px;">售卖标签(多个以"｜"分隔)</th>
        <th style="width: 80px;">库存</th>
        <th style="width: 80px;">销量</th>
        <th style="width: 50px;">是否招牌(取值：是、否)</th>
        <th style="width: 50px;">状态(取值：待上架、上架、下架)</th>
        <th style="width: 50px;">排序</th>
    </tr>
    </thead>
    <tbody>
    @foreach($spus as $spu)
        <tr>
            <td style="height: 100px;">{{ $spu->id }}</td>
            <td style="height: 100px;">{{ $spu->name }}</td>
            <td style="height: 100px;"></td>
            <td style="height: 100px;">{{ $spu->shop_id }}</td>
            <td style="height: 100px;">{{ $spu->shop->name }}</td>
            <td style="height: 100px;">{{ $spu->description }}</td>
            <td style="height: 100px;">{{ $spu->price }}</td>
            <td style="height: 100px;">{{ $spu->discount_price }}</td>
            <td style="height: 100px;">{{ implode("|", $spu->sell_tags ?: []) }}</td>
            <td style="height: 100px;">{{ $spu->stock }}</td>
            <td style="height: 100px;">{{ $spu->sales }}</td>
            <td style="height: 100px;">{{ $spu->is_signboard ? "是" : "否" }}</td>
            <td style="height: 100px;">{{ \App\Models\Common::StatusMap[$spu->status] }}</td>
            <td style="height: 100px;">{{ $spu->sort }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
