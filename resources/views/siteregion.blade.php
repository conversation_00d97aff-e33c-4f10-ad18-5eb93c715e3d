<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <style>
        html,
        body,
        #container {
            width: 100%;
            height: 100%;
        }
    </style>
    <title>站点配送范围划分</title>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.10.4/themes/smoothness/jquery-ui.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jqueryui/1.13.2/jquery-ui.js"></script>
    <link rel="stylesheet" href="http://jqueryui.com/resources/demos/style.css">
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <script
        src="https://webapi.amap.com/maps?v=2.0&key=aff5ca42c8169d4261a8bec7f69424d8&plugin=AMap.PolygonEditor"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <style type="text/css">
        .region-card {
            display: flex;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            background-clip: border-box;
            border-radius: 0.25rem;
            width: 25rem;
            border-width: 0;
            box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
            position: fixed;
            top: 1rem;
            left: 1rem;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            padding: 0.75rem 1.25rem;
        }

        .action-btn {
            display: contents;
            /*position: fixed;*/
            /*bottom: 5rem;*/
            /*left: 6rem;*/
            /*background-color: #fff;*/
            /*background-clip: border-box;*/
            /*border-radius: 0.25rem;*/
        }
        .action-btn button {
            margin: 10px 0 !important;
        }


        .region-element {
            background-color: #fff;
            background-clip: border-box;
            border-radius: 0.25rem;
            padding: 2px;
            word-wrap: break-word;
            border-width: 0;
            box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
            margin: 3px;
            text-align: center;
            font-weight: bold;
        }

        .site-info {
            color: white;
            font-weight: bold;
        }

        .delete-area{
            display: inline-table;
            float: right;
            padding: 0 5px;
            color: #ff0000;
        }
    </style>
</head>
<body>
<div id="container"></div>
<div class="input-card" style="width: 120px">
    <button class="btn" onclick="createPolygon()" style="margin-bottom: 5px">新建</button>
    <button class="btn" onclick="polyEditor.open()" style="margin-bottom: 5px">开始编辑</button>
    <button class="btn" onclick="polyEditor.close()">结束编辑</button>
</div>
<div class="region-card" style="width: 130px; background-color: #ea9942; height: 100%" id="region_card">
    <div class="site-info"><h3 id="site_name">未获取站点信息</h3></div>
    <div id="region-element" style="max-height: 88vh;overflow: auto;"></div>
{{--    <div id="region-element" style="max-height: 40vh;overflow: auto;"></div>--}}
{{--    <div class="site-info"><h3>其他区域</h3></div>--}}
{{--    <div id="region-element-other" style="max-height: 40vh;overflow: auto;"></div>--}}
    <div class="action-btn">
        <button onclick="save()">保存设置</button>
    </div>
</div>
<div id="dialog" title="请输入区域名称" style="display: none;">
    <input type="text" name="region_name" id="regionName" placeholder="请输入区域名称">
</div>
<script type="text/javascript">
    let polygonMap = {};
    let map = null
    let siteId = 0

    $(function () {
        init();
    });

    function init() {
        getSiteInfo();
    }

    $(document).on('click', '.delete-area', function (){
        _amap_id = $(this).attr("data-id");
        polygon = polygonMap[_amap_id]
        map.remove([polygon])
        delete polygonMap[_amap_id]
        $(this).parent().remove();
    });

    $(document).on('dblclick', '.region-element', function (){
        _amap_id = $(this).attr("data-id");
        polygon = polygonMap[_amap_id]
        polyEditor.addAdsorbPolygons(polygon);
        polyEditor.setTarget(polygon);
        polyEditor.open();
    });

    function map_init(site, regions) {
        // 成功
        siteId = site.id
        $("#site_name").text(site.name)
        if (map == null) {
            let zoom = GetQueryString('zoom');
            let lon = site.longitude
            let lat = site.latitude
            if (lon == null) {
                lon = 121.154572
            }
            if (lat == null) {
                lat = 30.037967
            }
            if (zoom == null) {
                zoom = 14;
            }
            map = new AMap.Map("container", {
                center: [lon, lat],
                zoom: zoom
            });
        }
        for (let region of regions) {
            if (region.site_id == site.id) {
                polygon = new AMap.Polygon({path: region.points})
            }else{
                polygon = new AMap.Polygon({path: region.points, fillColor: "#aaa", strokeColor: "#aaa"})
            }
            polygon.siteId = region.site_id
            polygon.regionName = region.name
            map.add([polygon]);
            polygonMap[polygon._amap_id] = polygon
            if (region.site_id == site.id) {
                $("#region-element").append('<div class="region-element" data-id="' + polygon._amap_id + '">' + region.name + '<div class="delete-area" data-id="' + polygon._amap_id + '">x</div></div>')
            // }else{
            //     $("#region-element-other").append('<div class="region-element" data-id="' + polygon._amap_id + '">' + region.name + '</div>')
            }
        }

        // map.setFitView();
        polyEditor = new AMap.PolygonEditor(map);
        polyEditor.on('add', function (data) {
            var polygon = data.target;
            polyEditor.addAdsorbPolygons(polygon);
            polygon.on('dblclick', () => {
                polyEditor.setTarget(polygon);
                polyEditor.open();
            })
        })
        polyEditor.on('end', function (data) {
            if (data.target) {
                var polygon = data.target;
                if (!polygon.hasOwnProperty("regionName")) {
                    $("#dialog").dialog({
                        resizable: false,
                        height: 140,
                        modal: true,
                        buttons: {
                            "确认": function () {
                                polygon.siteId = siteId
                                polygon.regionName = $("#regionName").val()
                                $("#region-element").append('<div class="region-element" data-id="' + polygon._amap_id + '">' + polygon.regionName + '<div class="delete-area" data-id="' + polygon._amap_id + '">x</div></div>')
                                $(this).dialog("close");
                            },
                            "取消": function () {
                                $(this).dialog("close");
                            }
                        }
                    });
                }
                polygonMap[polygon._amap_id] = polygon
            }
        });

        polyEditor.open();
    }

    function createPolygon() {
        polyEditor.close();
        polyEditor.setTarget();
        polyEditor.open();
    }

    function getSiteInfo() {
        const siteId = GetQueryString('site_id');
        $.ajax({
            type: "get",
            url: "api/v1/sites/" + siteId,
            datatype: "json",
            success: function (data) {
                console.log(data)
                if (data.code === 0) {
                    map_init(data.data.site, data.data.regions);
                }else{
                    alert(data.message)
                }
            },
            error: function (error) {
                alert(error.responseJSON.message ?? error.statusText)
            }
        })
    }

    function save() {
        let polygonList = [];
        for (let key in polygonMap) {
            if (polygonMap.hasOwnProperty(key)) {
                let polygon = polygonMap[key];
                if (polygon.siteId != siteId) continue
                let path = polygon.getPath()
                let latlonlist = [];
                for (let item of path) {
                    let latlon = {
                        lng: item.lng,
                        lat: item.lat
                    }
                    latlonlist.push(latlon)
                }
                let obj = {
                    name: polygon.regionName,
                    pointList: latlonlist
                }
                polygonList.push(obj)
            }
        }
        $.ajax({
            type: 'post',
            url: 'api/v1/save_site_region',
            datatype: "json",
            data: {'polygons': polygonList, 'site_id': siteId},
            success: function (data) {
                console.log(data)
                if (data.code === 0) {
                    alert("保存成功")
                    // window.location.reload();
                }else{
                    alert(data.message)
                }
            },
            error: function (error) {
                alert(error.responseJSON.message ?? error.statusText)
            }
        })
    }

    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
</body>
</html>
