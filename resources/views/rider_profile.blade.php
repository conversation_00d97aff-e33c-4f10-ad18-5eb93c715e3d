<html>
<head>
    <style>
        .profile {
            display: flex;
            align-items: flex-start;
        }

        .head-img {
            width: 100px;
            height: 100px;
            background: #CECECE;
            opacity: 1;
            border: 2px solid #CECECE;
        }

        .profile-info {
            margin-left: 10px;
        }

        .base-info {
            display: flex;
            margin-left: 60px;
        }
    </style>
</head>
</html>
<div class="content">
    <div class="profile">
        <div class="base-info">
            <img src="{{ $rider->avatar }}" class="head-img">
            <div>
                <div class="profile-info" style="font-size: 24px;">{{ $rider->name }}</div>
                <div class="profile-info">
                    <span>联系方式:</span>
                    {{ $rider->phone }}
                </div>
                <div class="profile-info">
                    <span>交通工具:</span>
                    {{ $rider->user->riderProfile->transport_type_text }}
                </div>
                <div class="profile-info">
                    @if($rider->status == 0)
                        <span class="label" style="background:red">离线</span>
                    @elseif($rider->status == 1)
                        <span class="label" style="background:#58b57d">在线</span>
                    @else
                        <span class="label" style="background:#586cb1">小休</span>
                    @endif

                </div>
            </div>
        </div>

        <div class="base-info">
            <div class="profile-info"><span>订单数:</span>60单</div>
            <div class="profile-info"><span>今日收益:</span>60单</div>
        </div>

    </div>
</div>

