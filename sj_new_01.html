<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骑手APP - 多路径规划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#ff6b01',
                            dark: '#e05a00',
                            light: '#ff8c3f',
                            50: '#fff8f1',
                            100: '#ffe9d7',
                            200: '#ffd2ae',
                            300: '#ffaf7a',
                            400: '#ff8c3f',
                            500: '#ff6b01',
                            600: '#e85a00',
                            700: '#c24a00',
                            800: '#9c3c03',
                            900: '#7e3206'
                        }
                    },
                    boxShadow: {
                        'soft': '0 2px 10px rgba(0,0,0,0.05)',
                        'card': '0 4px 12px rgba(0,0,0,0.08)',
                        'float': '0 8px 20px rgba(0,0,0,0.12)'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @tailwind base;
        @tailwind components;
        @tailwind utilities;
        
        body {
            @apply bg-gray-100 font-sans text-gray-800;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .page-container {
            @apply w-full mx-auto;
            max-width: 480px;
            min-height: 100vh;
        }
        
        .page {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow-y: auto;
        }
        
        .status-bar {
            height: 24px;
        }
        
        .nav-bar {
            height: 48px;
        }
        
        .content-container {
            height: calc(100vh - 72px);
            overflow-y: auto;
        }

        .map-container {
            height: 100vh;
            width: 100%;
        }
        
        .bottom-sheet {
            @apply fixed bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-2xl;
            max-height: 50vh;
            overflow-y: auto;
        }
        
        .task-card {
            @apply bg-white rounded-xl p-4 shadow-sm transition-all;
        }
        
        .task-card:hover, .task-card:focus {
            @apply shadow-md;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            @apply bg-primary-500 text-white font-medium py-2 px-4 rounded-lg hover:bg-primary-600 active:bg-primary-700 transition-colors;
        }
        
        .btn-outline {
            @apply border border-primary-500 text-primary-600 font-medium py-2 px-4 rounded-lg hover:bg-primary-50 active:bg-primary-100 transition-colors;
        }
        
        .btn-secondary {
            @apply bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg hover:bg-gray-200 active:bg-gray-300 transition-colors;
        }

        .task-tag {
            @apply text-xs font-medium rounded-full px-2 py-0.5;
        }
        
        .tag-pickup {
            @apply bg-blue-100 text-blue-800;
        }
        
        .tag-delivery {
            @apply bg-green-100 text-green-800;
        }
        
        .tag-overdue {
            @apply bg-red-100 text-red-800;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-8 text-gray-800">骑手APP多路径规划功能预览</h1>
        
        <!-- 页面1: 路径概览页 -->
        <div id="page1" class="page bg-gray-50">
            <!-- 顶部状态栏 -->
            <div class="status-bar bg-white px-4 pt-2 flex justify-between items-center text-xs text-gray-500">
                <span class="time">14:25</span>
                <div class="icons flex space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="nav-bar bg-white px-4 pb-2 flex justify-between items-center shadow-sm">
                <div class="flex items-center">
                    <span class="text-base font-semibold">您好，李师傅</span>
                    <span class="ml-2 bg-primary-100 text-primary-800 text-xs px-2 py-0.5 rounded-full">送餐英雄</span>
                </div>
                <div class="flex space-x-3">
                    <button class="text-gray-600 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <button class="text-gray-600 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                        <i class="fas fa-user text-lg"></i>
                    </button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-container">
                <!-- 任务导航标签 -->
                <div class="bg-white px-4 pt-3 pb-0 mb-3">
                    <div class="flex space-x-2 border-b border-gray-200">
                        <button class="text-primary-600 border-b-2 border-primary-500 pb-2 px-2 text-sm font-medium">
                            新任务 (5)
                        </button>
                        <button class="text-gray-500 pb-2 px-2 text-sm font-medium">
                            进行中 (2)
                        </button>
                    </div>
                </div>
                
                <!-- 多路径优化提示 -->
                <div class="p-4 bg-gradient-to-r from-primary-50 to-white border-b border-gray-100">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-primary-500/10 flex items-center justify-center mr-3">
                                <i class="fas fa-route text-primary-500"></i>
                            </div>
                            <div>
                                <span class="font-semibold text-gray-800 block">智能路径规划</span>
                                <p class="text-xs text-gray-500">优化配送路线可节省约 <span class="text-primary-600 font-medium">35 分钟</span></p>
                            </div>
                        </div>
                        <button class="py-1.5 px-4 bg-primary-500 text-white text-xs font-medium rounded-full shadow-sm hover:bg-primary-600 transition">
                            立即优化
                        </button>
                    </div>
                </div>
                
                <!-- 任务1 - 超时订单 -->
                <div class="task-card bg-white border-b border-gray-100 hover:bg-gray-50/50 transition">
                    <div class="px-4 py-2 bg-primary-50 flex items-center border-l-4 border-primary-500">
                        <i class="fas fa-exclamation-circle text-primary-500 mr-2"></i>
                        <span class="text-primary-600 font-medium text-sm">已超时 20 分钟</span>
                        <span class="ml-2 text-gray-500 text-sm">送达</span>
                    </div>
                    
                    <div class="p-4 flex">
                        <div class="w-9 h-9 rounded-full bg-primary-500 text-white flex items-center justify-center font-bold shadow-sm status-indicator">
                            取
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-gray-800 font-medium">浙江省杭州市临安区滨湖天地</p>
                            <div class="flex justify-end">
                                <span class="text-gray-500 text-sm flex items-center">
                                    <i class="fas fa-location-arrow text-primary-400 mr-1 text-xs"></i>
                                    131m
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 flex items-center">
                        <div class="w-9 flex justify-center">
                            <div class="h-8 w-0.5 bg-gray-200"></div>
                        </div>
                        <div class="ml-3 text-xs text-gray-500">
                            <i class="fas fa-road mr-1"></i>1.32km
                        </div>
                    </div>
                    
                    <div class="p-4 flex items-start">
                        <div class="ml-12 flex-1">
                            <p class="text-gray-700 font-medium">浙江省杭州市临安区滨湖天地购物中心801</p>
                            <div class="flex items-center mt-2 space-x-2">
                                <span class="bg-blue-50 text-blue-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-shopping-bag mr-1 text-xs"></i>
                                    帮我取
                                </span>
                                <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-utensils mr-1 text-xs"></i>
                                    餐饮
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 py-1 flex">
                        <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                            <span class="mr-1">备注:</span>
                            <span>需要餐具，付款码已发</span>
                        </div>
                    </div>
                    
                    <div class="p-4 flex space-x-3">
                        <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            已到取货点
                        </button>
                        
                        <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                            <i class="fas fa-phone text-gray-600"></i>
                        </button>
                    </div>
                    <div class="px-4 pb-3 text-right">
                        <span class="text-gray-500 text-xs">联系取件人</span>
                    </div>
                </div>
                
                <!-- 任务2 - 紧急送药订单 -->
                <div class="task-card bg-white border-b border-gray-100 hover:bg-gray-50/50 transition">
                    <div class="px-4 py-2 bg-red-50 flex items-center border-l-4 border-red-500">
                        <i class="fas fa-heartbeat text-red-500 mr-2"></i>
                        <span class="text-red-600 font-medium text-sm">紧急送药</span>
                        <span class="ml-2 text-gray-500 text-sm">优先配送</span>
                    </div>
                    
                    <div class="p-4 flex">
                        <div class="w-9 h-9 rounded-full bg-primary-500 text-white flex items-center justify-center font-bold shadow-sm">
                            取
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-gray-800 font-medium">浙江省杭州市临安区人民医院药房</p>
                            <div class="flex justify-end">
                                <span class="text-gray-500 text-sm flex items-center">
                                    <i class="fas fa-location-arrow text-primary-400 mr-1 text-xs"></i>
                                    1.5km
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 flex items-center">
                        <div class="w-9 flex justify-center">
                            <div class="h-8 w-0.5 bg-gray-200"></div>
                        </div>
                        <div class="ml-3 text-xs text-gray-500">
                            <i class="fas fa-road mr-1"></i>2.8km
                        </div>
                    </div>
                    
                    <div class="p-4 flex items-start">
                        <div class="ml-12 flex-1">
                            <p class="text-gray-700 font-medium">浙江省杭州市临安区青山湖街道锦绣蓝庭3幢</p>
                            <div class="flex items-center mt-2 space-x-2">
                                <span class="bg-red-50 text-red-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-pills mr-1 text-xs"></i>
                                    紧急药品
                                </span>
                                <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-bolt mr-1 text-xs"></i>
                                    特殊通道
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 py-1 flex">
                        <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                            <span class="mr-1">备注:</span>
                            <span>儿童退烧药，请尽快送达</span>
                        </div>
                    </div>
                    
                    <div class="p-4 flex space-x-3">
                        <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                            <i class="fas fa-location-arrow mr-2"></i>
                            开始导航
                        </button>
                        
                        <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                            <i class="fas fa-phone text-gray-600"></i>
                        </button>
                    </div>
                    <div class="px-4 pb-3 text-right">
                        <span class="text-gray-500 text-xs">联系商家</span>
                    </div>
                </div>
                
                <!-- 任务3 - 普通订单 -->
                <div class="task-card bg-white border-b border-gray-100 hover:bg-gray-50/50 transition">
                    <div class="px-4 py-2 bg-green-50 flex items-center border-l-4 border-green-500">
                        <i class="fas fa-clock text-green-500 mr-2"></i>
                        <span class="text-green-600 font-medium text-sm">预计 15:30 前送达</span>
                        <span class="ml-2 text-gray-500 text-sm">剩余 30 分钟</span>
                    </div>
                    
                    <div class="p-4 flex">
                        <div class="w-9 h-9 rounded-full bg-green-500 text-white flex items-center justify-center font-bold shadow-sm">
                            送
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-gray-800 font-medium">浙江省杭州市余杭区洋东新城昆明路</p>
                            <div class="flex justify-end">
                                <span class="text-gray-500 text-sm flex items-center">
                                    <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                    5.2km
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4 flex items-start">
                        <div class="ml-12 flex-1">
                            <p class="text-gray-700 font-medium">浙江省杭州市余杭区洋东新城时代广场2栋3单元</p>
                            <div class="flex items-center mt-2 space-x-2">
                                <span class="bg-primary-50 text-primary-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-people-carry mr-1 text-xs"></i>
                                    帮我送
                                </span>
                                <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-utensils mr-1 text-xs"></i>
                                    餐饮
                                </span>
                                <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-weight mr-1 text-xs"></i>
                                    小于5kg
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 py-1 flex">
                        <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                            <span class="mr-1">备注:</span>
                            <span>重生王者炒饭 分数等</span>
                        </div>
                    </div>
                    
                    <div class="p-4 flex space-x-3">
                        <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                            <i class="fas fa-location-arrow mr-2"></i>
                            开始导航
                        </button>
                        
                        <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                            <i class="fas fa-phone text-gray-600"></i>
                        </button>
                    </div>
                    <div class="px-4 pb-3 text-right">
                        <span class="text-gray-500 text-xs">联系收件人</span>
                    </div>
                </div>
                
                <!-- 任务4 - 超市订单 -->
                <div class="task-card bg-white border-b border-gray-100 hover:bg-gray-50/50 transition">
                    <div class="px-4 py-2 bg-green-50 flex items-center border-l-4 border-green-500">
                        <i class="fas fa-clock text-green-500 mr-2"></i>
                        <span class="text-green-600 font-medium text-sm">预计 15:45 前送达</span>
                        <span class="ml-2 text-gray-500 text-sm">剩余 45 分钟</span>
                    </div>
                    
                    <div class="p-4 flex">
                        <div class="w-9 h-9 rounded-full bg-primary-500 text-white flex items-center justify-center font-bold shadow-sm">
                            取
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-gray-800 font-medium">浙江省杭州市临安区青山超市城北店</p>
                            <div class="flex justify-end">
                                <span class="text-gray-500 text-sm flex items-center">
                                    <i class="fas fa-location-arrow text-primary-400 mr-1 text-xs"></i>
                                    2.3km
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 flex items-center">
                        <div class="w-9 flex justify-center">
                            <div class="h-8 w-0.5 bg-gray-200"></div>
                        </div>
                        <div class="ml-3 text-xs text-gray-500">
                            <i class="fas fa-road mr-1"></i>3.7km
                        </div>
                    </div>
                    
                    <div class="p-4 flex items-start">
                        <div class="ml-12 flex-1">
                            <p class="text-gray-700 font-medium">浙江省杭州市临安区锦北街道花园小区8号楼</p>
                            <div class="flex items-center mt-2 space-x-2">
                                <span class="bg-blue-50 text-blue-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-shopping-cart mr-1 text-xs"></i>
                                    生鲜超市
                                </span>
                                <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded-full text-xs flex items-center">
                                    <i class="fas fa-snowflake mr-1 text-xs"></i>
                                    冷藏
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 py-1 flex">
                        <div class="ml-12 flex-1 flex items-center text-xs text-gray-500">
                            <span class="mr-1">备注:</span>
                            <span>蔬果生鲜，请小心轻放</span>
                        </div>
                    </div>
                    
                    <div class="p-4 flex space-x-3">
                        <button class="flex-1 py-2.5 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                            <i class="fas fa-location-arrow mr-2"></i>
                            开始导航
                        </button>
                        
                        <button class="w-11 h-11 flex items-center justify-center border border-gray-200 rounded-full hover:bg-gray-50 transition shadow-sm">
                            <i class="fas fa-phone text-gray-600"></i>
                        </button>
                    </div>
                    <div class="px-4 pb-3 text-right">
                        <span class="text-gray-500 text-xs">联系商家</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面2: 路径详情页 -->
        <div id="page2" class="page hidden bg-gray-50">
            <!-- 顶部状态栏 -->
            <div class="status-bar bg-white px-4 pt-2 flex justify-between items-center text-xs text-gray-500">
                <span class="time">14:25</span>
                <div class="icons flex space-x-1">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="nav-bar bg-white px-4 pb-2 flex justify-between items-center shadow-sm">
                <div class="flex items-center">
                    <button class="text-gray-600 mr-2">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <span class="text-base font-semibold">最优配送路线</span>
                </div>
                <button class="text-gray-600">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-container">
                <!-- 地图预览 -->
                <div class="bg-white px-4 py-3 mb-3">
                    <div class="bg-gray-200 rounded-xl h-48 overflow-hidden relative">
                        <img src="https://dummyimage.com/600x400/e0e0e0/ffffff&text=地图预览" class="w-full h-full object-cover" alt="地图预览">
                        <div class="absolute bottom-3 right-3">
                            <button class="bg-white rounded-full w-10 h-10 shadow-md flex items-center justify-center">
                                <i class="fas fa-expand-alt text-gray-700"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 路径优化提示 -->
                <div class="p-4 bg-white border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 text-white flex items-center justify-center shadow-sm">
                            <i class="fas fa-route text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <div class="flex items-center">
                                <h2 class="font-semibold text-gray-800">最优路径规划</h2>
                                <span class="ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-700 rounded-full flex items-center">
                                    <i class="fas fa-clock mr-1 text-xs"></i>
                                    省时35分钟
                                </span>
                            </div>
                            <p class="text-sm text-gray-500 mt-0.5">智能排序5个配送任务，优先紧急订单</p>
                        </div>
                    </div>
                </div>
                
                <!-- 路段详情列表 -->
                <div class="bg-white overflow-y-auto" style="height: 350px;">
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center shadow-sm">
                                <i class="fas fa-walking text-xs"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">当前位置</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-location-dot text-gray-400 mr-1 text-xs"></i>
                                        0.0km
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">杭州市临安区临天路328号</p>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-up text-xs mr-1.5 text-gray-400"></i>
                                直行 450 米，右转
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务1 - 超时任务 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-primary-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                取
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">临安区滨湖天地</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        1.2km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2839</p>
                                    <p class="text-xs text-primary-500 font-medium flex items-center">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        已超时 20 分钟
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-left text-xs mr-1.5 text-gray-400"></i>
                                左转，沿湖滨路行驶 1.5 公里
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务2 - 紧急送药 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-primary-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                取
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">临安区人民医院药房</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        1.5km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2845</p>
                                    <p class="text-xs text-red-500 font-medium flex items-center">
                                        <i class="fas fa-heartbeat mr-1"></i>
                                        紧急送药
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-right text-xs mr-1.5 text-gray-400"></i>
                                右转，进入城北大道 1.8 公里
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务3 - 紧急配送目的地 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                送
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">青山湖街道锦绣蓝庭</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        2.8km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2845</p>
                                    <p class="text-xs text-red-500 font-medium flex items-center">
                                        <i class="fas fa-heartbeat mr-1"></i>
                                        紧急送药
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-left text-xs mr-1.5 text-gray-400"></i>
                                左转，进入滨湖路 1.2 公里
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务4 - 超市取件 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-primary-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                取
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">青山超市城北店</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        2.3km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2851</p>
                                    <p class="text-xs text-blue-500 font-medium flex items-center">
                                        <i class="fas fa-snowflake mr-1"></i>
                                        冷藏商品
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-up text-xs mr-1.5 text-gray-400"></i>
                                直行 2.5 公里
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务5 - 超市目的地 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                送
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">锦北街道花园小区</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        3.7km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2851</p>
                                    <p class="text-xs text-green-600 font-medium flex items-center">
                                        <i class="fas fa-clock mr-1"></i>
                                        预计15:45送达
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="ml-3 pl-3 border-l-2 border-dashed border-gray-200 my-2">
                            <p class="text-xs text-gray-500 py-1 flex items-center">
                                <i class="fas fa-arrow-right text-xs mr-1.5 text-gray-400"></i>
                                右转，沿洋东街道 3.0 公里
                            </p>
                        </div>
                    </div>
                    
                    <!-- 任务6 - 最后一个订单 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold shadow-sm">
                                送
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-800">余杭区洋东新城时代广场</p>
                                    <span class="text-xs text-gray-500 flex items-center">
                                        <i class="fas fa-road text-gray-400 mr-1 text-xs"></i>
                                        4.8km
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">任务#2842</p>
                                    <p class="text-xs text-green-600 font-medium flex items-center">
                                        <i class="fas fa-clock mr-1"></i>
                                        预计15:30送达
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部操作栏 -->
                <div class="absolute bottom-0 left-0 right-0 flex border-t border-gray-100 bg-white p-4">
                    <button class="flex-1 py-3 btn-primary text-white rounded-xl font-medium shadow-sm hover:shadow-md transition flex items-center justify-center">
                        <i class="fas fa-location-arrow mr-2"></i>开始导航
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 页面3: 导航页面 -->
        <div id="page3" class="page hidden">
            <!-- 全屏地图 -->
            <div class="map-container relative bg-gray-200">
                <img src="https://dummyimage.com/600x800/e0e0e0/ffffff&text=导航地图" class="w-full h-full object-cover" alt="导航地图">
                
                <!-- 状态栏 -->
                <div class="absolute top-0 left-0 right-0 p-2 flex justify-between items-center text-xs text-white">
                    <span class="time">14:25</span>
                    <div class="icons flex space-x-1">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 