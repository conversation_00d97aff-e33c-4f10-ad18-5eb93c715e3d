# 海博询价接口实现文档

## 概述

本文档描述了为海博系统实现的询价接口，该接口用于获取预估配送费、配送时间等信息。

## 接口信息

- **接口地址**: `POST /api/haibo/valuating`
- **Content-Type**: `application/json`
- **功能**: 询价，获取预估配送费、配送时间等信息

## 实现文件

### 1. 控制器文件
- **文件**: `app/Http/Controllers/Api/HaiboController.php`
- **方法**: `valuating(Request $request)`
- **功能**: 处理询价请求，验证参数，调用服务层逻辑

### 2. 服务文件
- **文件**: `app/Services/HaiboService.php`
- **方法**: `valuating(array $data)`
- **功能**: 核心询价业务逻辑

### 3. 路由配置
- **文件**: `routes/api.php`
- **路由**: `Route::post('valuating', [HaiboController::class, 'valuating'])`

## 请求参数

### 必填参数

| 字段 | 类型 | 描述 | 示例值 |
|------|------|------|--------|
| tradeOrderSource | int | 交易订单来源 | 4 |
| orderId | string | 海博平台订单号 | HB202407281234567 |
| serviceCode | string | 配送商配送服务 | standard_delivery |
| recipientName | string | 收件人姓名 | 张三 |
| recipientPhone | string | 收件人电话 | 13800138001 |
| recipientAddress | string | 收件人地址 | 北京市朝阳区建国路88号 |
| recipientLng | int | 收件人经度(火星坐标*10^6) | 116447552 |
| recipientLat | int | 收件人纬度(火星坐标*10^6) | 39906901 |
| prebook | int | 是否即时单(0-即时单；1-预约单) | 0 |
| totalWeight | int | 物品重量(单位：克) | 1500 |
| carrierMerchantId | string | 配送商ID | HAIBO_MERCHANT_001 |
| senderLng | int | 发件人经度(火星坐标*10^6) | 116398419 |
| senderLat | int | 发件人纬度(火星坐标*10^6) | 39908722 |
| senderName | string | 发件人姓名 | 海博测试门店 |
| senderContract | string | 发件人电话 | 13800138000 |
| senderAddressDetail | string | 发件人详细地址 | 北京市朝阳区广顺北大街666号 |

### 可选参数

| 字段 | 类型 | 描述 | 示例值 |
|------|------|------|--------|
| tradeOrderId | string | 京、美、饿渠道订单号 | JD123456789 |
| expectedDeliveryTime | long | 收件人期望送达时间(时间戳) | 1690876800 |
| expectedLeftDeliveryTime | long | 期望送达时间段左端点 | 1690875900 |
| expectedRightDeliveryTime | long | 期望送达时间段右端点 | 1690877700 |
| expectedPickupTime | long | 预计取货时间 | 1690875000 |
| insuredMark | int | 是否保价(0-不保价；1-保价) | 0 |
| totalValue | double | 货品价值(单位：元) | 50.00 |
| totalVolume | int | 物品体积(单位：cm³) | 2000 |
| riderPickMethod | int | 订单流向(0-从商家到用户；1-从用户到商家) | 0 |
| goodsDetails | string | 物品明细(JSON格式) | 见下方示例 |
| extInfo | string | 扩展信息(JSON格式) | {"note": "测试订单"} |
| carrierShopId | string | 配送商门店id | HB_1 |
| carModelCode | string | 汽车配送车型 | - |
| category | int | 商家经营的品类信息 | 1 |

### goodsDetails 示例

```json
[
  {
    "count": 2,
    "name": "麻辣烫",
    "price": 15.00,
    "unit": "份",
    "specs": ["中辣", "加蛋"]
  },
  {
    "count": 1,
    "name": "奶茶",
    "price": 20.00,
    "unit": "杯",
    "specs": ["半糖", "去冰"]
  }
]
```

## 返回参数

### 成功响应

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "predictDeliveryTime": 1690876800,
    "actualFee": 8.50,
    "deliveryFee": 8.00,
    "deliveryDistance": 3500,
    "discountFee": 0.00,
    "insuredFee": 0.50
  }
}
```

### 返回字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| code | int | 结果码(0-成功，非0-失败) |
| message | string | 结果描述 |
| data | object | 询价结果数据 |

### data字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| predictDeliveryTime | long | 预计送达时间(时间戳) |
| actualFee | double | 预计支付金额(元) |
| deliveryFee | double | 配送费用(元) |
| deliveryDistance | int | 配送距离(米) |
| discountFee | double | 优惠金额(元) |
| insuredFee | double | 保价费(元) |

## 错误码

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 无运力覆盖 |
| 1003 | 距离超限 |
| 9999 | 系统错误 |

## 业务逻辑

### 1. 参数验证
- 验证必填参数是否完整
- 验证经纬度格式是否正确
- 验证重量参数是否合理
- 验证预约单时间参数

### 2. 运力检查
- 根据收件人经纬度检查是否有运力覆盖
- 使用CommonService的getPointSite方法

### 3. 距离计算
- 使用MapService计算两点间实际距离
- 如果API调用失败，使用直线距离估算

### 4. 距离限制检查
- 检查配送距离是否超出站点最大配送范围

### 5. 费用计算
- 使用Pricing模型计算基础配送费
- 根据距离、重量、时间等因素计算附加费用
- 计算保价费(如果需要保价)
- 计算优惠金额(预留扩展)

### 6. 送达时间计算
- 即时单：当前时间 + 基础配送时间 + 距离时间
- 预约单：使用期望送达时间

## 常量定义

### 订单来源Code
- 1: 京东
- 2: 美团  
- 3: 饿了么
- 4: 海博自营

### 商品经营品类Code
- 1: 餐饮美食
- 2: 生鲜果蔬
- 3: 医药健康
- 4: 超市百货
- 5: 鲜花绿植
- 6: 烘焙蛋糕
- 7: 饮品奶茶
- 99: 其他

## 测试

测试文件: `test_haibo_valuating.php`

包含以下测试用例:
1. 即时单询价测试
2. 预约单询价测试  
3. 参数错误测试

## 日志

所有询价请求和响应都会记录到haibo日志频道中，便于调试和监控。

## 优化特性

### 1. 智能配送时间计算
- 基础配送时间：30分钟
- 距离时间：每公里增加3分钟
- 重量时间：超过5kg每kg增加1分钟
- 高峰时段：11:00-13:00, 17:00-19:00增加10分钟
- 时间范围：最少30分钟，最多120分钟

### 2. 保价费计算
- 费率：货品价值的0.5%
- 最低保价费：1元
- 最高保价费：100元
- 最大保价金额：10000元

### 3. 参数验证增强
- 重量限制：1-50000克（1-50kg）
- 预约时间：不能是过去时间，不能超过7天
- 坐标验证：经纬度范围检查
- 距离验证：最大100公里

### 4. 错误处理
- 地图API调用失败时自动降级为直线距离
- 详细的参数验证错误信息
- 完整的日志记录

## 测试

### 单元测试
测试文件: `tests/Feature/HaiboValuatingTest.php`

包含测试用例:
1. 基本询价功能测试
2. 预约单询价测试
3. 保价询价测试
4. 参数验证测试
5. 重量超限测试
6. 预约时间验证测试

### 集成测试
测试文件: `test_haibo_valuating.php`

包含测试场景:
1. 即时单询价
2. 预约单询价
3. 参数错误测试

## 注意事项

1. 经纬度使用火星坐标系，需要乘以10^6传输
2. 重量单位为克，体积单位为立方厘米
3. 金额单位为元，保留两位小数
4. 时间戳使用GMT+8时区的Unix时间戳
5. 配送商能正常询价表示该地址有运力覆盖
6. 系统会自动处理地图API调用失败的情况
7. 所有请求都会记录详细日志便于调试

## 性能考虑

1. 使用缓存优化站点查询
2. 地图API调用失败时快速降级
3. 参数验证在业务逻辑之前执行
4. 合理的超时和重试机制
