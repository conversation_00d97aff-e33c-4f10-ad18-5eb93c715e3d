# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| host | http://127.0.0.1:8888 | String | 是 | - |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 麦芽田开放平台

> 创建人: Anthony

> 更新人: 旭东

> 创建时间: 2022-09-19 10:22:18

> 更新时间: 2023-07-29 16:42:34

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 配送开放平台

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-15 10:47:01

> 更新时间: 2024-05-31 11:55:39

**配送开放平台开放对接规范
飞书文档：
https://lukt7uky8q.feishu.cn/docx/NOlvdPzNnoaudfxgmMGcvcbHnLc**

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

### 开放能力更新&发布记录

> 创建人: Anthony

> 更新人: Anthony

> 创建时间: 2024-01-10 12:07:16

> 更新时间: 2024-12-19 11:47:55

```text
一、分级标准
- S级【平台强制要求】：如果SaaS服务商未调整，将对现有业务流程产生堵塞，或者引起线上Bug，及涉及平台体验要求的重大升级，因此需全面通知且限期整改。
例如：发单功能，SaaS未调整将引起商家入单失败。
- A级【平台建议升级】：如果SaaS服务商未调整，不堵塞现有业务流程，但引起业务体验下降，包括但不限于商家业务流程低效、部分线下数据失准等问题；因此需全面通知，但可根据商家/服务商自行排期整改。
例如：配送信息回传升级，未回传可能导致顾客进线多，咨询商家的对接信息。
- B级【SaaS按需调用】：如果SaaS服务商未调整，不产生业务流程风险，平台只需告知SaaS服务商按需调用。
例如：在线充值链接，SaaS调通后，商家可以在SaaS上完成充值使用，无需登录配送端。

二、能力更新记录

| 序号 | 发布时间 | 分级评估 | 功能模块 | 简介 | 上线时间 | 具体字段 | 文档链接 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | 1.10 | S级 | 订单计费接口，下单接口 | 为提升预订单和即时单的订单接入体验，业务将上新的订单流水号接口，用于解决预订单订单号重复错乱等问题，请各合作伙伴在2月1日前完成新接口参数接入，届时，旧接口参数将做下线处理。1）解决场景：因现有订单流水号是按“用户下单日的下单时间顺序”生成纯数字编号，因流水号在用户下单生成后不变，如果门店有预订单时，会存在与配送日当天即时下单订单号重复情况，容易产生骑手到店报流水号取错货的情况，升级后可解决此类问题。2）变更内容：为预订单增加：下单月日标识，让骑手仅通过流水号即可准确取货。 | 1月17日 | 迭代更新2个接口，增加【full_sn】字符参数字段，【sn】字段后续会做下线处理 | 1. https://console-docs.apipost.cn/preview/272c3a66e3a4af27/4c99da58f091d4ff?target_id=d0105d5b-2191-4829-9839-d738952eb0ca 2. https://console-docs.apipost.cn/preview/272c3a66e3a4af27/4c99da58f091d4ff?target_id=b1ca719e-63f2-41a4-9983-2669d50e64b3 |
| 1 | 1.20 | A级 | 快递轨迹订阅接口，快递轨迹回传接口，下单接口 | 为了使不同的下游服务商可以对接开放平台，不单单给SaaS使用，增加了服务商概念，同事增加快递相关业务场景，下单增加快递业务相应字段。| 1月22日 | 1）callback相关接口header头统一增加服务商【Isv-Key】 2）订单查询接口增加【mobile】字段 3）下单接口增加【exp_type，pickup_start_time，pickup_end_time】字段 4）授权接口增加【source_key，platform】字段 | 1. https://console-docs.apipost.cn/preview/272c3a66e3a4af27/4c99da58f091d4ff?target_id=d0105d5b-2191-4829-9839-d738952eb0ca 2. https://console-docs.apipost.cn/preview/272c3a66e3a4af27/4c99da58f091d4ff?target_id=b1ca719e-63f2-41a4-9983-2669d50e64b3 |
```

**Query**

### 申请沙箱环境账号

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-15 11:56:54

> 更新时间: 2024-12-19 11:40:31

#### 申请沙箱环境账号

###### 需要准备提供相关信息

| 信息  | 说明  |
| --- | --- |
| 应用名称 | 您申请的开放平台应用名称 |
| 应用图标 | 您应用的图标,SVG格式,透明背景,128x128尺寸 |
| 公司名 | 您的公司名称 |
| 公司地址 | 您的公司地址 |
| 技术负责人 | 系统故障紧急联系人 |
| 技术负责人联系方式 | 紧急联系方式 |
| 邮箱 | 系统重要信息接收邮箱 |
| 正式回调地址 | 正式开放平台回调接口域名 |
| 测试回调地址 | 测试阶段开放平台回调接口域名 |
| 授权回调地址 | 授权流程开放平台回调接口完整地址 |
| 测试授权回调地址 | 测试授权流程开放平台回调接口完整地址 |

###### 对接必须资料

| 信息  | 解释  |
| --- | --- |
| APP名称 | 您平台的App名称 |
| 客服电话 | 您平台的客服电话 |
| 紧急联系人 | 您平台的联系人 |
| 手机号 | 您平台的联系人电话 |
| 配送简介 | 您平台的简短介绍，会在麦芽田显示 |
| 预约单 | 是否支持预约订单 |
| 加小费（下单时） | 是否支持下单添加小费 |
| 加小费（下单后） | 是否支持下单后添加小费 |

###### 测试推送地址：

**https://open-api-test.maiyatian.com/v1/delivery/{command}
仅供测试阶段使用**

###### 麦芽田测试后台地址：

**http://saas.open.test.maiyatian.com/
仅供测试阶段使用（你方需要自己注册测试账号）**

###### 测试配送回传工具：

**http://h5.mock.test.maiyatian.com/callback/maiyatian/status/
仅供测试阶段使用**

###### 签名校验工具：

**https://open-api-test.maiyatian.com/test/
仅供测试阶段使用**

###### 以下重要信息通过邮件发送

| 字段  | 说明  |
| --- | --- |
| app_key | 应用key |
| app_secret | 应用密钥 |
| tag | 分配您应用tag |

**Query**

### 接口规范

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-10-27 17:16:44

> 更新时间: 2024-12-24 09:52:10

#### 接口规范

| 版本  | 说明  | 最后修改时间 |
| --- | --- | ------ |
| v0.1 | 文档初版 | 2022-06-23 |

#### 概述

**适用于麦芽田配送接入**

#### 接入协议

**订单数据通过HTTP POST raw json推送给你方系统，推送Command和回调Command一致，概念和参数如下**

#### 推请求URL

**正式地址：
https://open-api.maiyatian.com/v1/delivery/{command}**

> 请对接完成，通过验收后再使用正式地址！
> 严禁使用正式地址进行接口调试！
> 其中{command}见附录

#### 回调请求对接方URL

**https://{对接方提供请求地址}/{command}**

#### 请求方式

**POST**

#### Content-Type

**application/json;charset=utf-8**

| 字段名 | 类型  | 是否必须 | 说明  |
| --- | --- | ---- | --- |
| app_key | String | 是 | 应用key |
| request_id | String | 是 | 标识此次请求的UUID，比如A62E9C508-048A-A624-8F6F-C7EC7315416，每次请求需生成新的值 |
| timestamp | Long | 是 | 请求发起时的Unix时间戳，精确到毫秒。需和实际时间误差不超过10分钟，否则无法通过签名验证 |
| token | String | 是 | 麦芽田商户店铺token（access_token接口时值为空，字段保留，若返回接口未授权，请检查签名） |
| signature | String | 是 | 签名，规则见后续描述 |
| data | String | 是 | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| command | String | 否 | Command事件 |

**示例如下：**

```
{
    "app_key": "1828372",
    "request_id": "A62E9C508-048A-A624-8F6F-C7EC7315416",
    "timestamp": 1655980727000,
    "token":"xxxxxxxxxxxxx",
    "signature": "8Ph3jEWUPe1oJkOEqysbsyXTBi-FEdj0TgIkG7QW7B0=",
    "data": "{\"order\":{\"order_id\":\"2020090112345\",\"sn\":5,\"ctime\":1606205346,\"is_pre_order\":false,\"pick_type\":\"TAKEAWAY\"},\"order_customer\":{\"recipient_phone\":\"13800138000\"},\"shop\":{\"shop_id\":123456}}",
    "command":"xxxx"
}
```

#### 签名生成规则

**以上述请求体为例，生成签名的规则为，提取请求体中的app_key，token, timestamp，data字段及path中的command，按ksort排序，用半角逗号连接起来，生成如下字符串dataToSign：**

```
command=ORDER_CONFIRMED,data={"order":{"order_id":"2020090112345","sn":5,"ctime":1606205346,"is_pre_order":false,"pick_type":"TAKEAWAY"},"order_customer":{"phone":"13800138000"},"shop":{"shop_id":123456}},timestamp=1606208735000
```

**然后对dataToSign进行utf8编码，再使用appSecret计算HmacSHA256值，并把结果用Url安全的Base64编码，作为最终签名。,签名校验工具：
https://open-api-test.maiyatian.com/test/,PHP版:**

```php
function signatureData(array $data, $command, $appKey, $appSecret, $token)
{
    $signData = [
        'app_key'   => $appKey,
        'command'   => $command,
        'data'      => $data['data'],
        'timestamp' => $data['timestamp'],
        'token'     => $token,
    ];

    ksort($signData);
    
    $dataToSign = '';
    foreach ($signData as $k => $v) {
        $dataToSign .= $k . '=' . $v . ',';
    }

    $dataToSign = substr($dataToSign, 0, -1);
    $hash  = hash_hmac('sha256', $dataToSign, $appSecret, true);
    $signData['signature'] = strtr(base64_encode($hash), '+/', '-_');
    unset($signData['command']);
    return $signData;
}
```

**java版：**

```java
public static String getSignature(RequestDto dto, String command, String appKey, String appSecret, String token) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("app_key", appKey);
        map.put("command", command);
        map.put("data", dto.getData());
        map.put("timestamp", dto.getTimestamp());
        map.put("token", token);
    
        // 先将参数以其参数名的字典序升序进行排序
        Map<String, Object> sortedParams = new TreeMap<>(map);
        Set<Map.Entry<String, Object>> entrySet = sortedParams.entrySet();
    
        // 遍历排序后的字典，将所有参数按"key=value"格式拼接在一起
        StringBuilder stringToSign = new StringBuilder();
        for (Map.Entry<String, Object> param : entrySet) {
            stringToSign.append(param.getKey()).append("=").append(param.getValue()).append(",");
        }
        stringToSign.deleteCharAt(stringToSign.length() - 1);
    
        System.out.println(stringToSign);
        return calSignature(appSecret, stringToSign.toString());
    }
    
    private static String calSignature(String appSecret, String dataToSign) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(appSecret.getBytes(), "HmacSHA256");
        Mac sha256HMAC = Mac.getInstance("HmacSHA256");
        sha256HMAC.init(secretKeySpec);
        byte[] hmacResult = sha256HMAC.doFinal(dataToSign.getBytes());
        Base64.Encoder encoder = Base64.getUrlEncoder();
        byte[] base64Result = encoder.encode(hmacResult);
        return new String(base64Result);
    }
```

**go版：**

```go
type SignData struct {
    AppKey    string
    Token     string
    Timestamp int64
    Data      string
    Command   string
}

func main() {
    []byte AppSecret := []byte("sdfwfsd892sfjkldsfuwofjsdfwevbwf")
    signData := SignData{
        AppKey: "xsdfw2wf",
        Token: "",
        Timestamp: time.Now().UnixMilli(),
        Data: "{\"order\":{...}}",
        Command: "ORDER_CREATED",
    }
    sign := GenSignString(signData, AppSecret)
    fmt.Println(sign)
}

func GenSign(data SignData, secretKey []byte) []byte {
    signData := map[string]interface{}{
        "app_key":   data.AppKey,
        "token":     data.Token,
        "timestamp": data.Timestamp,
        "data":      data.Data,
        "command":   data.Command,
    }

    keys := make([]string, len(data))
    i := 0
    for k, _ := range data {
        keys[i] = k
        i++
    }
    sort.Strings(keys)
    dataToSign := ""
    for _, k := range keys {
        dataToSign += fmt.Sprintf("%s=%v,", k, data[k])
    }
    dataToSign = dataToSign[:len(dataToSign)-1]
    bs := []byte(dataToSign)
    mac := hmac.New(sha256.New, secretKey)
    mac.Write(bs)
    sign := mac.Sum(nil)
    
    return sign
}

func GenSignString(data map[string]interface{}, secretKey []byte) string {
    return base64.URLEncoding.EncodeToString(GenSign(data, secretKey))
}
```

**nodejs版：**

```nodejs
let signatureData = function (data, command, appKey, appSecret, token) {
    let signData = {
        'app_key'   : appKey,
        'command'   : command,
        'data'      : data.data,
        'timestamp' : data.timestamp,
        'token'     : token,
    };

    let sortObj = {}, keys = Object.keys(signData);
    keys.sort();
    keys.forEach((key) => {
        sortObj[key] = signData[key];
    });

    let dataToSign = '';
    for (const _k in sortObj) {
        const _v = sortObj[_k];
        dataToSign += _k + '=' + _v + ',';
    }

    dataToSign     = dataToSign.substring(0, dataToSign.length - 1);
    const hashHmac = (key, message) => {
        return crypto.createHmac('sha256', key)
            .update(message)
            .digest()
            .toString('base64')
    }

    let hash = hashHmac(appSecret, dataToSign);
    console.log(hash)
    hash             = hash.replace(/\+/g, "-");
    hash             = hash.replace(/\//g, "_");
    signData['signature'] = hash
    delete signData.command;
    return signData;
}
```

#### 通用返回值说明

**格式为JSON，Content-Type为application/json;charset=utf-8，字段和含义如下**

| 字段名 | 类型  | 是否必须 | 说明  |
| --- | --- | ---- | --- |
| code | Int | 是 | 返回码，200表示成功，其他值表示错误码，比如：400：参数错误500：服务器内部错误 |
| message | String | 是,code不为200时 | 返回消息，如果出错，表示具体错误信息 |
| data | String | 否 | 根据接口返回,返回的为json结构数据 |

**示例如下：**

```
{
    "code": 200,
    "message": "ok",
    "data": ""
}
```

**错误重试说明
如果出现网络错误，或者通用返回值的code为5开头（比如500），请重试，重试间隔为1分钟，最大重试次数3次**

**Query**

### 流程图

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-15 12:09:13

> 更新时间: 2024-12-19 11:40:26

```text
* 接口流程图
![image.png](https://img.cdn.apipost.cn/client/user/1035682/avatar/78805a221a988e79ef3f42d7c5bfd418634cf8459cf6c.png)

* 配送状态流转
![image.png](https://img.cdn.apipost.cn/client/user/1035682/avatar/78805a221a988e79ef3f42d7c5bfd41863774b7abfbba.png)
```

**Query**

### 授权

> 创建人: Sjq

> 更新人: Sjq

> 创建时间: 2022-10-27 15:27:14

> 更新时间: 2022-10-27 15:27:14

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 商户绑定配送账号授权说明

> 创建人: Sjq

> 更新人: zhg399213463

> 创建时间: 2022-10-27 15:13:07

> 更新时间: 2024-05-31 11:55:40

###### 1.授权简介

###### 1.1 授权说明

```
麦芽田开放平台是基于OAuth2协议的开放授权和鉴权服务，接入前需要了解标准的OAuth2的一些相关知识，可以参考文档 THE OAUTH 2.0 AUTHORIZATION FRAMEWORK。
如您已创建的应用通过麦芽田开放平台操作用户数据时，您需要引导用户完成帐号「授权登录」的流程。该流程采用国际通用的OAuth2.0标准协议作为用户身份验证与授权协议，目前麦芽田开放平台OAuth2.0服务支持采用以下方式获取token。
```

**注：OAuth2相关知识是接入必备，建议提前阅读。**

###### 1.2 授权流程

**授权操作时，请以下按流程图逐步完成授权全流程：**

![image.png](https://img.cdn.apipost.cn/client/user/1035682/avatar/78805a221a988e79ef3f42d7c5bfd4186359f027bae49.png "")

| 账号类型 | 授权形式 | 授权页链接 |
| ---- | ---- | ----- |
| 麦芽田商户 | WEB端网页授权 | 由配送方提供 |

###### 2.OAuth2的code授权流程及接口简介

###### 2.1 code授权流程

![配送开放平台授权时序图.jpg](https://img.cdn.apipost.cn/client/user/681384/avatar/235a3a310082f35d3a8b8b825cb21150635a4b1f7d9c2.jpg "")

###### 2.2 获取授权码code

> 组装授权页面

**组装授权页URL（麦芽田侧组装），URL标准格式如下：**

> https://{授权回调地址}?response_type=code&view=web&redirect_uri={client_id对应的回调地址}&code={用来获取accessToken}&state={自定义参数}

**组装示例：**

> https://{授权回调地址}/?response_type=code&view=web&redirect_uri=xxx&code=xxx&state=xxx

**授权页组装参数如下：**

| 参数名 | 是否必选 | 示例值 | 描述  |
| --- | ---- | --- | --- |
| response_type | 是 | code | 授权步骤类型，固定值为code |
| redirect_uri | 是 | 在用户登录并授权后，配送方需要重定向到该redirect_uri |  |
| code | 是 | 授权链接会携带此参数 | 用来获取访问令牌token |
| view | 可选 | web | 可选web或h5，默认为web，H5移动端网页授权必填 |
| state | 是 | 授权链接会携带此参数 | 自定义参数 |

> 如果授权成功，配送方授权服务器需要将用户的浏览器重定向到：http(s)://redirect_uri?state=xxx
>  ** state值访问授权链接会携带此参数 **
> 组装示例：https://saas.maiyatian.com/logistic/mytApiAuth/?state=xxx
>    用户完成「授权登录」后，配送方需要使用授权码code去换取token。

****    注意事项：code有效期5分钟，5分钟后code过期则需要重新授权，多次使用code换取token是一样的****

###### 2.3 用授权码code换取长时令牌refresh_token以及访问令牌token

> 授权码code换取访问令牌accessToken

**通过access_token command接口获取token（调用参数方式参考API调用详解）,请求地址: access_token  ** 此接口为后端接口**
 请求方法: POST
 请求参数: 调用参数方式参考API调用详解**

> 刷新授权TOKEN

**当token 过期时，可以使用(在有效期内的) refresh_token重新获取新的token，若refresh_token也过期了，则需要再次经过用户授权，因此需要关注refresh_token的时效，需要在时效内用此接口再换取新的refresh_token才不会出现用户授权频繁失效的情况。该接口刷新得到新的token 和refresh_token, 旧的token随即在5分钟内失效。（调用参数方式参考API调用详解）,请求地址: refresh_token  ** 此接口为后端接口**
 请求方法: POST
 请求参数: 调用参数方式参考API调用详解**

**Query**

#### 授权码code获取access_token

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-10-27 15:29:42

> 更新时间: 2024-12-19 11:39:51

**简要描述**


 - 用授权码code换取长时令牌refreshToken以及访问令牌accessToken

**command值**


 - access_token

**接口状态**

> 已完成

**接口URL**

> access_token

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=27366b4

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Isv-Key | 0MFmUJRV | String | 是 | 服务商Key |

**请求Body参数**

```javascript
{
    "grant_type":"1", //授权级别 1:门店 2:商户    22
    "code":"xxxxx",
    "mobile":"", //三方平台账号注册手机号
    "store_id":"", //三方平台最小维度的唯一ID（如果平台方是门店维度的返回门店ID）
    "city":"", //城市名称（例：北京市）
    "city_code":"10001",
    "source_key":"", //三方平台最小维度的唯一ID对应密钥
    "platform":"JD"//快递平台唯一标识（例：京东 JD）
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| grant_type | 1 | String | 是 | 授权级别 1:门店 2:商户    22 |
| code | xxxxx | String | 是 | 授权码 |
| mobile | - | String | 是 | 三方平台账号注册手机号 |
| store_id | - | String | 是 | 三方平台最小维度的唯一ID（如果平台方是门店维度的返回门店ID） |
| city | - | String | 是 | 城市名称（例：北京市） |
| city_code | 10001 | String | 是 | 城市code |
| source_key | - | String | 否 | 三方平台最小维度的唯一ID对应密钥 |
| platform | JD | String | 否 | 快递平台唯一标识（例：京东 JD） |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok",
    "data": "{\"shop_id\":\"1\",\"token\":\"123123123\",\"refresh_token\":\"123123123\",\"expire_time\":123123123,\"refresh_expire_time\":123123123}"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| shop_id | 1 | String | 平台方渠道ID |
| token | 123123123 | String | 麦芽田授权token |
| refresh_token | 123123123 | String | 刷新授权token |
| expire_time | 123123123 | Number | token过期时间(30天) |
| refresh_expire_time | 123123123 | Number | token过期时间(30天) |

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Isv-Key | 0MFmUJRV | String | 是 | 服务商Key |

**Query**

#### refreshToken刷新accessToken

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-10-27 15:52:48

> 更新时间: 2024-12-19 11:28:25

**简要描述**


 - 用授权码code换取长时令牌refreshToken以及访问令牌accessToken

**command值**


 - refresh_token

**接口状态**

> 已完成

**接口URL**

> refresh_token

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=2737a66

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "token":"123123123",
    "refresh_token": "123123123"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| token | 123123123 | String | 是 | 访问令牌 |
| refresh_token | 123123123 | String | 是 | 刷新授权token |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok",
    "data": "{\"token\":\"123123123\",\"refresh_token\":\"123123123\",\"expire_time\":123123123,\"refresh_expire_time\":123123123}"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| token | 123123123 | String | 麦芽田授权token |
| refresh_token | 123123123 | String | 刷新授权token |
| expire_time | 123123123 | Number | token过期时间(30天) |
| refresh_expire_time | 123123123 | Number | token过期时间(30天) |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 推送command

> 创建人: Bobo

> 更新人: Bobo

> 创建时间: 2022-09-15 17:21:37

> 更新时间: 2022-09-15 17:21:37

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 配送状态同步(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-10-11 12:12:53

> 更新时间: 2025-02-19 16:09:27

**简要描述
*同步订单配送状态操作。,command值**


 - delivery_change

**要求**

> 必须对接实现

**接口状态**

> 已完成

**接口URL**

> delivery_change

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=25af5d1

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "order_no": "116157133388249348",
    "source_order_no": "11667336008123456",
    "shop_id": "123", //三方商户ID、门店ID
    "status": "PENDING",
    "rider_name": "林骑手",
    "rider_phone": "13888888888_1234",
    "longitude": "103.11111",
    "latitude": "90.1123123",
    "pickup_code": "取货码",
    "cancel_type": 1,
    "cancel_reason": "取消原因",
    "vehicle_info": {
        "vehicle_name": "雪铁龙C5",
        "vehicle_color": "蓝色",
        "vehicle_no": "冀E4WE32"
    },
    "at_time": 1657377391,
    "is_transship": false
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| order_no | 116157133388249348 | String | 是 | 三方平台运单号 |
| source_order_no | 11667336008123456 | String | 是 | 麦芽田订单号 |
| shop_id | 123 | String | 是 | 三方商户ID、门店ID |
| status | PENDING | String | 是 | 订单状态
未处理:UNPROGRESS
待确认:CREATED
已确认:CONFIRM
待抢单:DELIVERY
待取货:PICKUP
配送中:DELIVERING
已完成:DONE
已取消:CANCEL
已删除:DELETE
配送异常:EXPECT |
| rider_name | 林骑手 | String | 是 | 门店名称 |
| rider_phone | 13888888888_1234 | String | 是 | 客户电话 |
| longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| pickup_code | 取货码 | String | 否 | 取货码 |
| cancel_type | 1 | Number | 否 | 取消类型（见附录） |
| cancel_reason | 取消原因 | String | 否 | 取消原因 |
| vehicle_info | - | Object | 否 | 车辆信息 |
| vehicle_info.vehicle_name | 雪铁龙C5 | String | 否 | 门店名称 |
| vehicle_info.vehicle_color | 蓝色 | String | 否 | 车辆颜色 |
| vehicle_info.vehicle_no | 冀E4WE32 | String | 否 | 车牌号 |
| at_time | 1657377391 | Number | 是 | 更新时间 |
| is_transship | false | Boolean | 否 | 是否接驳单 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | Integer | 状态码 |
| message | ok | String | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 快递轨迹回传(必接)

> 创建人: zhg399213463

> 更新人: Anthony

> 创建时间: 2024-01-15 17:32:33

> 更新时间: 2024-12-25 11:51:01

```text
暂无描述
```

**接口状态**

> 已完成

**接口URL**

> location_change

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"order_no": "DPK364322890411",
	"source_order_no": "12826392824659312640",
	"shop_id": "1214123",
	"tag": "",
	"locations": [{
		"description": "[合肥市]【安徽合肥沐涵公司】的西瓜揽收已揽收",
		"city": "合肥市",
		"longitude": "",
		"latitude": "",
		"status": "PICKUP",
		"remark": "",
		"update_time": 1700283951
	}, {
		"description": "[合肥市]安徽合肥沐涵公司-西瓜揽收-已收件",
		"city": "合肥市",
		"longitude": "",
		"latitude": "",
		"status": "PICKUP",
		"remark": "",
		"update_time": 1700283951
	}, {
		"description": "[合肥市]快件已到达【安徽合肥转运中心】",
		"city": "合肥市",
		"longitude": "",
		"latitude": "",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700305944
	}, {
		"description": "[合肥市]快件到达【安徽合肥转运中心】",
		"city": "合肥市",
		"longitude": "",
		"latitude": "",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700305944
	}, {
		"description": "[合肥市]快件离开【安徽合肥转运中心】已发往【吉林长春转运中心】",
		"city": "合肥市",
		"longitude": "",
		"latitude": "",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700306760
	}, {
		"description": "[长春市]快件已到达【吉林长春转运中心】",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700437582
	}, {
		"description": "[长春市]快件到达【吉林长春转运中心】",
		"city": "吉林市",
		"longitude": "119.1773000",
		"latitude": "34.8406500",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700437582
	}, {
		"description": "[长春市]快件离开【吉林长春转运中心】已发往【吉林长春合隆公司】",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700444052
	}, {
		"description": "[长春市]快件已到达【吉林长春合隆公司】咨询电话：xxx",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700462668
	}, {
		"description": "[长春市]快件到达【吉林长春合隆公司】",
		"city": "吉林市",
		"longitude": "119.1773000",
		"latitude": "34.8406500",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700462668
	}, {
		"description": " [长春市]【吉林长春合隆公司】的申通小哥,正在为您派送(可放心接听95089申通专属派送号码)，投诉电话:xxx",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700463265
	}, {
		"description": "[长春市]【吉林长春合隆公司】(xxx)的申通小哥(xxx)正在为您派送，（可放心接听95089申通专属派送号码）",
		"city": "吉林市",
		"longitude": "119.1773000",
		"latitude": "34.8406500",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700463265
	}, {
		"description": "[驿站]您的包裹已存放至兔喜快递超市，记得来长春宽城合隆镇万隆广场店万隆广场6号门对面取它回家！如有取件码问题或找不到包裹等问题，请联系：，如您未收到此快递，请拨打投诉电话：xxx! ",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DELIVERING",
		"remark": "",
		"update_time": 1700471910
	}, {
		"description": "[驿站]包裹已签收！签收人凭取货码签收，如有问题请联系：xxx，投诉电话：xxx。起早贪黑不停忙，如有不妥您见谅，好评激励我向上，求个五星暖心房。",
		"city": "长春市",
		"longitude": "113.7517900",
		"latitude": "23.0206700",
		"status": "DONE",
		"remark": "",
		"update_time": 1700472206
	}]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| order_no | DPK364322890411 | String | 是 | 三方平台订单号 |
| source_order_no | 12826392824659312640 | String | 是 | 麦芽田侧单号（下单接口提供的单号） |
| shop_id | 1214123 | String | 是 | 三方商户ID、门店ID |
| tag | - | String | 否 | - |
| locations | - | Array | 是 | 轨迹 |
| locations.description | [合肥市]【安徽合肥沐涵公司】的西瓜揽收已揽收 | String | 否 | 描述 |
| locations.city | 合肥市 | String | 否 | 市 |
| locations.longitude | - | String | 是 | 经度(国测局02标准，如高德) |
| locations.latitude | - | String | 是 | 纬度(国测局02标准，如高德) |
| locations.status | PICKUP | String | 是 | 状态
未处理:UNPROGRESS
待确认:CREATED
已确认:CONFIRM
待抢单:DELIVERY
待取货:PICKUP
配送中:DELIVERING
已完成:DONE
已取消:CANCEL
已删除:DELETE
配送异常:EXPECT |
| locations.remark | - | String | 否 | - |
| locations.update_time | 1700283951 | Integer | 是 | 1657098040 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"message": "ok"
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

### 回调command

> 创建人: Bobo

> 更新人: Bobo

> 创建时间: 2022-09-15 17:22:01

> 更新时间: 2022-09-15 17:22:01

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 获取城市运力

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-16 12:51:15

> 更新时间: 2024-12-18 14:41:09

**获取城市运力,配送方城市运力接口,command值,city_capacity**

**接口状态**

> 已完成

**接口URL**

> city_capacity

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=23f8bb3

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "[{\"city_name\":\"北京市\",\"city_code\":\"100010\"},{\"city_name\":\"上海\",\"city_code\":\"100020\"}]",
    "message": "ok"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Array | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| city_name | 北京市 | String | 门店名称 |
| city_code | 100010 | String | 状态码 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 订单计费接口(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-18 23:58:06

> 更新时间: 2025-02-18 09:42:54

**查询订单运费
查询订单运费接口,command值
valuating**

**接口状态**

> 已完成

**接口URL**

> valuating

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=241bbce

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1000",
	"is_pre_order": false,
	"is_transship": false,
	"delay_delivery_time": 1665487116,
	"expect_finish_time": 1665490716,
	"tips": 100,
	"source_order_no": "11667336008123456",
	"remark": "备注",
	"sender": {
		"name": "发货门店名称",
		"phone": "发货门店电话",
		"address": "发货门店地址",
		"address_detail": "发货门店地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"receiver": {
		"name": "收货人",
		"phone": "收货人电话",
		"address": "收货人地址",
		"address_detail": "收货人地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"order_info": {
		"sn": 101,
		"full_sn": "101_1230",
		"source": "meituan",
		"channel_name": "渠道名称",
		"category": "xiaochi",
		"weight": 1,
		"total_fee": 1300,
		"paid_fee": 1200,
		"is_from_door": 1,
		"is_to_door": 1,
		"goods_list": [
			{
				"name": "商品名称",
				"number": 1,
				"price": 100
			}
		]
	}
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1000 | String | 是 | 平台方渠道ID |
| is_pre_order | false | Boolean | 是 | 是否预约单 |
| is_transship | false | Boolean | 否 | 是否接驳单 |
| delay_delivery_time | 1665487116 | Number | 是 | 延迟期望配送发单时间（单位：unix 时间戳） |
| expect_finish_time | 1665490716 | Number | 是 | 期望送达时间（单位：unix 时间戳） |
| tips | 100 | Number | 是 | 小费 |
| source_order_no | 11667336008123456 | String | 是 | 订单号 |
| remark | 备注 | String | 是 | 用户备注 |
| sender | - | Object | 是 | - |
| sender.name | 发货门店名称 | String | 是 | 门店名称 |
| sender.phone | 发货门店电话 | String | 是 | 客户电话 |
| sender.address | 发货门店地址 | String | 是 | 客户详细地址 |
| sender.address_detail | 发货门店地址详情 | String | 是 | - |
| sender.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| sender.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| sender.city_code | 110100 | String | 是 | 状态码 |
| receiver | - | Object | 是 | - |
| receiver.name | 收货人 | String | 是 | 门店名称 |
| receiver.phone | 收货人电话 | String | 是 | 客户电话 |
| receiver.address | 收货人地址 | String | 是 | 客户详细地址 |
| receiver.address_detail | 收货人地址详情 | String | 是 | - |
| receiver.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| receiver.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| receiver.city_code | 110100 | String | 是 | 状态码 |
| order_info | - | Object | 是 | - |
| order_info.sn | 101 | Number | 是 | 订单流水号 |
| order_info.full_sn | 101_1230 | String | 是 | - |
| order_info.source | meituan | String | 是 | 订单来源标识（来源枚举见附录） |
| order_info.channel_name | 渠道名称 | String | 是 | 门店名称 |
| order_info.category | xiaochi | String | 是 | 订单分类(麦芽田枚举) |
| order_info.weight | 1 | Number | 是 | 商品重量（单位：千克） |
| order_info.total_fee | 1300 | Number | 是 | 订单原价总金额 |
| order_info.paid_fee | 1200 | Number | 是 | 实付金额(分) |
| order_info.is_from_door | 1 | Number | 是 | 是否上门取件,0 不上门取件 1 上门取件 |
| order_info.is_to_door | 1 | Number | 是 | 是否送件上门,0 不送件上门 1 送件上门 |
| order_info.goods_list | - | Array | 是 | - |
| order_info.goods_list.name | 商品名称 | String | 是 | 门店名称 |
| order_info.goods_list.number | 1 | Number | 是 | 数量 |
| order_info.goods_list.price | 100 | Number | 是 | 价格 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "{\"pay_amount\":500,\"coupon\":100,\"distance\":1200,\"premium\":100,\"weight\":0,\"overweight\":0,\"tips\":100,\"at_time\":1663344226,\"expect_time\":1663344226,\"order_no\":\"123434543454\",\"source_order_no\":\"DH3123123123123\",\"billing_no\":\"123asd123\",\"service_pkg\":\"xxx\",\"service_tag\":\"专人直送\"}",
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| pay_amount | 500 | Number | 运费（分） |
| coupon | 100 | Number | 优惠费用(分),如遇没有可传0 |
| distance | 1200 | Number | 配送距离（米） |
| premium | 100 | Number | 当前溢价(分),如遇没有可 |
| weight | 0 | Number | 重量（克） |
| overweight | 0 | Number | 超重（克） |
| tips | 100 | Number | 加费(分),如遇没有可传0 |
| at_time | 1663344226 | Number | 计费时间 |
| expect_time | 1663344226 | Number | 期望送达时间 |
| order_no | 123434543454 | String | 三方运单号 |
| source_order_no | DH3123123123123 | String | 麦芽田订单号 |
| billing_no | 123asd123 | String | 计价三方唯一计费号 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 下单接口(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-19 00:08:20

> 更新时间: 2025-02-18 09:43:07

**配送下单
配送下单接口,command值
  send**

**接口状态**

> 已完成

**接口URL**

> send

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=241bc12

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "123",
	"is_pre_order": false,
	"is_transship": false,
	"delay_delivery_time": 1665487116,
	"expect_finish_time": 1665490716,
	"tips": 100,
	"source_order_no": "11667336008123456",
	"remark": "备注",
	"exp_type": "1",
	"pickup_start_time": 0,
	"pickup_end_time": 0,
	"custid": "xxxxxxxxx",
	"pay_mode": 0,
	"sender": {
		"name": "发货门店名称",
		"phone": "发货门店电话",
		"address": "发货门店地址",
		"address_detail": "发货门店地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"province_code": "110100",
		"city_code": "110100",
		"district_code": "110100"
	},
	"receiver": {
		"name": "收货人",
		"phone": "收货人电话",
		"address": "收货人地址",
		"address_detail": "收货人地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"province_code": "110100",
		"city_code": "110100",
		"district_code": "110100"
	},
	"order_info": {
		"sn": 101,
		"full_sn": "101_1230",
		"source": "meituan",
		"channel_tag": "meituan",
		"channel_name": "渠道名称",
		"source_no": "1667336008123456",
		"category": "xiaochi",
		"weight": 1,
		"total_fee": 1300,
		"paid_fee": 1200,
		"goods_list": [
			{
				"name": "商品名称",
				"number": 1,
				"price": 100
			}
		]
	}
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 123 | String | 是 | 平台方渠道ID |
| is_pre_order | false | Boolean | 是 | 是否预约单 |
| is_transship | false | Boolean | 是 | 是否接驳单 |
| delay_delivery_time | 1665487116 | Number | 是 | 延迟期望配送发单时间（单位：unix 时间戳） |
| expect_finish_time | 1665490716 | Number | 是 | 期望送达时间（单位：unix 时间） |
| tips | 100 | Number | 是 | 小费 |
| source_order_no | 11667336008123456 | String | 是 | 订单号 |
| remark | 备注 | String | 否 | 用户备注 |
| exp_type | 1 | String | 否 | 佣金 |
| pickup_start_time | 0 | Number | 否 | 快递预约上门揽件开始时间 |
| pickup_end_time | 0 | Number | 否 | 快递预约上门揽件结束时间 |
| custid | xxxxxxxxx | String | 否 | 快递月结账号 |
| pay_mode | 0 | Number | 否 | 运费付款方式，根据实际情况选择一种付款方式：0-寄付现结；1-寄付月结；2-收方付；3-第三方付； |
| sender | - | Object | 是 | - |
| sender.name | 发货门店名称 | String | 是 | 门店名称 |
| sender.phone | 发货门店电话 | String | 是 | 客户电话 |
| sender.address | 发货门店地址 | String | 是 | 客户详细地址 |
| sender.address_detail | 发货门店地址详情 | String | 是 | - |
| sender.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| sender.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| sender.province_code | 110100 | String | 是 | 状态码 |
| sender.city_code | 110100 | String | 是 | 状态码 |
| sender.district_code | 110100 | String | 是 | 状态码 |
| receiver | - | Object | 是 | - |
| receiver.name | 收货人 | String | 是 | 门店名称 |
| receiver.phone | 收货人电话 | String | 是 | 客户电话 |
| receiver.address | 收货人地址 | String | 是 | 客户详细地址 |
| receiver.address_detail | 收货人地址详情 | String | 是 | - |
| receiver.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| receiver.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| receiver.province_code | 110100 | String | 是 | 状态码 |
| receiver.city_code | 110100 | String | 是 | 状态码 |
| receiver.district_code | 110100 | String | 是 | 状态码 |
| order_info | - | Object | 是 | - |
| order_info.sn | 101 | Number | 是 | 订单流水号 |
| order_info.full_sn | 101_1230 | String | 否 | - |
| order_info.source | meituan | String | 是 | 订单来源标识（来源枚举见附录） |
| order_info.channel_tag | meituan | String | 是 | 订单渠道tag |
| order_info.channel_name | 渠道名称 | String | 是 | 门店名称 |
| order_info.source_no | 1667336008123456 | String | 是 | 渠道订单号 |
| order_info.category | xiaochi | String | 是 | 订单分类(麦芽田枚举) |
| order_info.weight | 1 | Number | 是 | 商品重量（单位：千克） |
| order_info.total_fee | 1300 | Number | 是 | 订单原价总金额 |
| order_info.paid_fee | 1200 | Number | 是 | 实付金额(分) |
| order_info.goods_list | - | Array | 是 | - |
| order_info.goods_list.name | 商品名称 | String | 是 | 门店名称 |
| order_info.goods_list.number | 1 | Number | 是 | 数量 |
| order_info.goods_list.price | 100 | Number | 是 | 价格 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "{\"pay_amount\":500,\"coupon\":100,\"distance\":1200,\"premium\":1000,\"weight\":0,\"overweight\":0,\"tips\":100,\"at_time\":1663344226,\"expect_time\":1663344226,\"order_no\":\"123434543454\",\"source_order_no\":\"DH3123123123123\",\"service_pkg\":\"xxx\",\"service_tag\":\"专人直送\"}",
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| pay_amount | 500 | Number | 运费（分） |
| coupon | 100 | Number | 优惠费用(分),如遇没有可传0 |
| distance | 1200 | Number | 配送距离（米） |
| premium | 100 | Number | 当前溢价(分),如遇没有可传0 |
| weight | 0 | Number | 重量（克） |
| overweight | 0 | Number | 超重（克） |
| tips | 100 | Number | 加费(分),如遇没有可传0 |
| at_time | 1663344226 | Number | 计费时间 |
| expect_time | 1663344226 | Number | 期望送达时间,如遇没有可传0 |
| order_no | 123434543454 | String | 三方运单号 |
| source_order_no | DH3123123123123 | String | 麦芽田订单号 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 添加小费接口(必接)

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-11-01 16:14:26

> 更新时间: 2024-12-18 15:01:22

**添加小费
添加小费接口,command值
  tips**

**接口状态**

> 已完成

**接口URL**

> tips

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=27a80e3

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "source_order_no":"123", //麦芽田侧运单号
    "tips": 100 //小费金额，累加方式（分）
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| source_order_no | 123 | String | 是 | 麦芽田侧运单号 |
| tips | 100 | Integer | 是 | 小费金额（分） |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | Integer | 状态码 |
| message | 成功 | String | - |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 取消配送(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-19 00:38:19

> 更新时间: 2025-01-14 10:20:58

**取消配送
取消配送接口,command值
cancel**

**接口状态**

> 已完成

**接口URL**

> cancel

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=241bc9a

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"source_order_no": "11667336008123456",
	"source_delivery_no": "11667336008123456",
	"cancel_reason_type": 1,
	"cancel_reason": "其他原因"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| source_order_no | 11667336008123456 | String | 是 | 麦芽田侧运单号 |
| source_delivery_no | 11667336008123456 | String | 是 | 三方配送单号 |
| cancel_reason_type | 1 | Integer | 是 | 取消类型（取消类型枚举详情见附录） |
| cancel_reason | 其他原因 | String | 是 | 取消原因 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "成功",
    "data": "{\"status\":1,\"error_code\":\"1111\",\"reason\":\"xxxx\"}"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| status | 1 | Number | 处理状态: 1-成功；0-失败 |
| error_code | 1111 | String | 状态码 |
| reason | xxxx | String | 催单原因 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 查询配送详情(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-19 00:46:15

> 更新时间: 2024-12-25 11:56:23

**获取配送详情
查询订单配送详情,command值
query_info**

**接口状态**

> 已完成

**接口URL**

> query_info

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=241bcb5

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"order_no": "12121212121212",
	"source_order_no": "11667336008123456",
	"mobile": "18291869390",
	"type": 1,
	"shipper_code": "dada"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| order_no | 12121212121212 | String | 是 | 订单号 |
| source_order_no | 11667336008123456 | String | 是 | 订单号 |
| mobile | 18291869390 | String | 是 | 手机号码 |
| type | 1 | Number | 是 | 付款类型 |
| shipper_code | dada | String | 是 | 配送方编码 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "{\"order_no\":\"orderNo1653708880866\",\"source_order_no\":\"1653708880866\",\"status\":\"PANDING\",\"status_name\":\"待接单\",\"pay_amount\":1240,\"coupon\":100,\"premium\":100,\"tips\":100,\"distance\":1004,\"create_time\":1665484632,\"accept_time\":1665484632,\"fetch_time\":1665484632,\"finish_time\":1665484632,\"cancel_time\":1665484632,\"rider_name\":\"林骑手\",\"rider_phone\":\"13888888888_1234\",\"longitude\":\"103.11111\",\"latitude\":\"90.1123123\",\"is_transship\":false}",
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| order_no | orderNo1653708880866 | String | 三方运单号 |
| source_order_no | 1653708880866 | String | 麦芽田订单号 |
| status | PANDING | String | 订单状态
未处理:UNPROGRESS
待确认:CREATED
已确认:CONFIRM
待抢单:DELIVERY
待取货:PICKUP
配送中:DELIVERING
已完成:DONE
已取消:CANCEL
已删除:DELETE
配送异常:EXPECT |
| status_name | 待接单 | String | 状态名称 |
| pay_amount | 1240 | Number | 运费（分） |
| coupon | 100 | Number | 优惠费用(分),如遇没有可传0 |
| premium | 100 | Number | 溢价(分),如遇没有可传0 |
| tips | 100 | Number | 加费(分),如遇没有可传0 |
| distance | 1004 | Number | 配送距离（米） |
| create_time | 1665484632 | Number | 下单时间（单位：unix 时间戳，精确到秒） |
| accept_time | 1665484632 | Number | 接单时间,如遇没有可传0 |
| fetch_time | 1665484632 | Number | 取货时间,如遇没有可传0 |
| finish_time | 1665484632 | Number | 完成时间,如遇没有可传0 |
| cancel_time | 1665484632 | Number | 取消时间,如遇没有可传0 |
| rider_name | 林骑手 | String | 骑手姓名 |
| rider_phone | 13888888888_1234 | String | 骑手电话 |
| longitude | 103.11111 | String | 经度(国测局02标准，如高德) |
| latitude | 90.1123123 | String | 纬度(国测局02标准，如高德) |
| is_transship | false | Boolean | 是否转单 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 获取骑手当前位置(必接)

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-09-19 01:03:41

> 更新时间: 2024-12-18 15:12:46

**查询骑手当前位置
查询骑手当前位置,command值
rider_location**

**接口状态**

> 已完成

**接口URL**

> rider_location

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=241bd37

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "order_no":"12121212121212",
    "source_order_no":"11667336008123456"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| order_no | 12121212121212 | String | 是 | 配送单号 |
| source_order_no | 11667336008123456 | String | 是 | 麦芽田侧运单号 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "{\"source_order_no\":\"23445455\",\"order_no\":\"4356745634\",\"rider_name\":\"林骑手\",\"rider_phone\":\"13888888888_1234\",\"longitude\":\"103.11111\",\"latitude\":\"90.1123123\",\"at_time\":1665463383}",
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| order_no | 4356745634 | String | 三方运单号 |
| source_order_no | 23445455 | String | 麦芽田订单号 |
| rider_name | 林骑手 | String | 骑手姓名 |
| rider_phone | 13888888888_1234 | String | 骑手电话 |
| longitude | 103.11111 | String | 经度(国测局02标准，如高德) |
| latitude | 90.1123123 | String | 纬度(国测局02标准，如高德) |
| at_time | 1665463383 | Number | 产生时间 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 批量获取骑手当前位置(必接)

> 创建人: Anthony

> 更新人: Anthony

> 创建时间: 2022-10-28 17:52:23

> 更新时间: 2024-12-18 15:18:57

**查询骑手当前位置
批量查询骑手当前位置,command值
multi_rider_locations**

**接口状态**

> 已完成

**接口URL**

> multi_rider_locations

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=27621f7

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"orders": [
		{
			"order_no": "12121212121212",
			"source_order_no": "11667336008123456"
		}
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| orders | - | Object | 是 | - |
| orders.order_no | 12121212121212 | String | 是 | - |
| orders.source_order_no | 11667336008123456 | String | 是 | 麦芽田侧运单号 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "data": "[{\"source_order_no\":\"23445455\",\"order_no\":\"4356745634\",\"rider_name\":\"林骑手\",\"rider_phone\":\"13888888888_1234\",\"longitude\":\"103.11111\",\"latitude\":\"90.1123123\",\"at_time\":1665463383}]",
    "message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Array | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| source_order_no | 23445455 | String | 麦芽田订单号 |
| order_no | 4356745634 | String | 三方运单号 |
| rider_name | 林骑手 | String | 骑手姓名 |
| rider_phone | 13888888888_1234 | String | 骑手电话 |
| longitude | 103.11111 | String | 经度(国测局02标准，如高德) |
| latitude | 90.1123123 | String | 纬度(国测局02标准，如高德) |
| at_time | 1665463383 | Number | 计费时间 |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 授权解绑(必接)

> 创建人: Anthony

> 更新人: Anthony

> 创建时间: 2022-11-04 17:48:14

> 更新时间: 2024-10-29 15:43:01

**简要描述**


 - 当用户解绑掉授权时，调用此接口通知。

**command值**


 - token_unbind

**接口状态**

> 已完成

**接口URL**

> token_unbind

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=280afc8

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1",
	"token":"xxxxxx"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1 | String | 是 | 平台方渠道ID |
| token | xxxxxx | String | 是 | 麦芽田商户token |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | Number | 状态码 |
| msg | ok | String | 返回文字描述 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 查询当前账号余额(必接)

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-12-08 19:33:59

> 更新时间: 2024-12-19 15:07:16

**简要描述**


 - 查询余额。

**command值**


 - balance

**接口状态**

> 已完成

**接口URL**

> balance

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=2b10fa9

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1" //三方平台账号ID
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1 | String | 是 | 平台方渠道ID |
| token | xxxxxx | String | 是 | 麦芽田商户token |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok",
    "data": "{\"balance\":500,\"at_time\":1663344226}"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| balance | 500 | Number | 余额金额（分） |
| at_time | 1663344226 | Number | 时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 在线充值链接(必接)

> 创建人: Sjq

> 更新人: Anthony

> 创建时间: 2022-12-08 19:43:36

> 更新时间: 2024-12-19 15:07:20

**简要描述**


 - 获取授权链接。

**command值**


 - recharge_url

**接口状态**

> 已完成

**接口URL**

> recharge_url

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=2b11167

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1" //三方平台账号ID
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1 | String | 是 | 平台方渠道ID |
| token | xxxxxx | String | 是 | 麦芽田商户token |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": 200,
    "message": "ok",
    "data": "{\"recharge_url\":\"xxxxxxxxx\",\"at_time\":1663344226}"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| recharge_url | xxxxxxxxx | String | 三方充值链接 |
| at_time | 1663344226 | Number | 计费时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 预计费接口(废弃)

> 创建人: 小胖

> 更新人: 小胖

> 创建时间: 2024-01-12 09:55:38

> 更新时间: 2025-01-08 14:56:03

**预计费
配送渠道未授权场景下的计费接口,command值
pre_valuating**

**接口状态**

> 已废弃

**接口URL**

> pre_valuating(废弃)

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=4792c71

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"sender": {
		"shop_id": "adfa111",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"receiver": {
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"order_info": {
		"category": "xiaochi",
		"weight": 1
	}
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| sender | - | Object | 是 | - |
| sender.shop_id | adfa111 | String | 是 | 店铺id |
| sender.longitude | 103.11111 | String | 是 | 门店经度(国测局02标准，如高德) |
| sender.latitude | 90.1123123 | String | 是 | 门店纬度(国测局02标准，如高德) |
| sender.city_code | 110100 | String | 是 | 城市编码 |
| receiver | - | Object | 是 | - |
| receiver.longitude | 103.11111 | String | 是 | 顾客经度(国测局02标准，如高德) |
| receiver.latitude | 90.1123123 | String | 是 | 顾客纬度(国测局02标准，如高德) |
| receiver.city_code | 110100 | String | 是 | 城市编码 |
| order_info | - | Object | 是 | - |
| order_info.category | xiaochi | String | 是 | 订单类型 |
| order_info.weight | 1 | Integer | 是 | 商品重量（单位：千克） |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "{\"pay_amount\":500,\"coupon\":100,\"distance\":1200,\"premium\":1000,\"weight\":0,\"overweight\":0,\"tips\":100,\"at_time\":1663344226,\"expect_time\":1663344226,\"order_no\":\"123434543454\",\"source_order_no\":\"DH3123123123123\",\"service_pkg\":\"xxx\",\"service_tag\":\"专人直送\"}",
	"message": "成功"
}
```

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 快递订阅轨迹接口

> 创建人: zhg399213463

> 更新人: Anthony

> 创建时间: 2024-01-15 14:22:50

> 更新时间: 2024-12-18 15:24:06

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> subscribe

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"order_no": "12121212121212",
	"source_order_no": "11667336008123456",
	"mobile": "18291869390",
	"type": 1,
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| order_no | 12121212121212 | String | 是 | 三方平台订单号 |
| source_order_no | 11667336008123456 | String | 是 | 发单侧订单号 |
| mobile | 18291869390 | String | 是 | 联系人电话 |
| type | 1 | Integer | 是 | 快递单类型 1-非地图2-地图 同城配送忽略该字段 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "{\"source_order_no\":\"DPK364322890411\",\"logistics_code\":\"DBL\",\"update_time\":\"2024-01-15 14:20:38\",\"success\":true,\"reason\":\"\"}",
	"message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| source_order_no | DPK364322890411 | String | 麦芽田订单号 |
| logistics_code | DBL | String | 快递公司码 |
| update_time | 2024-01-15 14:20:38 | String | 更新时间 |
| success | true | Boolean | 是否成功 |
| reason | xxxx | String | 失败原因 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 预注册门店接口

> 创建人: Anthony

> 更新人: Anthony

> 创建时间: 2024-04-08 12:29:06

> 更新时间: 2024-12-18 15:27:02

**预计费
配送渠道未授权场景下的计费接口,command值
pre_valuating**

**接口状态**

> 开发中

**接口URL**

> pre_built_shop

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=4d7f4a9

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "外部门店编号",
	"name": "店铺名称",
	"province": "陕西省",
	"province_code": 123,
	"city": "西安市",
	"city_code": 456,
	"district": "莲湖区",
	"district_code": 789,
	"address": "xxxxxxxxx",
	"address_detail": "xxxxxxxxx",
	"longitude": "19.221211",
	"latitude": "123.123123123",
	"contact": "韩先生",
	"mobile": "13312312312",
	"open_time": "09:00",
	"close_time": "20:00"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 外部门店编号 | String | 是 | 平台方渠道ID |
| name | 店铺名称 | String | 是 | 门店名称 |
| province | 陕西省 | String | 是 | 省 |
| province_code | 123 | Integer | 是 | - |
| city | 西安市 | String | 是 | 市 |
| city_code | 456 | Integer | 是 | - |
| district | 莲湖区 | String | 是 | 区 |
| district_code | 789 | Integer | 是 | - |
| address | xxxxxxxxx | String | 是 | 客户详细地址 |
| address_detail | xxxxxxxxx | String | 是 | - |
| longitude | 19.221211 | String | 是 | 经度(国测局02标准，如高德) |
| latitude | 123.123123123 | String | 是 | 纬度(国测局02标准，如高德) |
| contact | 韩先生 | String | 是 | - |
| mobile | 13312312312 | String | 是 | - |
| opening_time | 09:00 | String | 是 | - |
| closeing_time | 20:00 | String | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "{\"store_id\":\"123123\"}",
	"message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Object | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| store_id | 123123 | String | 门店ID |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 一取多投计费接口

> 创建人: 小胖

> 更新人: Anthony

> 创建时间: 2024-04-09 16:26:01

> 更新时间: 2024-12-17 11:22:36

**查询订单运费
查询订单运费接口,command值
valuating**

**接口状态**

> 开发中

**接口URL**

> multi_valuating

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=4da15f0

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1000",
	"is_pre_order": false,
	"delay_delivery_time": 1665487116,
	"sender": {
		"name": "发货门店名称",
		"phone": "发货门店电话",
		"address": "发货门店地址",
		"address_detail": "发货门店地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"order_list": [
		{
			"receiver": {
				"name": "收货人",
				"phone": "收货人电话",
				"address": "收货人地址",
				"address_detail": "收货人地址详情",
				"longitude": "103.11111",
				"latitude": "90.1123123",
				"province_code": "110100",
				"city_code": "110100",
				"district_code": "110100"
			},
			"order_info": {
				"source_order_no": "11667336008123456",
				"sn": 101,
				"source": "meituan",
				"channel_tag": "meituan",
				"channel_name": "渠道名称",
				"source_no": "1667336008123456",
				"category": "xiaochi",
				"weight": 1,
				"total_fee": 1300,
				"paid_fee": 1200,
				"remark": "备注",
				"is_from_door":1,
				"is_to_door":1,
				"goods_list": [
					{
						"name": "商品名称",
						"number": 1,
						"price": 100
					}
				]
			}
		}
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1000 | String | 是 | 三方平台账号ID |
| is_pre_order | false | Boolean | 是 | 是否是预约单 0:非预约单；1:预约单 |
| delay_delivery_time | 1665487116 | Integer | 是 | 预约送达时间 |
| expect_finish_time | 1665490716 | Integer | 是 | 期望送达时间 |
| sender | - | Object | 是 | - |
| sender.name | 发货门店名称 | String | 是 | 门店名称 |
| sender.phone | 发货门店电话 | String | 是 | 门店电话 |
| sender.address | 发货门店地址 | String | 是 | 门店详细地址 |
| sender.address_detail | 发货门店地址详情 | String | 是 | - |
| sender.longitude | 103.11111 | String | 是 | 门店经度(国测局02标准，如高德) |
| sender.latitude | 90.1123123 | String | 是 | 门店纬度(国测局02标准，如高德) |
| sender.city_code | 110100 | String | 是 | 城市编码 |
| order_list | - | Array | 是 | - |
| order_list.receiver | - | Object | 是 | - |
| order_list.receiver.name | 收货人 | String | 是 | 门店名称 |
| order_list.receiver.phone | 收货人电话 | String | 是 | 客户电话 |
| order_list.receiver.address | 收货人地址 | String | 是 | 客户详细地址 |
| order_list.receiver.address_detail | 收货人地址详情 | String | 是 | - |
| order_list.receiver.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| order_list.receiver.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| order_list.receiver.province_code | 110100 | String | 是 | - |
| order_list.receiver.city_code | 110100 | String | 是 | - |
| order_list.receiver.district_code | 110100 | String | 是 | - |
| order_list.order_info | - | Object | 是 | - |
| order_list.order_info.source_order_no | 11667336008123456 | String | 是 | 麦芽田单号 |
| order_list.order_info.sn | 101 | Integer | 是 | 流水号 |
| order_list.order_info.source | meituan | String | 是 | - |
| order_list.order_info.channel_tag | meituan | String | 是 | - |
| order_list.order_info.channel_name | 渠道名称 | String | 是 | - |
| order_list.order_info.source_no | 1667336008123456 | String | 是 | - |
| order_list.order_info.category | xiaochi | String | 是 | 订单分类(麦芽田枚举) |
| order_list.order_info.weight | 1 | Integer | 是 | - |
| order_list.order_info.total_fee | 1300 | Integer | 是 | 订单原价总金额 |
| order_list.order_info.paid_fee | 1200 | Integer | 是 | - |
| order_list.order_info.remark | 备注 | String | 是 | - |
| order_list.order_info.is_from_door | 1 | Integer | 是 | - |
| order_list.order_info.is_to_door | 1 | Integer | 是 | - |
| order_list.order_info.goods_list | - | Array | 是 | - |
| order_list.order_info.goods_list.name | 商品名称 | String | 是 | 门店名称 |
| order_list.order_info.goods_list.number | 1 | Integer | 是 | 数量 |
| order_list.order_info.goods_list.price | 100 | Integer | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "{\"order_no\":\"ddks12345678\",\"pay_amount\":900,\"coupon\":200,\"distance\":1900,\"premium\":200,\"expect_time\":1663344226,\"items\":[{\"order_no\":\"sub1\",\"source_order_no\":\"maiyatian_sub1\",\"pay_amount\":500,\"coupon\":100,\"distance\":1200,\"premium\":100},{\"order_no\":\"sub2\",\"source_order_no\":\"maiyatian_sub2\",\"pay_amount\":400,\"coupon\":100,\"distance\":700,\"premium\":100}]}",
	"message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | Integer | 状态码 |
| data | {"order_no":"ddks12345678","pay_amount":900,"coupon":200,"distance":1900,"premium":200,"expect_time":1663344226,"items":[{"order_no":"sub1","source_order_no":"maiyatian_sub1","pay_amount":500,"coupon":100,"distance":1200,"premium":100},{"order_no":"sub2","source_order_no":"maiyatian_sub2","pay_amount":400,"coupon":100,"distance":700,"premium":100}]} | String | 业务参数，序列化为JSON格式的字符串。
order_no：平台方父运单号
distance：配送总距离（米）
pay_amount：实际支付总金额，包含下单小费金额（分）
coupon：优惠总金额（分）
premium：溢价总金额（分）
expect_time：预计送达时间
items.order_no:平台子运单号,订单履约单号
items.source_order_no:麦芽田子运单号
items.distance：配送距离（米）
items.pay_amount：实际支付金额，包含下单小费金额（分）
items.coupon：优惠金额（分）
items.premium：溢价金额（分） |
| message | 成功 | String | - |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 一取多投下单接口

> 创建人: 小胖

> 更新人: Anthony

> 创建时间: 2024-04-08 12:40:01

> 更新时间: 2024-12-17 11:22:40

**配送下单
配送下单接口,command值
  send**

**接口状态**

> 开发中

**接口URL**

> multi_send

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**Mock URL**

> /?apipost_id=4d7f53f

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1000",
	"is_pre_order": false,
	"delay_delivery_time": 1665487116,
	"sender": {
		"name": "发货门店名称",
		"phone": "发货门店电话",
		"address": "发货门店地址",
		"address_detail": "发货门店地址详情",
		"longitude": "103.11111",
		"latitude": "90.1123123",
		"city_code": "110100"
	},
	"order_list": [
		{
			"receiver": {
				"name": "收货人",
				"phone": "收货人电话",
				"address": "收货人地址",
				"address_detail": "收货人地址详情",
				"longitude": "103.11111",
				"latitude": "90.1123123",
				"province_code": "110100",
				"city_code": "110100",
				"district_code": "110100"
			},
			"order_info": {
				"source_order_no": "11667336008123456",
				"sn": 101,
				"source": "meituan",
				"channel_tag": "meituan",
				"channel_name": "渠道名称",
				"source_no": "1667336008123456",
				"category": "xiaochi",
				"weight": 1,
				"total_fee": 1300,
				"paid_fee": 1200,
				"remark": "备注",
				"is_from_door":1,
				"is_to_door":1,
				"goods_list": [
					{
						"name": "商品名称",
						"number": 1,
						"price": 100
					}
				]
			}
		}
	]
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| shop_id | 1000 | String | 是 | 三方平台账号ID |
| is_pre_order | false | Boolean | 是 | 是否是预约单 false:非预约单；true:预约单 |
| delay_delivery_time | 1665487116 | Integer | 是 | 预约送达时间 |
| sender | - | Object | 是 | - |
| sender.name | 发货门店名称 | String | 是 | 门店名称 |
| sender.phone | 发货门店电话 | String | 是 | 门店电话 |
| sender.address | 发货门店地址 | String | 是 | 门店详细地址 |
| sender.address_detail | 发货门店地址详情 | String | 是 | - |
| sender.longitude | 103.11111 | String | 是 | 门店经度(国测局02标准，如高德) |
| sender.latitude | 90.1123123 | String | 是 | 门店纬度(国测局02标准，如高德) |
| sender.city_code | 110100 | String | 是 | 城市编码 |
| order_list | - | Array | 是 | - |
| order_list.receiver | - | Object | 是 | - |
| order_list.receiver.name | 收货人 | String | 是 | 门店名称 |
| order_list.receiver.phone | 收货人电话 | String | 是 | 客户电话 |
| order_list.receiver.address | 收货人地址 | String | 是 | 客户详细地址 |
| order_list.receiver.address_detail | 收货人地址详情 | String | 是 | - |
| order_list.receiver.longitude | 103.11111 | String | 是 | 经度(国测局02标准，如高德) |
| order_list.receiver.latitude | 90.1123123 | String | 是 | 纬度(国测局02标准，如高德) |
| order_list.receiver.province_code | 110100 | String | 是 | - |
| order_list.receiver.city_code | 110100 | String | 是 | - |
| order_list.receiver.district_code | 110100 | String | 是 | - |
| order_list.order_info | - | Object | 是 | - |
| order_list.order_info.source_order_no | 11667336008123456 | String | 是 | 麦芽田单号 |
| order_list.order_info.sn | 101 | Integer | 是 | - |
| order_list.order_info.source | meituan | String | 是 | - |
| order_list.order_info.channel_tag | meituan | String | 是 | - |
| order_list.order_info.channel_name | 渠道名称 | String | 是 | - |
| order_list.order_info.source_no | 1667336008123456 | String | 是 | - |
| order_list.order_info.category | xiaochi | String | 是 | 订单分类(麦芽田枚举) |
| order_list.order_info.weight | 1 | Integer | 是 | - |
| order_list.order_info.total_fee | 1300 | Integer | 是 | 订单原价总金额 |
| order_list.order_info.paid_fee | 1200 | Integer | 是 | - |
| order_list.order_info.remark | 备注 | String | 是 | - |
| order_list.order_info.is_from_door | 1 | Integer | 是 | - |
| order_list.order_info.is_to_door | 1 | Integer | 是 | - |
| order_list.order_info.goods_list | - | Array | 是 | - |
| order_list.order_info.goods_list.name | 商品名称 | String | 是 | 门店名称 |
| order_list.order_info.goods_list.number | 1 | Integer | 是 | 数量 |
| order_list.order_info.goods_list.price | 100 | Integer | 是 | - |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "{\"order_no\":\"ddks12345678\",\"pay_amount\":900,\"coupon\":200,\"distance\":1900,\"premium\":200,\"expect_time\":1663344226,\"items\":[{\"order_no\":\"sub1\",\"source_order_no\":\"maiyatian_sub1\",\"pay_amount\":500,\"coupon\":100,\"distance\":1200,\"premium\":100},{\"order_no\":\"sub2\",\"source_order_no\":\"maiyatian_sub2\",\"pay_amount\":400,\"coupon\":100,\"distance\":700,\"premium\":100}]}",
	"message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | Integer | 状态码 |
| data | {"order_no":"ddks12345678","pay_amount":900,"coupon":200,"distance":1900,"premium":200,"expect_time":1663344226,"items":[{"order_no":"sub1","pay_amount":500,"coupon":100,"distance":1200,"premium":100},{"order_no":"sub2","pay_amount":400,"coupon":100,"distance":700,"premium":100}]} | String | 业务参数，序列化为JSON格式的字符串。
order_no：平台方父运单号
distance：配送总距离（米）
pay_amount：实际支付总金额，包含下单小费金额（分）
coupon：优惠总金额（分）
premium：溢价总金额（分）
expect_time：预计送达时间
items.order_no:平台子运单号,订单履约单号
items.source_order_no:麦芽田子运单号
items.distance：配送距离（米）
items.pay_amount：实际支付金额，包含下单小费金额（分）
items.coupon：优惠金额（分）
items.premium：溢价金额（分） |
| message | 成功 | String | - |

* 失败(201)

```javascript
暂无数据
```

**Query**

#### 获取服务包列表

> 创建人: 小胖

> 更新人: Anthony

> 创建时间: 2024-11-05 16:15:23

> 更新时间: 2024-12-18 15:26:15

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> service_pkg_list

| 环境  | URL |
| --- | --- |
| 默认环境 | http://127.0.0.1:9999/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
	"shop_id": "1", //三方平台账号ID
	"city": "10100" //城市code
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"data": "[{\"name\":\"滴滴跑腿直送\",\"service_pkg\":\"ddzs\"},{\"name\":\"滴滴跑腿特惠\",\"service_pkg\":\"ddpt\"}]",
	"message": "成功"
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | Array | 业务参数，序列化为JSON格式的字符串。其中具体字段在后续每个API给出 |
| name | 滴滴跑腿直送 | String | 门店名称 |
| service_pkg | ddzs | String | 服务包内容 |

* 失败(404)

```javascript
暂无数据
```

**Query**

### 附录

> 创建人: Bobo

> 更新人: Anthony

> 创建时间: 2022-10-11 15:21:37

> 更新时间: 2024-08-01 11:59:22


 - 订单品类 order_info.category

| 枚举名 | 枚举值 |
| --- | --- |
| 小吃美食 | xiaochi |
| 正餐快餐 | canyin |
| 龙虾烧烤 | shaokao |
| 烘焙蛋糕 | dangao |
| 甜品奶茶 | tianpin |
| 西餐料理 | liaoli |
| 火锅串串 | huoguo |
| 浪漫鲜花 | xianhua |
| 生鲜果蔬 | shuiguo |
| 酒水零售 | yinpin |
| 超市百货 | chaoshi |
| 医药成人 | chengren |


 - 订单来源 order_info.source

| 枚举名 | 枚举值 |
| --- | --- |
| 美团 | meituan |
| 饿了么 | eleme |
| 京东到家 | daojia |
| 有赞 | youzan |
| 微盟 | weimob |
| 微店 | weidian |
| 抖音 | douyin |
| 其他 | other |


 - 配送回调状态- status

| 枚举名 | 枚举值 |
| --- | --- |
| 未分配骑手 | PENDING |
| 已分配骑手 | GRABBED |
| 已到店 | ATSHOP |
| 已揽收 | COLLECTED |
| 已取货 | PICKUP |
| 已到站点 | ONSTATION |
| 配送中 | DELIVERING |
| 已完单 | DONE |
| 配送取消 | CANCEL |
| 配送退回 | BACK |
| 配送转单 | TRANSFER |
| 骑手取消 | RIDER_CANCEL |
| 订单创建失败 | CREATE_FAILED |


 - 取消类型枚举- cancel

| 取消类型 | 取消说明 |
| ---- | ---- |
| 1000 | 订单信息填写错误 |
| 1001 | 已用其他发单工具配送 |
| 1002 | 没有骑手接单 |
| 1005 | 配送方系统超时 |
| 1007 | 订单取消 |
| 1008 | 其他原因 |

**错误码- code**

| 错误码 | 错误说明 |
| --- | ---- |
| 400 | 数据错误 |
| 401 | token、签名错误 |
| 429 | 接口限流 |

**Query**

### 常见问题

> 创建人: Anthony

> 更新人: Anthony

> 创建时间: 2024-12-19 11:48:20

> 更新时间: 2024-12-19 11:48:41

##### 请求接口参数示例

![image.png](https://img.cdn.apipost.cn/client/user/252281/avatar/78805a221a988e79ef3f42d7c5bfd41864b761db00b1e.png "")

**url后拼接command**

##### 请求接口后响应:'{"code":401,"message":"接口未授权","data":""}'


 - 使用签名工具对请求的参数进行签名生成，比对签名签的字符串是否和自己代码中生成的一致。
 - 比对自己生成的签名和工具生成的签名是否一致。
 - 签名工具：https://open-api-test.maiyatian.com/test/

##### 请求接口后响应:'{"code":500}'参数类型问题报错。


 - 根据文档严格比对每一个参数

##### access_token 接口返回的expire_time 0是什么意思？


 - expire_time 是过期时间，0表示永久不过期，大于0表示过期时间（单位秒）

##### 如何快速得到麦芽田技术解答？


 - 收集所有问题，统一进行提问，麦芽田技术统一回复。
 - 开发中遇到问题，提问标准格式
 - 问题描述：xxxxx
 - 接口地址：xxxxx
 - 请求报文：xxxxx
 - 返回报文：xxxxx

**返回报文：xxxxx**

**Query**
