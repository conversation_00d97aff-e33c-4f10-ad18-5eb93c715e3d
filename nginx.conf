server {
    listen 80;
    server_name running-man.com;

    root /var/www/running-man/public;
    index index.php index.html index.htm;

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    # 添加静态文件的缓存配置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri $uri/ =404;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;  # 根据您的 Docker 服务名进行调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;  # 假设此文件存在且可用
    }

    location ~ /\.ht {
        deny all;
    }
}
