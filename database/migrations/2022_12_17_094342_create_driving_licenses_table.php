<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('driving_licenses', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户');
            $table->string('cover')->comment('行驶证正面');
            $table->string('obverse')->comment('行驶证反面');
            $table->string('name')->comment('车辆所哟人');
            $table->string('car_no')->comment('车牌号');
            $table->string('type')->comment('车辆类型');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('driving_licenses');
    }
};
