<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('merchant_tokens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('merchant_id')->comment('商户ID');
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->string('platform', 20)->default('maiyatian')->comment('平台：maiyatian,其他平台...');
            $table->string('access_token')->comment('访问令牌');
            $table->string('refresh_token')->comment('刷新令牌');
            $table->timestamp('expire_time')->nullable()->comment('访问令牌过期时间');
            $table->timestamp('refresh_expire_time')->nullable()->comment('刷新令牌过期时间');
            $table->string('shop_id', 50)->nullable()->comment('平台店铺ID');
            $table->json('extra_data')->nullable()->comment('额外数据，用于存储平台特定信息');
            $table->timestamps();
            
            // 联合唯一索引：确保同一商户的同一用户在同一平台只有一条记录
            $table->unique(['merchant_id', 'user_id', 'platform'], 'merchant_user_platform_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('merchant_tokens');
    }
};
