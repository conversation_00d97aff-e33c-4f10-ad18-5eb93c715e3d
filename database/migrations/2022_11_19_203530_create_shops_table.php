<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shops', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('community_id')->default(0)->index()->comment('社区ID');
            $table->string('name')->comment('店铺名称');
            $table->string("promotion_info", 500)->nullable()->default("")->comment("公告");
            $table->string('province')->comment('省');
            $table->string('city')->comment('市');
            $table->string('district')->comment('区');
            $table->string('address_detail')->comment('详细地址');
            $table->decimal("lng", 9, 6)->default(0)->comment("经度");
            $table->decimal("lat", 9, 6)->default(0)->comment("纬度");
            $table->string('logo', 500)->nullable()->comment("logo");
            $table->string('cover', 500)->nullable()->comment("头图");
            $table->string('tel')->nullable()->default("")->comment('电话');
            $table->unsignedInteger("sort")->default(0)->comment("排序，数字越小越靠前");
            $table->tinyInteger('status')->default(0)->comment('状态：0-待上架，1-上架，-1-下架');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shops');
    }
};
