<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pricings', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedTinyInteger("type")->default(0)->comment("类型：1-用户，2-骑手");
            $table->string("title")->comment("名称");
            $table->boolean('is_special')->default(false)->comment("是否专人、全职");
            $table->unsignedBigInteger('site_id')->default(0)->comment('站点');
            $table->decimal("base_price")->default(0)->comment("基础单费，单位元");
            $table->text("distance_price")->nullable()->comment('距离收费[{"egt":2,"base":2,"unit":1},{"egt":4,"base":4,"unit":2}]');
            $table->text("period_price")->nullable()->comment('特殊时段收费[{"from":"00:00","ft":0,"to":"07:00","tt":0,"price": 5}]');
            $table->decimal("weather_price")->default(0)->comment("天气收费，单位元");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pricings');
    }
};
