<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('advertisements', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string("title")->comment("标题");
            $table->string("cover")->comment("图片");
            $table->unsignedTinyInteger("type")->default(0)->comment("类型：1-轮播，2-弹窗");
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->unsignedTinyInteger('target_type')->default(0)->comment('跳转类型：1--h5, 2--app');
            $table->text('target')->nullable()->comment('json格式，跳转信息');
            $table->unsignedInteger("sort")->default(0)->comment("排序，数字越小越靠前");
            $table->tinyInteger('status')->default(0)->comment('状态：0-待上架，1-上架，-1-下架');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('advertisements');
    }
};
