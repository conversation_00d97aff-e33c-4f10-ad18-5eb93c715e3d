<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity_rules', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('activity_id')->index()->comment('邀请活动ID');
            $table->integer('start')->comment('起始');
            $table->integer('end')->comment('结束');
            $table->boolean('order_count')->default(1)->comment('订单数');
            $table->decimal('reward')->default(0)->comment('奖励金额');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invite_activity_rules');
    }
};
