<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string("title")->comment("优惠券名称");
            $table->unsignedTinyInteger("type")->default(0)->comment("类型：1-满减券，2-折扣券");
            $table->decimal("discount_price")->default(0)->comment("优惠金额/折扣，20元/80%");
            $table->decimal("start_price")->default(0)->comment("门槛金额，单位元");
            $table->string("validity")->nullable()->comment('有效期:{"type":1,"day":"30","start":"","end":""}');
            $table->text("rules")->nullable()->comment('规则');
            $table->unsignedInteger("stock")->default(0)->comment("库存");
            $table->unsignedInteger("sales")->default(0)->comment("销量");
            $table->tinyInteger("status")->default(0)->comment("状态：0-待上架，1-上架，-1-下架");
            $table->unsignedInteger("sort")->default(0)->comment("排序，数字越小越靠前");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupons');
    }
};
