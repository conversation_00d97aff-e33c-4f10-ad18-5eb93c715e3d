<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sites', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('站点名称');
            $table->string('province')->comment('省');
            $table->string('city')->comment('市');
            $table->string('district')->comment('区');
            $table->string('address')->comment('详细地址');
            $table->string('contact_name')->comment('联系人');
            $table->string('phone')->comment('手机号');
            $table->unsignedInteger('farthest')->default(0)->comment('最远配送距离，单位：米');
            $table->decimal('latitude', 9, 6)->default(0)->comment('纬度');
            $table->decimal('longitude', 9, 6)->default(0)->comment('经度');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sites');
    }
};
