<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_pay_recs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('帮我*订单ID');
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('order_no', 32)->unique()->comment('单号');
            $table->tinyInteger("type")->default(0)->comment("类型：1-加小费，2-打赏，3-支付商品费");
            $table->integer("amount")->default(0)->comment("金额，分");
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->integer('pay_method')->default(0)->comment('支付方式 1--支付宝 2--微信 3-云闪付 4-余额支付');
            $table->string('payment_no')->nullable()->comment('支付流水号');
            $table->string('refund_no')->nullable()->comment('退款流水');
            $table->timestamp('refund_at')->nullable()->comment('退款时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_pay_recs');
    }
};
