<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('riders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->default(0)->comment('用户ID');
            $table->string('name')->default("")->comment('姓名');
            $table->tinyInteger('status')->default(0)->comment('0--下线 1--');
            $table->string('phone')->comment('手机号')->unique();
            $table->string('avatar')->nullable()->comment('头像');
            $table->bigInteger('parent_id')->default(0)->comment('上级');
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->tinyInteger('level')->default(1)->comment('等级');
            $table->integer('score')->default(0)->comment('当前等级分');
            $table->tinyInteger('verified')->default(0)->comment('实名认证 0--未上传 1--审核中 2--审核通过 3--审核失败');
            $table->tinyInteger('health_status')->default(0)->comment('健康证 0--未上传 1--审核中 2--审核通过 3--审核失败');
            $table->tinyInteger('transport_status')->default(0)->comment('交通工具 0--未上传 1--审核中 2--审核通过 3--审核失败');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('riders');
    }
};
