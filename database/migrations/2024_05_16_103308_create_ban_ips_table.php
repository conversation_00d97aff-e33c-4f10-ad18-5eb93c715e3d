<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ban_ips', function (Blueprint $table) {
            $table->id();
            $table->string('ip', 15)->unique()->comment('IP地址');
            $table->string('reason', 255)->nullable()->comment('封禁原因');
            $table->timestamp('expired_at')->nullable()->comment('解封时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ban_ips');
    }
};
