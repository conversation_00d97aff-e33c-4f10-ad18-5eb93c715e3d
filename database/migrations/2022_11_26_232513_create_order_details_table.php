<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_details', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('订单ID');
            $table->bigInteger('spu_id')->index()->comment('商品ID');
            $table->string('spu_name')->comment('商品名称');
            $table->string('spu_cover', 500)->comment("商品头图");
            $table->integer('price')->comment('原价 单位分');
            $table->integer('discount_price')->comment('优惠价 单位分');
            $table->integer('quantity')->comment('购买数量');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_details');
    }
};
