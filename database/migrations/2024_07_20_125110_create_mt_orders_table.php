<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mt_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_sn')->index()->comment('订单号');
            $table->integer('distance')->comment('距离');
            $table->string('rider_name')->comment('骑手名称');
            $table->timestamp('create_time')->nullable()->comment('下单时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mt_orders');
    }
};
