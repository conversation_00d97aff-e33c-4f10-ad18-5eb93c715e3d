<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_rights', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('权益名称');
            $table->string('icon')->comment('权益logo');
            $table->string('remark')->comment('权益说明');
            $table->string('level')->comment('所属等级');
            $table->integer('view_order');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_rights');
    }
};
