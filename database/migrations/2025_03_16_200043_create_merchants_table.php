<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('merchants', function (Blueprint $table) {
            $table->id();
            
            // 基础信息字段
            $table->string('shop_name', 100)->comment('商家名称');
            $table->string('phone', 20)->unique()->comment('商家手机号');
            $table->string('password')->comment('登录密码');
            $table->string('city', 50)->comment('城市');
            $table->string('city_code', 20)->comment('城市编号');
            $table->string('address', 255)->comment('详细地址');
            $table->string('contact_name', 50)->nullable()->comment('联系人姓名');
            $table->string('email', 100)->nullable()->comment('电子邮箱');
            
            // 商家详细信息
            $table->string('merchant_type', 50)->nullable()->comment('商家类型/分类');
            $table->string('license_number', 50)->nullable()->comment('营业执照号码');
            
            // 财务信息
            $table->decimal('balance', 10, 2)->default(0)->comment('账户余额');
            
            // 状态和审核信息
            $table->tinyInteger('status')->default(0)->comment('状态: 0=待审核, 1=已审核通过, 2=审核拒绝, 3=暂停服务, 4=账号禁用');
            $table->timestamp('approved_at')->nullable()->comment('审核通过时间');
            $table->unsignedBigInteger('approved_by')->nullable()->comment('审核人ID');
            $table->string('approval_remarks', 255)->nullable()->comment('审核备注');
            
            // 系统管理字段
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            
            // 时间戳
            $table->timestamps();
            $table->softDeletes()->comment('软删除');
            
            // 索引
            $table->index('shop_name');
            $table->index('city');
            $table->index('city_code');
            $table->index('status');
            $table->index('merchant_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('merchants');
    }
};
