<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->tinyInteger('sex')->nullable()->default(0)->comment('性别 0--未知 1-男 2-女');
            $table->string('constellation')->nullable()->default('')->comment('星座');
            $table->string('shu_xiang')->nullable()->default('')->comment('属相');
            $table->string('tag')->nullable()->comment('用户标签');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_profiles');
    }
};
