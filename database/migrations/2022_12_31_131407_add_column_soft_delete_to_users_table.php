<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('log_out_sq')->default(0)->comment('注销状态  0--正常 1--注销');
            $table->boolean('log_out_pt')->default(0)->comment('注销状态  0--正常 1--注销');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('log_out_sq');
            $table->dropColumn('log_out_pt');
        });
    }
};
