<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_remind_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('帮我*订单ID');
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('rider_id')->index()->comment('骑手ID');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_remind_logs');
    }
};
