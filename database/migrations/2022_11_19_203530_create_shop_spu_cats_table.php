<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_spu_cats', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('shop_id')->index()->comment('店铺ID');
            $table->string('name', 50)->comment('名称');
            $table->string('cover', 500)->nullable()->comment("图片");
            $table->bigInteger('pid')->nullable()->default(0)->index()->comment('父类目ID');
            $table->unsignedInteger("sort")->default(0)->comment("排序，数字越小越靠前");
            $table->tinyInteger('status')->default(0)->comment('状态：0-待上架，1-上架，-1-下架');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_spu_cats');
    }
};
