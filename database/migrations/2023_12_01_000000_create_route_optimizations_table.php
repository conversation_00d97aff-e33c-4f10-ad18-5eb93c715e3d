<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRouteOptimizationsTable extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        Schema::create('route_optimizations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('rider_id')->comment('骑手ID');
            $table->integer('total_distance')->default(0)->comment('总距离(米)');
            $table->integer('estimated_time')->default(0)->comment('预计时间(秒)');
            $table->integer('time_savings')->default(0)->comment('节省时间(秒)');
            $table->integer('distance_savings')->default(0)->comment('节省距离(米)');
            $table->integer('percentage_time')->default(0)->comment('节省时间百分比');
            $table->integer('percentage_distance')->default(0)->comment('节省距离百分比');
            $table->json('route_data')->comment('路径数据');
            $table->json('waypoints_data')->comment('路径点数据');
            $table->string('status', 20)->default('pending')->comment('状态：pending待确认,accepted已接受,rejected已拒绝');
            $table->timestamps();

            $table->foreign('rider_id')->references('id')->on('riders')->onDelete('cascade');
            $table->index('rider_id');
            $table->index('status');
        });

        Schema::create('route_optimization_tasks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('route_id')->comment('路径优化ID');
            $table->unsignedBigInteger('order_id')->comment('订单ID');
            $table->integer('sequence')->default(0)->comment('任务顺序');
            $table->timestamps();

            $table->foreign('route_id')->references('id')->on('route_optimizations')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('o2o_errand_orders')->onDelete('cascade');
            $table->unique(['route_id', 'order_id']);
            $table->index('route_id');
            $table->index('order_id');
        });
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('route_optimization_tasks');
        Schema::dropIfExists('route_optimizations');
    }
} 