<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_settings', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->index()->comment('用户ID');
            $table->char('distance', 8)->default('001')->nullable()->comment('配送距离偏好');
            $table->string('cids')->default('')->nullable()->comment('订单品类偏好');
            $table->boolean('around_push')->default(false)->comment('附近单推送');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_settings');
    }
};
