<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('logout_recs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->integer('platform')->default(1)->comment('平台 1--社区食堂 2--跑腿');
            $table->string('reason')->comment('原因');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('logout_recs');
    }
};
