<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_evaluate_recs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('帮我*订单ID');
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('rider_id')->index()->comment('骑手ID');
            $table->boolean('is_anonymous')->default(true)->comment('是否匿名');
            $table->boolean('is_satisfied')->default(false)->comment('是否满意');
            $table->string("reason", 2048)->nullable()->comment("原因");
            $table->string("remark", 2048)->nullable()->comment("备注");
            $table->text("imgs")->nullable()->comment("图片");
            $table->tinyInteger("pszs_star")->default(0)->comment("配送准时，星级");
            $table->tinyInteger("cdgf_star")->default(0)->comment("穿戴工服，星级");
            $table->tinyInteger("ybzj_star")->default(0)->comment("仪表整洁，星级");
            $table->tinyInteger("hpwh_star")->default(0)->comment("货品完好，星级");
            $table->tinyInteger("lmrq_star")->default(0)->comment("礼貌热情，星级");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_evaluate_recs');
    }
};
