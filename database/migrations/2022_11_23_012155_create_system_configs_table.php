<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_configs', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('platform')->default(1)->comment('平台 1--社区食堂 2--雨骑士');
            $table->string('name', 64)->index()->comment('参数值');
            $table->text('value')->comment('参数值');
            $table->boolean('is_json')->comment('是否是json');
            $table->string('last_update_user')->nullable()->comment('最后更新人');
            $table->string("remark")->comment("中文备注");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_configs');
    }
};
