<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->bigInteger('parent_id')->default(0)->nullable()->comment('上级ID')->index();
            $table->string('recommend_code')->default('')->nullable()->comment('邀请码')->index();
            $table->string('alipay_account')->default('')->nullable()->comment('支付宝账号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('parent_id');
            $table->dropColumn('recommend_code');
            $table->dropColumn('alipay_account');
        });
    }
};
