<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pricings', function (Blueprint $table) {
            // 雨骑士定价策略字段
            $table->text('normal_pricing')->nullable()->comment('正常时间+正常天气定价策略 JSON');
            $table->text('night_pricing')->nullable()->comment('夜间时间+正常天气定价策略 JSON');
            $table->text('weather_normal_pricing')->nullable()->comment('正常时间+恶劣天气定价策略 JSON');
            $table->text('weather_night_pricing')->nullable()->comment('夜间时间+恶劣天气定价策略 JSON');
            $table->boolean('use_yuqishi_pricing')->default(false)->comment('是否启用雨骑士定价策略');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pricings', function (Blueprint $table) {
            $table->dropColumn([
                'normal_pricing',
                'night_pricing', 
                'weather_normal_pricing',
                'weather_night_pricing',
                'use_yuqishi_pricing'
            ]);
        });
    }
}; 