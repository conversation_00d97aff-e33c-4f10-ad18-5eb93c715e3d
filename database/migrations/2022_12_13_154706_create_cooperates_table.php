<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cooperates', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('company')->default("")->comment('企业名称');
            $table->string("type")->default("")->comment("企业类型");
            $table->string("province")->default("")->comment("省");
            $table->string("city")->default("")->comment("市");
            $table->string("district")->default("")->comment("区");
            $table->string("address")->default("")->comment("地址");
            $table->string("name")->default("")->comment("负责人姓名");
            $table->string("phone")->default("")->comment("负责人电话");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cooperates');
    }
};
