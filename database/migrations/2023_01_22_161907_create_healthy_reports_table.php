<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('healthy_reports', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->index()->comment('用户Id');
            $table->decimal('temperature')->default(37.0)->comment('体温');
            $table->timestamp('check_time')->nullable()->comment('检测时间')->index();
            $table->string('check_img')->nullable()->comment('核酸证明');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('healthy_reports');
    }
};
