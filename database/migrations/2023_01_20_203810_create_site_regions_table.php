<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_regions', function (Blueprint $table) {
            $table->id();
            $table->integer('site_id')->index()->comment('站点ID');
            $table->string('region_name')->comment('区域名称')->index();
            $table->string('points', 1024)->comment('多边形顶点');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('site_regions');
    }
};
