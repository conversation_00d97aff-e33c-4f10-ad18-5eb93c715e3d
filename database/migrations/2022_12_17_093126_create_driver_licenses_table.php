<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('driver_licenses', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户');
            $table->string('cover')->comment('驾驶证正面');
            $table->string('obverse')->comment('驾驶证反面');
            $table->string('card_no')->comment('驾驶证号码');
            $table->string('name')->comment('姓名');
            $table->string('vehicle_type')->comment('准驾车型');
            $table->tinyInteger('exp_time_type')->default(1)->comment('时效类型');
            $table->timestamp('start_time')->nullable()->comment('有效起始日期');
            $table->timestamp('end_time')->nullable()->comment('有效期结束日期');
            $table->timestamp('create_time')->nullable()->comment('初次领证时间');
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('driver_licenses');
    }
};
