<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('versions', function (Blueprint $table) {
            $table->id();
            $table->string('version')->comment('版本号');
            $table->string('status')->comment('状态 0--未发布 1--审核中 2--已发布');
            $table->tinyInteger('platform')->comment('平台 1--社区食堂 2--跑腿');
            $table->tinyInteger('system')->comment('系统 1--android 2--ios');
            $table->tinyInteger('app_type')->default(1)->comment('应用类型 1--app 2--小程序 3--H5');
            $table->string('download_path')->nullable();
            $table->boolean('current')->default(0);
            $table->string('remark', 1024)->nullable()->comment('备注');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('versions');
    }
};
