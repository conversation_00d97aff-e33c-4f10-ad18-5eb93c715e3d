<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transport_tools', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户');
            $table->tinyInteger('type')->default(1)->comment('1--电动车 2--摩托车');
            $table->tinyInteger('card_type')->default(1)->comment('车牌类型 1--正式牌照 2--临时牌照');
            $table->string('card_no')->nullable()->default('')->comment('车牌号');
            $table->string('image')->nullable()->comment('侧身45度照片');
            $table->string('image2')->nullable()->comment('人车合照');
            $table->tinyInteger('status')->default(0)->comment('状态');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transport_tools');
    }
};
