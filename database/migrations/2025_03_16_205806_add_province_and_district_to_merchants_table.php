<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchants', function (Blueprint $table) {
            $table->string('province', 50)->after('password')->comment('省份');
            $table->string('district', 50)->after('city')->comment('区县');
            
            // 添加索引
            $table->index('province');
            $table->index('district');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchants', function (Blueprint $table) {
            $table->dropColumn(['province', 'district']);
            $table->dropIndex(['province', 'district']);
        });
    }
};
