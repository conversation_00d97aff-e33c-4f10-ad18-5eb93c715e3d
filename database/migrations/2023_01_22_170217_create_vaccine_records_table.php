<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vaccine_records', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->index()->comment('用户Id');
            $table->integer('vaccine_times')->default(0)->comment('接种次数');
            $table->timestamp('last_vaccine_time')->nullable()->comment('最近一针接种时间');
            $table->text('check_imgs')->nullable()->comment('接种记录');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vaccine_records');
    }
};
