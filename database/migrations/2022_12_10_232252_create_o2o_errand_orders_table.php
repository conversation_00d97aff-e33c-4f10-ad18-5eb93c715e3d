<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('o2o_errand_orders', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger("type")->default(0)->comment('跑腿订单类型 1--帮我送 2--帮我取 3--帮我买');
            $table->string('order_no', 32)->unique()->comment('跑腿订单号');
            $table->bigInteger('user_id')->default(0)->index()->comment('用户ID');
            $table->bigInteger('rider_id')->default(0)->index()->comment('骑手ID');
            $table->tinyInteger('order_status')->default(0)->comment('0-已取消 10-待付款 20-待接单 26-待取货 30-派送中 40-已完成');
            $table->integer('coupon_id')->default(0)->comment('优惠券ID');
            $table->integer('gratuity')->default(0)->comment('小费');
            $table->boolean('hide_address')->default(false)->comment('隐藏发货地址');
            $table->boolean('is_special')->default(false)->comment('是否专人配送');
            $table->boolean('need_incubator')->default(false)->comment('是否需要保温箱');
            $table->integer('reward')->default(0)->comment('打赏');
            $table->string('remark')->nullable()->comment('备注');

            $table->integer('distance')->default(0)->comment('距离，单位：米');
            $table->string('pickup_name')->nullable()->comment('取货点联系人');
            $table->string('pickup_phone', 16)->nullable()->index()->comment('取货点手机');
            $table->string('pickup_address_id')->default(0)->comment('取货点id');
            $table->string('pickup_address')->nullable()->comment('取货点地址');
            $table->integer('pickup_region_id')->default(0)->comment('取货点地区ID');
            $table->decimal('pickup_lng', 9, 6)->default(0)->comment('取货点经度');
            $table->decimal('pickup_lat', 9, 6)->default(0)->comment('取货点纬度');
            $table->string('deliver_name')->comment('送货点联系人');
            $table->string('deliver_phone', 16)->index()->comment('送货点手机');
            $table->string('deliver_address_id')->default(0)->comment('送货点id');
            $table->string('deliver_address')->comment('送货点地址');
            $table->integer('deliver_region_id')->default(0)->comment('送货点地区ID');
            $table->decimal('deliver_lng', 9, 6)->default(0)->comment('送货点经度');
            $table->decimal('deliver_lat', 9, 6)->default(0)->comment('送货点纬度');

            $table->string('receive_code', 10)->default('')->comment('收货码');
            $table->tinyInteger('receive_code_mode')->default(0)->comment('1-口头验证 2-输入验证');
            $table->string('pickup_code', 10)->default('')->comment('取货码');
            $table->tinyInteger('pickup_code_mode')->default(0)->comment('1-口头验证 2-输入验证');

            $table->string('goods_desc', 512)->nullable()->comment('商品描述');
            $table->text('goods_imgs')->nullable()->comment('商品图');
            $table->integer('goods_price')->default(0)->comment('预估商品价格');
            $table->integer('goods_protected_price')->default(0)->comment('报价费用');
            $table->integer('category_id')->default(0)->comment('分类ID');
            $table->integer('goods_category_id')->default(0)->comment('物品类型');
            $table->integer('weight')->default(0)->comment('重量');
            $table->string('volume')->nullable()->comment('体积：长*宽*高，每项单位cm');
            $table->text('buy_imgs')->nullable()->comment('购买凭证');

            $table->string('detail', 512)->nullable()->comment('跑腿订单详情');
            $table->string('close_reason')->nullable()->comment('取消原因');
            $table->boolean('is_trans')->default(false)->comment('是否转单');
            $table->integer('ori_rider_id')->default(0)->comment('转单前骑手');

            $table->integer('order_amount')->default(0)->comment('订单总费用');
            $table->integer('actual_amount')->default(0)->comment('实付金额');
            $table->integer('coupon_amount')->default(0)->comment('优惠金额');
            $table->integer('freight')->default(0)->comment('基础运费');
            $table->integer('distance_price')->default(0)->comment('距离附加费');
            $table->integer('time_price')->default(0)->comment('特殊时段费');
            $table->integer('weather_price')->default(0)->comment('恶劣天气费');
            $table->integer('weight_price')->default(0)->comment('重量附加费');

            $table->integer('pay_method')->default(0)->comment('支付方式 1--支付宝 2--微信 3-云闪付 4-余额支付');
            $table->string('payment_no')->nullable()->comment('支付流水号');
            $table->integer("refund_amount")->default(0)->comment("退款金额");
            $table->string('refund_status')->default(0)->comment('退款状态');
            $table->string('refund_no')->nullable()->comment('退款流水');
            $table->timestamp('refund_at')->nullable()->comment('退款时间');
            $table->timestamp('create_time')->nullable()->comment('下单时间');
            $table->timestamp('appointment_start_time')->nullable()->comment('预约时间');
            $table->timestamp('appointment_end_time')->nullable()->comment('预约时间');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('pickup_at')->nullable()->comment('取货时间');
            $table->timestamp('receipt_time')->nullable()->comment('接单时间');
            $table->timestamp('finish_time')->nullable()->comment('完成时间');
            $table->timestamp('estimated_delivery_time')->nullable()->comment('预计送达时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('o2o_errand_orders');
    }
};
