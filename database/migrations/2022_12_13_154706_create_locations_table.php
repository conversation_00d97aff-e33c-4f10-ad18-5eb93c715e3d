<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('belong_type', 20)->comment('所属用户类型');
            $table->bigInteger('belong_id')->index()->comment('所属用户ID');
            $table->decimal("lng", 9, 6)->default(0)->comment("经度");
            $table->decimal("lat", 9, 6)->default(0)->comment("纬度");
            $table->unsignedBigInteger("time")->default(0)->comment("插入时间戳");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('locations');
    }
};
