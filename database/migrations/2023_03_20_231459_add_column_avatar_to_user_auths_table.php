<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_auths', function (Blueprint $table) {
            $table->string('avatar')->nullable()->default('')->comment('三方头像')->after('union_id');
            $table->string('nickname')->nullable()->default('')->comment('三方昵称')->after('avatar');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_auths', function (Blueprint $table) {
            $table->dropColumn('avatar');
            $table->dropColumn('nickname');
        });
    }
};
