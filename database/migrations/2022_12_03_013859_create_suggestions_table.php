<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('suggestions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->tinyInteger('suggestion_cid')->default(1)->comment('类型');
            $table->string('content')->comment('内容');
            $table->integer('platform')->default(1)->comment('平台 1--社区食堂 2--跑腿 3--跑男');
            $table->string('phone')->default('')->comment('联系电话');
            $table->string('contact')->default('')->comment('联系人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('suggestions');
    }
};
