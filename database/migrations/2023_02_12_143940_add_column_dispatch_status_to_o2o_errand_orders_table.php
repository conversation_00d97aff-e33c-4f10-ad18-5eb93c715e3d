<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->tinyInteger('dispatch_status')->default(0)
                ->comment('0--未派单 1--派单等待 2--派单成功 3--派单失败')->after('order_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->dropColumn('dispatch_status');
        });
    }
};
