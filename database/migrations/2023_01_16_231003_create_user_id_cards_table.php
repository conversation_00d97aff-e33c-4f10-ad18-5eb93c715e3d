<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_id_cards', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->unique()->comment('用户ID');
            $table->string('id_card_image')->comment('身份证正面');
            $table->string('id_card_image_over')->comment('身份证反面');
            $table->string('name')->nullable()->comment('姓名');
            $table->string('nation')->nullable()->comment('名族');
            $table->string('address')->nullable()->comment('地址');
            $table->string('id_card')->index()->comment('身份证号');
            $table->string('issuing_authority')->nullable()->comment('签发机关');
            $table->string('sex')->nullable()->comment('性别');
            $table->date('birth')->nullable()->comment('身份证上出生日期');
            $table->date('issuing_date')->nullable()->comment('签发日期');
            $table->date('expiry_date')->nullable()->comment('有效日期');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_id_cards');
    }
};
