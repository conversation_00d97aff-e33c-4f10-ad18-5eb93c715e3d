<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('shop_id')->index()->comment('店铺ID');
            $table->string('order_no')->unique()->comment('订单号');
            $table->integer('order_amount')->comment('订单金额 单位分');
            $table->integer('reduce_amount')->comment('优惠金额 单位分');
            $table->integer('pay_amount')->comment('实付金额 单位分');
            $table->boolean('closed')->default(false);
            $table->integer('pay_method')->default(0)->comment('支付方式 1--支付宝 2--微信 3-云闪付');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->string('payment_no')->default('')->comment('支付流水号');
            $table->string('receiver_name')->comment("省-市-区");
            $table->string('receiver_tel')->comment("省-市-区");
            $table->string('receiver_area')->comment("省-市-区");
            $table->string('receiver_address')->comment("省市区详细地址");
            $table->timestamp("sending_start_time")->comment("配送开始时间");
            $table->timestamp("sending_end_time")->comment("配送结束时间");
            $table->text("remark")->nullable()->comment("订单备注");
            $table->boolean("is_tel_protect")->default(true)->comment("号码保护");
            $table->tinyInteger("refund_type")->default(0)->comment("退款类型：0-默认，1-售后，-1-取消订单");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
