<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifies', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();

            $table->unsignedTinyInteger("type")->default(0)->comment("类型：1-通知，2-活动")->index();
            $table->string('title')->comment('标题');
            $table->string('cover', 500)->nullable()->comment("图片");
            $table->longText('content')->comment('文章内容');
            $table->boolean("is_important")->default(false)->comment("是否重要");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifies');
    }
};
