<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->timestamp('arrive_at')->nullable()->comment('到达取货点时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->dropColumn('arrive_at');
        });
    }
};
