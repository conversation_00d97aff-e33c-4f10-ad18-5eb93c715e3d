<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->timestamp('settle_time')->nullable()->index()->comment('结算日期');
            $table->integer('reward_amount_part')->default(0)->comment('兼职骑手收入，不含小费纯订单收益');
            $table->integer('reward_amount_full')->default(0)->comment('全职骑手收入，不含小费纯订单收益');
            $table->integer('site_id')->default(0)->comment('站点ID');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->dropColumn('settle_time');
            $table->dropColumn('reward_amount_part');
            $table->dropColumn('reward_amount_full');
            $table->dropColumn('site_id');
        });
    }
};
