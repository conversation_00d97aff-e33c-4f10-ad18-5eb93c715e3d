<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge_orders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('order_no')->unique()->default('')->comment('订单号');
            $table->integer('order_amount')->comment('订单金额 单位分');
            $table->integer('actual_amount')->comment('实付金额 单位分');
            $table->integer('reduce_amount')->comment('优惠金额 单位分');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->boolean('closed')->default(false);
            $table->string('refund_status')->comment('退款状态');
            $table->string('refund_no')->nullable()->default('')->comment('退款流水');
            $table->timestamp('refund_at')->nullable()->comment('退款时间');
            $table->integer('pay_method')->nullable()->comment('支付方式 1--支付宝 2--微信 3-云闪付');
            $table->string('payment_no')->nullable()->default('')->comment('支付流水号');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge_orders');
    }
};
