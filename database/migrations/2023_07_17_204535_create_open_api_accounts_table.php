<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('open_api_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('账号名称');
            $table->char('app_key', 6)->comment('app key')->unique();
            $table->char('app_secret')->comment('app secret');
            $table->string('callback_url')->nullable()->comment('回调地址');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('open_api_accounts');
    }
};
