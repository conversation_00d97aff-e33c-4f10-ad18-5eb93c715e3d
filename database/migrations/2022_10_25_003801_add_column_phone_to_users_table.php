<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('email');
            $table->dropColumn('email_verified_at');
            $table->dropColumn('remember_token');
            $table->char('phone', 11)->unique()->comment('手机号')->after('id');
            $table->string('nickname')->comment('昵称')->default('')->nullable()->after('phone');
            $table->string('avatar')->nullable()->default('')->after('nickname');
            $table->tinyInteger('level')->default(1)->comment('用户等级');
            $table->boolean('certified')->default(false)->comment('是否认证')->after('password');
            $table->string('id_card')->comment('身份证')->default('')->nullable();
            $table->date('birthday')->nullable()->comment('生日');
            $table->string('channel')->comment('注册渠道');
            $table->string('scene')->comment('注册场景 1--APP 2--小程序');
            $table->ipAddress('ip')->comment('注册IP');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};
