<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('regions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->index()->comment('城市名称');
            $table->bigInteger('pid')->default(0)->comment('上级ID');
            $table->tinyInteger('level')->comment('层级');
            $table->char('code', 16)->unique()->comment('城市编码');
            $table->decimal("lng", 9, 6)->default(0)->comment("经度");
            $table->decimal("lat", 9, 6)->default(0)->comment("纬度");
            $table->boolean('is_hot')->default(0)->comment('是否热门');
            $table->char('letter')->index()->comment('首字母');
            $table->string('pinyin')->comment('拼音');
            $table->boolean('status')->default(1)->comment('1--上架 0--下架');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('regions');
    }
};
