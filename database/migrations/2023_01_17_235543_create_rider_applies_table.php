<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_applies', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->unique()->comment('用户ID');
            $table->tinyInteger('status')->default(0)->comment('0--申请中 1--申请通过 2--申请失败');
            $table->string('remark');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_applies');
    }
};
