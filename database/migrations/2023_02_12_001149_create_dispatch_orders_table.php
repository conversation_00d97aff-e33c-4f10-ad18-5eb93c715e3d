<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dispatch_orders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->comment('配送订单ID');
            $table->tinyInteger('status')->default(0)->comment('派单状态 0--待确认 1--接受 2--拒绝');
            $table->boolean('closed')->default(0)->comment('是否自动关闭');
            $table->integer('rider_id')->comment('骑手Id');
            $table->string('remark')->nullable()->comment('备注');
            $table->integer('operator_id')->nullable()->comment('操作员ID');
            $table->string('operator_name')->comment('操作员名称');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dispatch_orders');
    }
};
