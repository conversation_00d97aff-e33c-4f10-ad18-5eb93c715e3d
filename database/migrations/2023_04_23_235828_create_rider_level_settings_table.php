<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_level_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name')->default('')->comment('等级名称');
            $table->integer('level')->default(1)->comment('等级');
            $table->integer('max_score')->default(0)->comment('升级门槛分数');
            $table->string('card_background')->default('')->comment('卡片背景');
            $table->integer('order_limit')->default(5)->comment('接单限制');
            $table->integer('trans_limit')->default(2)->comment('转单限制');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_level_settings');
    }
};
