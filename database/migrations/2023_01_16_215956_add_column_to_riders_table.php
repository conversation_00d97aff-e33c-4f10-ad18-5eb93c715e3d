<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('riders', function (Blueprint $table) {
            //
            $table->boolean('id_card_certified')->default(false)->comment('身份证认证');
            $table->boolean('face_certified')->default(false)->comment('人脸认证');
            $table->timestamp('last_check_time')->nullable()->comment('上次检测时间');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('riders', function (Blueprint $table) {
            $table->dropColumn('id_card_certified');
            $table->dropColumn('face_certified');
            $table->dropColumn('last_check_time');
        });
    }
};
