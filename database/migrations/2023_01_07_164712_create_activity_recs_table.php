<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity_recs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('activity_id')->index()->comment('邀请活动ID');
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('child_id')->nullable()->default(0)->comment('邀请人ID');
            $table->integer('pre_reward_amount')->nullable()->default(0)->comment('预估奖励金额');
            $table->integer('reward_amount')->nullable()->default(0)->comment('实际奖励金额');
            $table->integer('order_limit')->default(0)->comment('订单要求');
            $table->boolean('status')->default(0)->comment('状态 0-已注册 1-已完成');
            $table->timestamp('settle_time')->nullable()->comment('结算时间');
            $table->tinyInteger('settle_status')->default(0)->comment('结算状态 0--未结算 1--已结算 2--取消结算 3--退款不结算');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activity_recs');
    }
};
