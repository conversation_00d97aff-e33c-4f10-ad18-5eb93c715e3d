<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_account_flows', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('user_account_id')->index()->comment('用户账号ID');
            $table->integer('chg_amount')->default(0)->comment('变更金额');
            $table->integer('before_amount')->default(0)->comment('变化前金额');
            $table->integer('after_amount')->default(0)->comment('变化后金额');
            $table->char('type', 8)->default('in')->comment('交易类型')->index();
            $table->integer('business_type')->default(1)->comment('业务类型');
            $table->string('no', 64)->index()->comment('流水号');
            $table->string('remark')->comment('备注')->nullable()->default('');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_account_flows');
    }
};
