<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('active_shequ')->default(0)->comment('社区食堂激活');
            $table->boolean('active_paotui')->default(0)->comment('跑腿激活');
            $table->timestamp('last_active_time')->nullable()->comment('最近活跃时间');
            $table->string('device_id', 64)->nullable()->comment('设备号');
            $table->string('device_type')->nullable()->comment('设备号类型');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('active_shequ');
            $table->dropColumn('active_paotui');
            $table->dropColumn('last_active_time');
            $table->dropColumn('device_id');
            $table->dropColumn('device_type');
        });
    }
};
