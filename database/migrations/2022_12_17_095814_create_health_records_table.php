<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('health_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户');
            $table->tinyInteger('type')->default(1)->comment('1-核酸证明 2-疫苗记录');
            $table->decimal('temperature', 10, 2)->nullable()->comment('体温');
            $table->tinyInteger('ym_count')->nullable()->comment('疫苗接种次数');
            $table->string('images', 512)->nullable()->default('')->comment('图片');
            $table->timestamp('check_time')->nullable()->comment('检测时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('health_records');
    }
};
