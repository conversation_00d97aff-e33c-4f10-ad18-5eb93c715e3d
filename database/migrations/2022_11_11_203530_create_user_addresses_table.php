<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_addresses', function (Blueprint $table) {
            $table->id();
            $table->string('name', 63)->comment('收货人姓名');
            $table->bigInteger('user_id')->index()->comment('用户表的用户ID');
            $table->string('province')->comment('省');
            $table->string('city')->comment('市');
            $table->string('county')->comment('区');
            $table->string('address_detail')->comment('详细收货地址');
            $table->char('area_code', 6)->comment('地区编码');
            $table->string('tel', 20)->comment('手机号');
            $table->boolean('is_default')->default(0)->comment('是否默认');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_addresses');
    }
};
