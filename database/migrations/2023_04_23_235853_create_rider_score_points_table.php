<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_score_points', function (Blueprint $table) {
            $table->id();
            $table->string('point_key')->comment('点位');
            $table->string('name')->comment('名称');
            $table->tinyInteger('point_type')->default(1)->comment('类型 1-周期性 2-触发类');
            $table->integer('score');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_score_points');
    }
};
