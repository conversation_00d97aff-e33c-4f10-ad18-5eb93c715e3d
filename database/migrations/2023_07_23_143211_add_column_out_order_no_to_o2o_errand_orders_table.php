<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->string('out_order_no')->nullable()->index()->comment('外部订单号')->after('id');
            $table->string('app_key')->nullable()->index()->comment('外部应用')->after('out_order_no');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->dropColumn('out_order_no');
            $table->dropColumn('app_key');
        });
    }
};
