<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_spus', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('shop_id')->index()->comment('店铺ID');
            $table->bigInteger('cat_id')->nullable()->default(0)->index()->comment('类目ID');
            $table->string('name')->comment('名称');
            $table->string('cover', 500)->nullable()->comment("头图");
            $table->string("description", 1000)->nullable()->default("")->comment("介绍");
            $table->decimal("price")->default(0)->comment("原价，单位：元");
            $table->decimal("discount_price")->default(0)->comment("优惠价，单位：元");
            $table->string("sell_tags")->nullable()->default("")->comment("售卖标签");
            $table->integer("stock")->default(0)->comment("库存");
            $table->integer("sales")->nullable()->default(0)->comment("销量");
            $table->boolean('is_signboard')->default(false)->comment('是否是招牌');
            $table->unsignedInteger("sort")->default(0)->comment("排序，数字越小越靠前");
            $table->tinyInteger('status')->default(0)->comment('状态：0-待上架，1-上架，-1-下架');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_spus');
    }
};
