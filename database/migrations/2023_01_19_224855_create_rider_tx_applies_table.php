<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_tx_applies', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('order_no')->unique()->default('')->comment('流水号');
            $table->integer('amount')->default(0)->comment('提现金额');
            $table->tinyInteger('status')->default(0)->comment('0-待处理 1-审核通过 2-审核拒绝');
            $table->tinyInteger('trans_status')->default(0)->comment('0-待打款 1-打款成功 2-打款失败');
            $table->string('remark')->nullable()->comment('备注');
            $table->string('trans_message')->nullable()->comment('打款信息');
            $table->tinyInteger('account_type')->default(1)->comment('1-支付宝 2-微信 3-银行卡');
            $table->string('bank_name')->nullable()->comment('收款银行');
            $table->string('bank_account')->nullable()->comment('收款账号');
            $table->string('real_name')->nullable()->comment('收款人');
            $table->timestamp('check_time')->nullable()->comment('审核时间');
            $table->timestamp('transfer_time')->nullable()->comment('转账时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_tx_applies');
    }
};
