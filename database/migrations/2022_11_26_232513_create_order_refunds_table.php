<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_refunds', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index()->comment('订单ID');
            $table->string('no')->unique()->comment('退款单号');
            $table->string("refund_reason")->comment("退款原因");
            $table->text("refund_remark")->nullable()->comment("退款备注");
            $table->text("refund_pics")->nullable()->comment("退款凭证");
            $table->text("spu_info")->nullable()->comment('商品信息[{"spu_id":1,"quantity":1}]');
            $table->integer('refund_amount')->comment('退款金额 单位分');
            $table->integer('refund_status')->default(0)->comment('退款状态');
            $table->timestamp('verify_at')->nullable()->comment('审核时间');
            $table->string('reject_reason')->nullable()->comment('审核拒绝原因');
            $table->timestamp('refund_at')->nullable()->comment('退款时间');
            $table->string('refund_no')->nullable()->comment('退款流水');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_refunds');
    }
};
