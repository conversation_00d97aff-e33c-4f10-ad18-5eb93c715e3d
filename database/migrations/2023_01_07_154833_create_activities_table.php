<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('活动名称');
            $table->string('image')->default('')->nullable()->comment('活动图片');
            $table->boolean('status')->comment('活动状态 1-生效 0-失效');
            $table->tinyInteger('type')->comment('活动类型 1--用户拉新 2--骑手拉新 3--用户订单奖励 4--骑手订单奖励');
            $table->timestamp('start_time')->nullable()->comment('活动开始时间');
            $table->timestamp('end_time')->nullable()->comment('活动结束时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invite_activities');
    }
};
