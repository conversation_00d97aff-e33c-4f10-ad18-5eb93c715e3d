<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_account_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('merchant_id')->constrained('merchants')->onDelete('cascade');
            $table->integer('amount')->comment('变动金额(分)');
            $table->integer('before_balance')->comment('变动前余额(分)');
            $table->integer('after_balance')->comment('变动后余额(分)');
            $table->string('type')->comment('变动类型:recharge-充值,order-订单支付,refund-退款');
            $table->string('order_no')->nullable()->comment('关联订单号');
            $table->string('remark')->nullable()->comment('备注说明');
            $table->timestamps();
            
            $table->index(['merchant_id', 'created_at']);
            $table->index('order_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_account_logs');
    }
};
