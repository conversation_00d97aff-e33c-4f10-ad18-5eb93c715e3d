<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('suggestion_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('platform')->default(1)->comment('平台 1--社区食堂 2--跑腿 3--跑男');
            $table->integer('view_order')->default(1)->comment('排序');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('suggestion_categories');
    }
};
