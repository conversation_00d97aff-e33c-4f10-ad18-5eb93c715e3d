<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trans_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_no')->index()->comment('订单号');
            $table->integer('trans_rider_id')->index()->comment('发起人');
            $table->string('reason')->comment('原因');
            $table->tinyInteger('status')->default(1)->comment('状态 1-待接单 2--已接单 3--没人接单');
            $table->integer('target_rider_id')->index()->comment('接受人');
            $table->timestamp('receive_time')->nullable()->comment('接受时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trans_orders');
    }
};
