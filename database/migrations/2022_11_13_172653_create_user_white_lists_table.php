<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_white_lists', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('收货人姓名');
            $table->bigInteger('user_id')->default(0)->nullable()->comment('用户ID');
            $table->string('tel', 20)->comment('手机号')->unique();
            $table->string('province')->comment('省');
            $table->string('city')->comment('市');
            $table->string('county')->comment('区');
            $table->string('address_detail')->comment('详细地址');
            $table->boolean('active')->default(0)->comment('是否激活');
            $table->date('sign_date')->nullable()->comment('登记日期');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_white_lists');
    }
};
