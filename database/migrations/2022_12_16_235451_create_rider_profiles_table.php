<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rider_profiles', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->unique()->comment('用户ID');
            $table->tinyInteger('transport_type')->default(0)->comment('交通工具');
            $table->tinyInteger('edu_background')->default(0)->comment('学历');
            $table->string('address')->default('')->comment('现居住地');
            $table->string('job')->default('')->comment('工作');
            $table->boolean('marriage')->nullable()->comment('婚姻情况');
            $table->tinyInteger('children_count')->default(0)->comment('子女数量');
            $table->string('health_cert_no')->nullable()->comment('健康证号码');
            $table->string('health_cert_publisher')->nullable()->comment('发证单位');
            $table->timestamp('health_cert_expire_date')->nullable()->comment('健康证到期时间');
            $table->string('health_cert_cover')->nullable()->comment('健康证正面');
            $table->string('health_cert_obverse')->nullable()->comment('健康证反面');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rider_profiles');
    }
};
