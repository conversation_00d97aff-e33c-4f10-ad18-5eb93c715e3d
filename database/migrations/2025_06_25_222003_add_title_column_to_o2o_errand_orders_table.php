<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->string('title')->nullable()->default('')->comment('订单标题，麦芽田订单存储商家SN')->after('app_key');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('o2o_errand_orders', function (Blueprint $table) {
            $table->dropColumn('title');
        });
    }
};
