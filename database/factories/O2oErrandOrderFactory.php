<?php

namespace Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\O2oErrandOrder>
 */
class O2oErrandOrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'type' => $this->faker->numberBetween(1,3),
            'user_id' => 17,
            'rider_id' => 1,
            'distance' => $this->faker->randomNumber(4),
            'pickup_name' => $this->faker->name,
            'pickup_address' => $this->faker->address,
            'pickup_phone' => $this->faker->phoneNumber,
            'pickup_lng' => $this->faker->longitude,
            'pickup_lat' => $this->faker->latitude,
            'deliver_name' => $this->faker->name,
            'deliver_address' => $this->faker->address,
            'deliver_phone' => $this->faker->phoneNumber,
            'deliver_lng' => $this->faker->longitude,
            'deliver_lat' => $this->faker->latitude,
            'detail' => $this->faker->text(),
            'order_status' => 40,
            'receive_code' => $this->faker->randomNumber(4),
            'pickup_code' => $this->faker->randomNumber(4),
            'pickup_code_mode' => 2,
            'order_amount' => $this->faker->randomNumber(4, true),
            'actual_amount' => $this->faker->randomNumber(4, true),
            'coupon_amount' => 0,
            'coupon_id' => 0,
            'freight' => 8,
            'weight_price' => 0,
            'time_price' => 0,
            'goods_price' => 0,
            'goods_protected_price' => 0,
            'goods_category_id' => 0,
            'category_id' => 0,
            'gratuity' => 0,
            'weight' => 10,
            'remark' => $this->faker->text(20),
            'deliver_region_id' => 0,
            'pickup_region_id' => 0,
            'pay_method' => 1,
            'payment_no' => '111111',
            'hide_address' => $this->faker->boolean,
            'close_reason' => '',
            'refund_status' => 0,
            'refund_at' => null,
            'is_trans' => 0,
            'ori_rider_id' => 0,
            'create_time' => Carbon::now(),
            'appointment_time' => Carbon::now()->addHours(1),
            'paid_at' => Carbon::now()->addSeconds(15),
            'pickup_at' => Carbon::now()->addMinutes(30),
            'receipt_time' => Carbon::now()->addMinutes(5),
            'finish_time' => Carbon::now()->addHour(),
            'estimated_delivery_time' => Carbon::now()->addMinutes(50)
        ];
    }
}
