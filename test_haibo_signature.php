<?php

/**
 * 海博签名测试脚本
 * 用于验证签名算法是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

// 模拟海博签名算法
function generateHaiboSignature(array $params, string $secret): string
{
    // 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
    $filteredParams = [];
    foreach ($params as $key => $value) {
        // 排除 sign 参数和空值参数
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }

        // 数组和对象转为JSON字符串
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }

        // 布尔值转换
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }

        $filteredParams[$key] = $value;
    }

    // 按参数名字典顺序排序
    ksort($filteredParams);

    // 2. 将参数以参数1值1参数2值2...的顺序拼接
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }

    // 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
    $signString = $secret . $signString;

    echo "签名计算过程:\n";
    echo "过滤后的参数: " . json_encode($filteredParams, JSON_UNESCAPED_UNICODE) . "\n";
    echo "参数拼接字符串: " . substr($signString, strlen($secret)) . "\n";
    echo "完整签名字符串: " . $signString . "\n";

    // 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
    $signature = strtolower(sha1($signString));
    echo "计算出的签名: " . $signature . "\n";

    return $signature;
}

// 测试用例1：海博官方文档示例
echo "=== 测试用例1：海博官方文档示例 ===\n";
$testParams1 = [
    'developerId' => 'test',
    'timestamp' => 1477395862,
    'version' => '1.0',
    'number' => 123,
    'string' => '测试',
    'double' => 123.123,
    'boolean' => true,
    'empty' => '', // 空值会被过滤掉
];
$secret1 = 'test';
$expectedSign1 = '8943ba698f4b009f80dc2fd69ff9b313381263bd';

$generatedSign1 = generateHaiboSignature($testParams1, $secret1);
echo "期望签名: " . $expectedSign1 . "\n";
echo "结果: " . ($generatedSign1 === $expectedSign1 ? "✅ 通过" : "❌ 失败") . "\n\n";

// 测试用例2：回调接口示例
echo "=== 测试用例2：回调接口示例 ===\n";
$testParams2 = [
    'developerId' => 'test_dev_id',
    'timestamp' => time(),
    'version' => '1.0',
    'orderId' => 'HB_ORDER_123456',
    'carrierDeliveryId' => 'LOCAL_ORDER_789',
    'status' => 20,
    'operateTime' => time(),
    'riderName' => '张三',
    'riderPhone' => '13800138000',
    'longitude' => 116.397128,
    'latitude' => 39.916527,
];
$secret2 = 'test_secret';

$generatedSign2 = generateHaiboSignature($testParams2, $secret2);
echo "生成的签名: " . $generatedSign2 . "\n";

// 验证签名是否一致（使用相同参数再次生成）
$verifySign2 = generateHaiboSignature($testParams2, $secret2);
echo "验证签名: " . $verifySign2 . "\n";
echo "结果: " . ($generatedSign2 === $verifySign2 ? "✅ 签名一致" : "❌ 签名不一致") . "\n\n";

echo "=== 测试完成 ===\n";
